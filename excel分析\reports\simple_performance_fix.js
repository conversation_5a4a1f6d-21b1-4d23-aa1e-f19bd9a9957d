/**
 * 简化的性能修复脚本
 * 直接修复表格结构问题，确保性能优化器能正常工作
 * 
 * @version 1.0.0
 * @date 2025-07-19
 */

(function() {
    'use strict';
    
    console.log('🚀 简化性能修复脚本启动...');
    
    /**
     * 修复表格结构
     */
    function fixTableStructure() {
        console.log('🔧 开始修复表格结构...');
        
        // 1. 查找表格
        let table = document.querySelector('table.dataframe');
        if (!table) {
            table = document.querySelector('table');
        }
        
        if (!table) {
            console.error('❌ 未找到任何表格元素');
            return false;
        }
        
        console.log('📋 找到表格:', table.className);
        
        // 2. 修复重复的类名
        if (table.className.includes('dataframe dataframe')) {
            table.className = table.className.replace('dataframe dataframe', 'dataframe');
            console.log('✅ 修复了重复的类名');
        }
        
        // 3. 确保有tbody结构
        let tbody = table.querySelector('tbody');
        if (!tbody) {
            console.log('🔄 创建tbody结构...');
            
            tbody = document.createElement('tbody');
            const rows = Array.from(table.querySelectorAll('tr'));
            const thead = table.querySelector('thead');
            const headerRowCount = thead ? thead.querySelectorAll('tr').length : 0;
            
            console.log(`📊 总行数: ${rows.length}, 表头行数: ${headerRowCount}`);
            
            // 移动数据行到tbody
            for (let i = headerRowCount; i < rows.length; i++) {
                tbody.appendChild(rows[i]);
            }
            
            table.appendChild(tbody);
            console.log(`✅ 创建tbody并移动了 ${rows.length - headerRowCount} 行数据`);
        } else {
            console.log('✅ 表格已有tbody结构');
        }
        
        // 4. 验证最终结构
        const finalDataRows = tbody.querySelectorAll('tr');
        console.log(`📋 最终数据行数: ${finalDataRows.length}`);
        
        return true;
    }
    
    /**
     * 强制加载性能优化器
     */
    function forceLoadOptimizer() {
        console.log('🔄 强制加载性能优化器...');

        // 检查是否已经加载
        if (window.performanceOptimizer) {
            console.log('✅ 性能优化器已存在，重新初始化...');
            try {
                window.performanceOptimizer.setupOptimization();
                return true;
            } catch (error) {
                console.error('❌ 重新初始化失败:', error);
            }
        }

        // 如果没有加载，尝试手动创建
        console.log('🔧 手动创建性能优化器...');

        try {
            // 检查PerformanceOptimizer类是否存在
            if (typeof PerformanceOptimizer !== 'undefined') {
                window.performanceOptimizer = new PerformanceOptimizer({
                    virtualScrolling: true,
                    visibleRowCount: 15,
                    batchSize: 30,
                    enablePerfMonitor: true
                });
                console.log('✅ 手动创建性能优化器成功');
                return true;
            } else {
                console.error('❌ PerformanceOptimizer类未定义');
                return false;
            }
        } catch (error) {
            console.error('❌ 手动创建性能优化器失败:', error);
            return false;
        }
    }

    /**
     * 重新初始化性能优化器
     */
    function reinitializeOptimizer() {
        console.log('⚡ 重新初始化性能优化器...');

        // 首先尝试强制加载
        const loaded = forceLoadOptimizer();

        if (loaded && window.performanceOptimizer) {
            try {
                // 重新设置优化
                window.performanceOptimizer.setupOptimization();
                console.log('✅ 性能优化器重新初始化成功');
                return true;
            } catch (error) {
                console.error('❌ 性能优化器重新初始化失败:', error);
                return false;
            }
        } else {
            console.log('⚠️ 性能优化器加载失败');
            return false;
        }
    }
    
    /**
     * 重新初始化分页管理器
     */
    function reinitializePagination() {
        console.log('📄 重新初始化分页管理器...');
        
        if (typeof initializePaginationManager === 'function') {
            try {
                initializePaginationManager();
                console.log('✅ 分页管理器重新初始化成功');
                return true;
            } catch (error) {
                console.error('❌ 分页管理器重新初始化失败:', error);
                return false;
            }
        } else {
            console.log('⚠️ 分页管理器初始化函数未找到');
            return false;
        }
    }
    
    /**
     * 运行完整修复
     */
    function runCompleteFix() {
        console.log('🎯 开始运行完整修复...');
        
        const results = {
            tableFixed: false,
            optimizerFixed: false,
            paginationFixed: false
        };
        
        // 1. 修复表格结构
        results.tableFixed = fixTableStructure();
        
        // 2. 等待一下再重新初始化其他组件
        setTimeout(() => {
            results.optimizerFixed = reinitializeOptimizer();
            results.paginationFixed = reinitializePagination();
            
            // 输出修复结果
            console.log('🎉 修复完成，结果:', results);
            
            // 显示修复结果
            showFixResults(results);
            
        }, 500);
    }
    
    /**
     * 显示修复结果
     */
    function showFixResults(results) {
        // 创建结果显示
        const resultDiv = document.createElement('div');
        resultDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 10001;
            font-family: Arial, sans-serif;
            max-width: 400px;
        `;
        
        resultDiv.innerHTML = `
            <h3 style="margin: 0 0 15px 0; color: #4CAF50;">🔧 性能修复结果</h3>
            <div style="margin: 8px 0;">
                表格结构修复: ${results.tableFixed ? '✅ 成功' : '❌ 失败'}
            </div>
            <div style="margin: 8px 0;">
                性能优化器: ${results.optimizerFixed ? '✅ 成功' : '❌ 失败'}
            </div>
            <div style="margin: 8px 0;">
                分页管理器: ${results.paginationFixed ? '✅ 成功' : '❌ 失败'}
            </div>
            <div style="margin: 15px 0 10px 0; padding: 10px; background: #f0f8ff; border-radius: 4px; font-size: 12px;">
                <strong>建议操作:</strong><br>
                1. 按F12打开开发者工具查看详细日志<br>
                2. 刷新页面验证修复效果<br>
                3. 测试表格滚动和分页功能
            </div>
            <button onclick="this.parentElement.remove()" 
                    style="background: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                关闭
            </button>
        `;
        
        document.body.appendChild(resultDiv);
        
        // 3秒后自动关闭
        setTimeout(() => {
            if (resultDiv.parentElement) {
                resultDiv.remove();
            }
        }, 8000);
    }
    
    /**
     * 检查脚本加载状态
     */
    function checkScriptLoadStatus() {
        console.log('🔍 检查脚本加载状态:');

        const scripts = {
            'PerformanceOptimizer': typeof PerformanceOptimizer !== 'undefined',
            'DatePaginationManager': typeof DatePaginationManager !== 'undefined',
            'makeTableSortable': typeof makeTableSortable !== 'undefined',
            'initializeKlineChart': typeof initializeKlineChart !== 'undefined',
            'EnhancedLayoutManager': typeof EnhancedLayoutManager !== 'undefined'
        };

        const instances = {
            'window.performanceOptimizer': !!window.performanceOptimizer,
            'window.globalDatePaginationManager': !!window.globalDatePaginationManager,
            'window.layoutManager': !!window.layoutManager
        };

        console.log('  脚本类定义:', scripts);
        console.log('  实例对象:', instances);

        return { scripts, instances };
    }

    /**
     * 检查页面状态
     */
    function checkPageStatus() {
        const table = document.querySelector('table.dataframe') || document.querySelector('table');
        const tbody = table ? table.querySelector('tbody') : null;
        const optimizer = window.performanceOptimizer;
        const pagination = window.globalDatePaginationManager;

        console.log('📊 页面状态检查:');
        console.log('  表格存在:', !!table);
        console.log('  tbody存在:', !!tbody);
        console.log('  数据行数:', tbody ? tbody.querySelectorAll('tr').length : 0);
        console.log('  性能优化器:', !!optimizer);
        console.log('  分页管理器:', !!pagination);

        // 检查脚本加载状态
        checkScriptLoadStatus();

        return {
            hasTable: !!table,
            hasTbody: !!tbody,
            dataRows: tbody ? tbody.querySelectorAll('tr').length : 0,
            hasOptimizer: !!optimizer,
            hasPagination: !!pagination
        };
    }
    
    // 暴露全局函数
    window.simplePerformanceFix = {
        runCompleteFix,
        checkPageStatus,
        fixTableStructure,
        reinitializeOptimizer,
        reinitializePagination
    };
    
    // 页面加载完成后自动运行
    function autoFix() {
        console.log('🔄 自动修复启动...');
        
        // 检查当前状态
        const status = checkPageStatus();
        
        // 如果表格存在但没有tbody，或者优化器/分页器未加载，则运行修复
        if (status.hasTable && (!status.hasTbody || !status.hasOptimizer || !status.hasPagination)) {
            console.log('🎯 检测到需要修复的问题，开始自动修复...');
            runCompleteFix();
        } else {
            console.log('✅ 页面状态正常，无需修复');
        }
    }
    
    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(autoFix, 1000);
        });
    } else {
        setTimeout(autoFix, 1000);
    }
    
    console.log('✅ 简化性能修复脚本加载完成');
    
})();
