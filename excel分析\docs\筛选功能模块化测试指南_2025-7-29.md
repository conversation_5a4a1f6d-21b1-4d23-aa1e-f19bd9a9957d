# 筛选功能模块化测试指南

## 测试概述

本指南用于验证筛选功能模块化重构的效果。通过对比测试页面和原版页面，确保模块化版本的功能完整性和性能表现。

## 测试环境

### 文件结构
```
excel分析/reports/
├── 复盘分析_精简版.html          # 原版页面（生产环境）
├── 筛选功能测试页.html            # 测试页面（模块化版本）
├── filter-engine.js              # 筛选引擎模块
├── table_sorter.js               # 表格排序模块
└── data/                          # 数据文件
    ├── index.json
    └── date_*.json
```

### 启动测试
```bash
cd excel分析
python 启动监控服务器.py
```

**访问地址：**
- 原版页面：http://localhost:8000/复盘分析_精简版.html
- 测试页面：http://localhost:8000/筛选功能测试页.html

## 功能对比测试

### 1. 基础筛选功能测试

#### 测试用例1.1：数值比较筛选
**测试条件：**
```
涨幅% > 5
量比 >= 2
成交量 < 1000000
```

**验证步骤：**
1. 在原版页面输入条件并应用
2. 记录筛选结果数量和执行时间
3. 在测试页面输入相同条件
4. 对比结果数量和性能

**预期结果：**
- 筛选结果数量完全一致
- 性能差异在10%以内

#### 测试用例1.2：文本筛选
**测试条件：**
```
名称 = "平安银行"
代码 != "000001"
```

**验证要点：**
- 文本匹配准确性
- 特殊字符处理
- 大小写敏感性

### 2. 排序筛选功能测试

#### 测试用例2.1：基础排序筛选
**测试条件：**
```
振幅 倒序前10
成交量 正序前20
涨幅% 倒序前15
```

**验证步骤：**
1. 应用排序筛选条件
2. 检查结果数量是否正确（10、20、15条）
3. 验证排序顺序是否正确
4. 对比两个页面的结果一致性

#### 测试用例2.2：新增字段排序
**测试条件：**
```
倍55 倒序前10
hupd33 倒序前10
hupd55 倒序前10
低量 倒序前10
```

**验证要点：**
- 字段识别准确性
- 排序逻辑正确性
- 数值处理准确性

### 3. 组合筛选功能测试

#### 测试用例3.1：筛选+排序组合
**测试条件：**
```
涨幅% > 5 AND 振幅 倒序前10
量比 > 2 AND 成交量 正序前15
(涨幅% > 3 OR 量比 > 1.5) AND 振幅 倒序前20
```

**验证步骤：**
1. 先应用筛选条件，记录中间结果
2. 再应用排序，验证最终结果
3. 确保组合逻辑正确执行

#### 测试用例3.2：复杂逻辑组合
**测试条件：**
```
涨幅% > 5 AND (量比 > 2 OR 次日量比 > 1.5)
(名称 CONTAINS "银行" OR 名称 CONTAINS "保险") AND 振幅 倒序前10
```

**验证要点：**
- 括号优先级处理
- 逻辑运算符正确性
- 复杂条件解析准确性

### 4. 状态保存功能测试

#### 测试用例4.1：日期翻页状态保持
**测试步骤：**
1. 应用筛选条件：`振幅 倒序前10`
2. 使用PageUp/PageDown翻页
3. 验证筛选条件是否保持
4. 验证筛选面板是否保持可见

**验证要点：**
- 筛选条件在翻页后保持不变
- 筛选面板状态正确恢复
- 数据正确应用筛选条件

#### 测试用例4.2：日期选择器切换
**测试步骤：**
1. 设置筛选条件：`涨幅% > 5 AND 量比 > 2`
2. 使用日期选择器切换到其他日期
3. 验证筛选状态是否保持

### 5. 快捷按钮功能测试

#### 测试用例5.1：预设按钮验证
**测试按钮：**
- 量比>2
- 振幅前10（正序）
- 倍55前10
- hupd33前10
- hupd55前10
- 低量前10

**验证步骤：**
1. 点击每个预设按钮
2. 验证输入框中的条件是否正确
3. 验证筛选结果是否符合预期

### 6. 错误处理测试

#### 测试用例6.1：语法错误处理
**错误输入：**
```
涨幅% >           # 缺少值
振幅 倒序前abc    # 非数字
(涨幅% > 5        # 括号不匹配
```

**验证要点：**
- 错误提示信息清晰
- 系统不会崩溃
- 错误恢复机制正常

#### 测试用例6.2：字段不存在处理
**测试输入：**
```
不存在字段 > 5
未知字段 倒序前10
```

**验证要点：**
- 字段匹配容错性
- 模糊匹配功能
- 错误提示准确性

## 性能对比测试

### 测试数据集
- **小数据集**: 100-500条记录
- **中数据集**: 500-2000条记录
- **大数据集**: 2000+条记录

### 性能指标

#### 1. 筛选响应时间
**测试方法：**
```javascript
const startTime = performance.now();
// 执行筛选操作
const endTime = performance.now();
const duration = endTime - startTime;
```

**对比项目：**
- 简单筛选条件响应时间
- 复杂筛选条件响应时间
- 排序筛选响应时间

#### 2. 内存使用情况
**监控指标：**
- 页面加载后内存占用
- 筛选操作后内存变化
- 长时间使用后内存稳定性

#### 3. 模块加载时间
**对比项目：**
- 原版页面首次加载时间
- 测试页面首次加载时间
- 模块文件加载时间

## 兼容性测试

### 浏览器兼容性
**测试浏览器：**
- Chrome (最新版本)
- Firefox (最新版本)
- Safari (如果可用)
- Edge (最新版本)

**测试要点：**
- 基础功能正常运行
- 样式显示正确
- 性能表现一致

### 设备兼容性
**测试设备：**
- 桌面端（1920x1080）
- 笔记本端（1366x768）
- 平板端（如果适用）

## 测试记录模板

### 功能测试记录
```
测试用例: [用例编号]
测试条件: [筛选条件]
原版结果: [数量] 条记录，耗时 [时间] ms
测试版结果: [数量] 条记录，耗时 [时间] ms
结果对比: ✅ 一致 / ❌ 不一致
备注: [详细说明]
```

### 性能测试记录
```
数据集大小: [记录数] 条
筛选条件: [条件内容]
原版性能: [时间] ms
测试版性能: [时间] ms
性能差异: [百分比]%
内存使用: [对比情况]
```

### 问题记录
```
问题类型: [功能/性能/兼容性]
问题描述: [详细描述]
重现步骤: [步骤列表]
预期结果: [预期行为]
实际结果: [实际行为]
严重程度: [高/中/低]
```

## 验收标准

### 功能完整性
- ✅ 所有筛选语法正确支持
- ✅ 排序筛选功能正常
- ✅ 状态保存机制正常
- ✅ 错误处理机制完善

### 性能要求
- ✅ 筛选响应时间不超过原版的120%
- ✅ 内存使用增长不超过20%
- ✅ 页面加载时间增长不超过30%

### 兼容性要求
- ✅ 主流浏览器完全兼容
- ✅ 不同屏幕分辨率正常显示
- ✅ 与现有模块无冲突

### 代码质量
- ✅ 模块接口清晰
- ✅ 错误处理完善
- ✅ 代码注释充分
- ✅ API文档完整

## 测试结论

### 成功标准
当所有测试用例通过，且性能和兼容性满足要求时，可以认为模块化重构成功，可以考虑应用到生产环境。

### 失败处理
如果测试发现重大问题，需要：
1. 详细记录问题
2. 分析根本原因
3. 修复模块代码
4. 重新进行测试

### 后续计划
测试成功后的后续步骤：
1. 代码审查和优化
2. 文档完善
3. 生产环境部署计划
4. 监控和维护策略

---

**测试负责人**: [姓名]  
**测试日期**: 2025-07-29  
**文档版本**: 1.0
