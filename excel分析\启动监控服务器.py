#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键启动监控服务器
功能：检查依赖、启动Web服务器和数据监控
作者：AI Assistant
日期：2025-07-23
"""

import os
import sys
import subprocess
from pathlib import Path

# 确保当前目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import watchdog
        return True
    except ImportError:
        return False

def install_dependencies():
    """安装依赖"""
    print("📦 正在安装依赖...")
    try:
        result = subprocess.run([
            sys.executable, "install_dependencies.py"
        ], check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """主函数"""
    print("🚀 复盘系统监控服务器启动器")
    print("=" * 50)

    # 获取脚本所在目录
    script_dir = Path(__file__).parent.absolute()
    print(f"📂 脚本所在目录: {script_dir}")

    # 切换到脚本所在目录
    os.chdir(script_dir)
    print(f"📂 当前工作目录: {Path.cwd()}")

    # 检查必要文件是否存在
    required_files = ["excel_processor.py", "web_monitor.py", "complete_monitor.py"]
    missing_files = [f for f in required_files if not (script_dir / f).exists()]

    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        print("💡 请确保所有文件都在同一目录下")
        input("按回车键退出...")
        return
    
    # 检查依赖
    if not check_dependencies():
        print("⚠️ 缺少依赖库，正在安装...")
        if not install_dependencies():
            print("❌ 依赖安装失败")
            print("💡 请手动运行: python install_dependencies.py")
            input("按回车键退出...")
            return
    
    print("✅ 依赖检查完成")

    # 检查目录结构
    if not Path("../复盘数据").exists():
        print("⚠️ 未找到'复盘数据'目录")
        print("💡 请确保'复盘数据'目录存在，用于存放通达信导出的.xls文件")
        create_dir = input("是否创建'复盘数据'目录？(y/n): ").lower().strip()
        if create_dir == 'y':
            Path("../复盘数据").mkdir(exist_ok=True)
            print("✅ '复盘数据'目录已创建")
        else:
            print("⚠️ 请手动创建'复盘数据'目录后重新运行")

    # 启动完整监控服务器
    try:
        print("🚀 正在启动完整自动化监控系统...")
        print("📂 监控目录: 复盘数据/*.xls")
        print("📊 输出目录: reports/data/*.json")
        print("🌐 Web服务: http://localhost:8000")
        print("\n💡 现在可以将通达信导出的.xls文件放入'复盘数据'目录")
        print("   系统会自动转换为JSON格式并更新网页显示")
        subprocess.run([sys.executable, "web_monitor.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 服务器启动失败: {e}")
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
