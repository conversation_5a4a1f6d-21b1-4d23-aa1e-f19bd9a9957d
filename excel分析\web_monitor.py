#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web服务器集成监控模块
功能：集成HTTP服务器和数据监控，提供一体化解决方案
作者：AI Assistant
日期：2025-07-23
"""

import os
import sys
import json
import threading
import time
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import logging
from pathlib import Path

# 确保当前目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入监控模块
try:
    from data_monitor import DataMonitor
    from complete_monitor import CompleteMonitor
except ImportError as e:
    print(f"❌ 导入监控模块失败: {e}")
    print("💡 请先安装依赖: pip install watchdog")
    print(f"📂 当前工作目录: {os.getcwd()}")
    print(f"📂 脚本所在目录: {current_dir}")
    exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MonitorHTTPRequestHandler(SimpleHTTPRequestHandler):
    """增强的HTTP请求处理器"""
    
    def __init__(self, *args, monitor=None, **kwargs):
        self.monitor = monitor
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        
        # API端点
        if parsed_path.path == '/api/monitor/status':
            self.handle_monitor_status()
        elif parsed_path.path == '/api/monitor/update':
            self.handle_manual_update()
        elif parsed_path.path == '/api/data/refresh':
            self.handle_data_refresh()
        else:
            # 默认文件服务
            super().do_GET()
    
    def handle_monitor_status(self):
        """返回监控状态"""
        try:
            status = {
                "running": self.monitor.is_running() if self.monitor else False,
                "timestamp": time.time(),
                "data_dir": str(self.monitor.data_dir) if self.monitor else None
            }
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = json.dumps(status, ensure_ascii=False)
            self.wfile.write(response.encode('utf-8'))
            
        except Exception as e:
            logger.error(f"获取监控状态失败: {e}")
            self.send_error(500, str(e))
    
    def handle_manual_update(self):
        """手动更新索引"""
        try:
            if self.monitor:
                success = self.monitor.manual_update()
                result = {
                    "success": success,
                    "message": "索引更新成功" if success else "索引更新失败",
                    "timestamp": time.time()
                }
            else:
                result = {
                    "success": False,
                    "message": "监控器未初始化",
                    "timestamp": time.time()
                }
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = json.dumps(result, ensure_ascii=False)
            self.wfile.write(response.encode('utf-8'))
            
        except Exception as e:
            logger.error(f"手动更新失败: {e}")
            self.send_error(500, str(e))
    
    def handle_data_refresh(self):
        """数据刷新端点"""
        try:
            # 读取最新的index.json
            if self.monitor:
                index_file = self.monitor.data_dir / "index.json"
                if index_file.exists():
                    with open(index_file, 'r', encoding='utf-8') as f:
                        index_data = json.load(f)
                else:
                    index_data = {"available_dates": [], "total_dates": 0}
            else:
                index_data = {"available_dates": [], "total_dates": 0}
            
            result = {
                "success": True,
                "data": index_data,
                "timestamp": time.time()
            }
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = json.dumps(result, ensure_ascii=False)
            self.wfile.write(response.encode('utf-8'))
            
        except Exception as e:
            logger.error(f"数据刷新失败: {e}")
            self.send_error(500, str(e))
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        # 只记录重要请求，减少日志噪音
        if any(path in self.path for path in ['/api/', '.json']):
            logger.info(f"{self.address_string()} - {format % args}")

class WebMonitorServer:
    """Web监控服务器（集成完整监控）"""

    def __init__(self, port=8000, source_dir="../复盘数据", output_dir="reports/data"):
        self.port = port
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.complete_monitor = None  # 完整监控（源文件到JSON）
        self.data_monitor = None      # 数据监控（JSON文件变化）
        self.server = None
        self.server_thread = None
        self.running = False
        
    def create_request_handler(self):
        """创建请求处理器"""
        data_monitor = self.data_monitor

        class RequestHandler(MonitorHTTPRequestHandler):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, monitor=data_monitor, **kwargs)

        return RequestHandler
    
    def start(self):
        """启动服务器和监控"""
        try:
            # 切换到reports目录
            reports_dir = Path("reports")
            if reports_dir.exists():
                os.chdir(reports_dir)
                logger.info(f"📂 切换到目录: {reports_dir.absolute()}")

            # 启动完整监控（源文件到JSON）
            self.complete_monitor = CompleteMonitor("../../复盘数据", "data")
            if not self.complete_monitor.start():
                logger.error("❌ 完整监控启动失败")
                return False

            # 启动数据监控（JSON文件变化，用于API）
            self.data_monitor = DataMonitor("data")
            if not self.data_monitor.start():
                logger.error("❌ 数据监控启动失败")
                return False
            
            # 启动HTTP服务器
            handler_class = self.create_request_handler()
            self.server = HTTPServer(('localhost', self.port), handler_class)
            
            # 在单独线程中运行服务器
            self.server_thread = threading.Thread(
                target=self.server.serve_forever,
                daemon=True
            )
            self.server_thread.start()
            self.running = True
            
            logger.info(f"🌐 Web服务器已启动: http://localhost:{self.port}")
            logger.info(f"📊 主页面: http://localhost:{self.port}/复盘分析_精简版.html")
            logger.info(f"🔧 监控状态: http://localhost:{self.port}/api/monitor/status")
            
            return True
            
        except Exception as e:
            logger.error(f"启动失败: {e}")
            return False
    
    def stop(self):
        """停止服务器和监控"""
        self.running = False

        if self.complete_monitor:
            self.complete_monitor.stop()

        if self.data_monitor:
            self.data_monitor.stop()

        if self.server:
            self.server.shutdown()
            self.server.server_close()

        if self.server_thread:
            self.server_thread.join(timeout=5)

        logger.info("🛑 Web监控服务器已停止")
    
    def is_running(self):
        """检查是否在运行"""
        return self.running

def main():
    """主函数"""
    import signal
    import sys
    
    # 检查依赖
    try:
        import watchdog
    except ImportError:
        print("❌ 缺少依赖库，请运行: pip install watchdog")
        return
    
    # 查找正确的工作目录
    current_dir = Path.cwd()
    if current_dir.name == "excel分析":
        # 已经在正确目录
        pass
    elif (current_dir / "excel分析").exists():
        # 需要进入excel分析目录
        os.chdir("excel分析")
    else:
        logger.error("❌ 请在excel分析目录或其父目录下运行此脚本")
        return
    
    # 创建服务器
    server = WebMonitorServer(port=8000, source_dir="../复盘数据", output_dir="reports/data")
    
    # 信号处理
    def signal_handler(sig, frame):
        logger.info("收到停止信号...")
        server.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动服务器
    if server.start():
        try:
            logger.info("✅ 服务器运行中，按 Ctrl+C 停止")
            while server.is_running():
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            server.stop()
    else:
        logger.error("❌ 服务器启动失败")

if __name__ == "__main__":
    main()
