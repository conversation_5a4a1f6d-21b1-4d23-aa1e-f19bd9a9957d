@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    通达信复盘数据处理器
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo 请确保已安装Python并添加到PATH环境变量
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 🔍 检查依赖包...
python -c "import pandas, openpyxl, xlrd, chardet" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少必要的依赖包，正在安装...
    pip install pandas openpyxl xlrd chardet
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查完成

echo.
echo 🚀 开始处理数据...
python excel_processor.py --verbose

if errorlevel 1 (
    echo.
    echo ⚠️  标准处理器遇到问题，尝试特殊编码修复...
    echo 🔧 运行增强修复工具...
    python enhanced_auto_fixer.py

    if errorlevel 1 (
        echo.
        echo ❌ 数据处理失败！
        echo 请检查错误信息并重试
    ) else (
        echo.
        echo ✅ 特殊编码文件修复完成！
        echo 🔄 重新运行标准处理器...
        python excel_processor.py --verbose

        if errorlevel 1 (
            echo ⚠️  部分文件可能需要手动处理
        ) else (
            echo ✅ 所有数据处理完成！
        )
    )
) else (
    echo.
    echo ✅ 数据处理完成！
)

echo.
echo 📁 数据文件已保存到: reports\data\
echo 📋 可用日期列表: reports\data\index.json

echo.
echo 按任意键退出...
pause >nul
