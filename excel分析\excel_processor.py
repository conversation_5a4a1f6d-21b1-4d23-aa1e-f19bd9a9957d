#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通达信复盘数据处理器
自动读取复盘数据目录下的Excel文件，转换为分离的JSON格式
保持与现有前端JS完全兼容

作者: AI Assistant
日期: 2025-01-23
版本: 1.0.0
"""

import os
import json
import pandas as pd
from pathlib import Path
from datetime import datetime
import re
import logging
from typing import Dict, List, Optional, Tuple
import chardet

class TongDaXinProcessor:
    """通达信数据处理器"""
    
    def __init__(self, data_dir: str = "../复盘数据", output_dir: str = "reports/data"):
        """
        初始化处理器
        
        Args:
            data_dir: 通达信数据目录
            output_dir: 输出目录
        """
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 支持的文件格式
        self.supported_formats = ['.xls', '.xlsx', '.csv']
        
        # 编码检测顺序（移除latin1，避免乱码）
        self.encodings = ['gbk', 'gb18030', 'gb2312', 'utf-8']
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('TongDaXinProcessor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def detect_encoding(self, file_path: Path) -> str:
        """
        智能检测文件编码

        Args:
            file_path: 文件路径

        Returns:
            检测到的编码格式
        """
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # 读取前10KB用于检测
                result = chardet.detect(raw_data)
                detected_encoding = result.get('encoding', 'gbk')
                confidence = result.get('confidence', 0)

                self.logger.info(f"文件 {file_path.name} 检测编码: {detected_encoding} (置信度: {confidence:.2f})")

                # 对于通达信文件，优先使用中文编码
                if detected_encoding and detected_encoding.lower() in ['gb2312', 'gbk', 'gb18030']:
                    return detected_encoding.lower()

                # 如果置信度较低或检测到其他编码，使用默认编码顺序
                if confidence < 0.7:
                    return 'gbk'

                return detected_encoding.lower() if detected_encoding else 'gbk'
        except Exception as e:
            self.logger.warning(f"编码检测失败: {e}，使用默认编码 gbk")
            return 'gbk'
    
    def extract_date_from_filename(self, filename: str) -> Optional[str]:
        """
        从文件名提取日期
        
        Args:
            filename: 文件名
            
        Returns:
            日期字符串 (YYYY-MM-DD) 或 None
        """
        # 匹配模式: 临时条件股_20250701_1.xls
        patterns = [
            r'(\d{8})',  # 8位数字日期
            r'(\d{4}-\d{2}-\d{2})',  # 标准日期格式
            r'(\d{4}_\d{2}_\d{2})',  # 下划线分隔
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                date_str = match.group(1)
                try:
                    # 转换为标准格式
                    if len(date_str) == 8:  # 20250701
                        date_obj = datetime.strptime(date_str, '%Y%m%d')
                    elif '_' in date_str:  # 2025_07_01
                        date_obj = datetime.strptime(date_str, '%Y_%m_%d')
                    else:  # 2025-07-01
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                    
                    return date_obj.strftime('%Y-%m-%d')
                except ValueError:
                    continue
        
        self.logger.warning(f"无法从文件名 {filename} 提取日期")
        return None
    
    def read_excel_file(self, file_path: Path) -> Optional[pd.DataFrame]:
        """
        读取数据文件，支持多种编码和格式
        通达信导出的.xls文件实际是TSV格式

        Args:
            file_path: 文件路径

        Returns:
            DataFrame或None
        """
        self.logger.info(f"正在读取文件: {file_path}")

        # 根据文件扩展名选择读取方法
        file_ext = file_path.suffix.lower()

        # 通达信的.xls文件实际是TSV格式，优先尝试TSV方式读取
        if file_ext in ['.xls', '.xlsx']:
            self.logger.info("检测到.xls/.xlsx文件，优先尝试TSV格式读取...")

            # 首先使用智能检测的编码
            detected_encoding = self.detect_encoding(file_path)

            # 构建编码尝试列表，优先使用检测到的编码
            encoding_list = [detected_encoding]
            for enc in self.encodings:
                if enc != detected_encoding:
                    encoding_list.append(enc)

            # 尝试TSV格式读取（通达信导出格式）
            for encoding in encoding_list:
                try:
                    self.logger.info(f"尝试TSV格式，编码: {encoding}")

                    # 智能检测需要跳过的行数
                    df = None
                    for skip_rows in [1, 2, 3, 0]:  # 尝试不同的跳过行数
                        try:
                            df_temp = pd.read_csv(file_path, encoding=encoding, sep='\t',
                                               skiprows=skip_rows, low_memory=False, dtype=str)

                            # 检查是否有合理的列数和数据
                            if not df_temp.empty and len(df_temp.columns) > 5:
                                column_names = list(df_temp.columns)
                                has_chinese = any('\u4e00' <= char <= '\u9fff' for col in column_names for char in str(col))

                                if has_chinese:
                                    df = df_temp
                                    self.logger.info(f"找到合适的跳过行数: {skip_rows}")
                                    break
                        except:
                            continue

                    if df is None:
                        continue

                    # 验证是否成功读取到数据
                    if not df.empty and len(df.columns) > 5:  # 降低列数要求
                        # 额外验证：检查列名是否包含中文字符
                        column_names = list(df.columns)
                        has_chinese = any('\u4e00' <= char <= '\u9fff' for col in column_names for char in str(col))

                        if has_chinese:
                            self.logger.info(f"✅ TSV格式读取成功，编码: {encoding}，数据形状: {df.shape}")
                            self.logger.info(f"✅ 检测到中文列名: {column_names[:5]}")
                            return df
                        else:
                            self.logger.warning(f"TSV读取成功但列名可能有乱码，编码: {encoding}")
                            self.logger.warning(f"列名示例: {column_names[:5]}")
                            # 继续尝试其他编码
                    else:
                        self.logger.warning(f"TSV读取数据异常: 形状{df.shape}，列数{len(df.columns)}")

                except Exception as e:
                    self.logger.debug(f"TSV格式读取失败，编码{encoding}: {str(e)[:100]}")
                    continue

            # TSV失败后，尝试真正的Excel格式
            self.logger.info("TSV格式失败，尝试真正的Excel格式...")
            try:
                engine = 'openpyxl' if file_ext == '.xlsx' else 'xlrd'
                df = pd.read_excel(file_path, engine=engine)
                self.logger.info(f"✅ Excel格式读取成功，数据形状: {df.shape}")
                return df
            except Exception as e:
                self.logger.warning(f"Excel格式也失败: {str(e)[:100]}")

        elif file_ext == '.csv':
            # CSV文件，需要检测编码
            encoding = self.detect_encoding(file_path)
            try:
                df = pd.read_csv(file_path, encoding=encoding, low_memory=False, dtype=str)
                self.logger.info(f"✅ CSV格式读取成功，编码: {encoding}，数据形状: {df.shape}")
                return df
            except Exception as e:
                self.logger.error(f"CSV读取失败: {e}")
                return None

        else:
            self.logger.error(f"不支持的文件格式: {file_ext}")
            return None

        self.logger.error("所有读取方式都失败")
        return None
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗通达信数据
        处理特殊格式和编码问题

        Args:
            df: 原始数据

        Returns:
            清洗后的数据
        """
        if df is None or df.empty:
            return df

        self.logger.info(f"开始清洗数据，原始形状: {df.shape}")

        # 1. 移除完全空的行和列
        df = df.dropna(how='all')
        df = df.dropna(axis=1, how='all')

        # 2. 处理通达信特殊格式
        # 移除第一行如果是标题行（包含"历史回测"等字样）
        if len(df) > 0 and any('历史' in str(cell) or '回测' in str(cell) for cell in df.iloc[0]):
            self.logger.info("检测到标题行，移除第一行")
            df = df.iloc[1:].reset_index(drop=True)

        # 3. 清理列名
        if len(df.columns) > 0:
            # 使用第一行作为列名（如果当前列名不合理）
            if len(df) > 0 and all(str(col).startswith('Unnamed') or str(col).isdigit() for col in df.columns):
                self.logger.info("使用第一行数据作为列名")
                new_columns = [str(cell).strip() for cell in df.iloc[0]]
                df.columns = new_columns
                df = df.iloc[1:].reset_index(drop=True)

        # 4. 清理列名（移除空格和特殊字符）
        df.columns = [str(col).strip() for col in df.columns]

        # 5. 处理数据中的特殊字符
        for col in df.columns:
            if df[col].dtype == 'object':
                # 移除Excel公式前缀 ="
                df[col] = df[col].astype(str).str.replace(r'^="', '', regex=True)
                df[col] = df[col].str.replace(r'"$', '', regex=True)
                # 清理空白字符
                df[col] = df[col].str.strip()

        # 6. 重置索引
        df = df.reset_index(drop=True)

        # 7. 移除完全空的行（清洗后可能产生）
        df = df.dropna(how='all')

        self.logger.info(f"数据清洗完成，最终形状: {df.shape}")
        if len(df.columns) > 0:
            self.logger.info(f"列名示例: {list(df.columns[:5])}")

        return df
    
    def convert_to_json_format(self, df: pd.DataFrame, date: str) -> List[Dict]:
        """
        转换为前端兼容的JSON格式
        
        Args:
            df: 数据框
            date: 日期
            
        Returns:
            JSON格式的数据列表
        """
        if df is None or df.empty:
            return []
        
        # 转换为字典列表
        records = df.to_dict('records')
        
        # 处理NaN值和数据类型
        for record in records:
            for key, value in record.items():
                if pd.isna(value):
                    record[key] = ""
                elif key == '代码':
                    # 股票代码必须保持字符串格式，保留前导零
                    record[key] = str(value).replace('.0', '') if isinstance(value, float) else str(value)
                elif isinstance(value, float) and value.is_integer():
                    record[key] = int(value)
        
        self.logger.info(f"转换完成，日期 {date} 包含 {len(records)} 条记录")
        return records
    
    def save_date_data(self, data: List[Dict], date: str) -> bool:
        """
        保存单个日期的数据
        
        Args:
            data: 数据列表
            date: 日期
            
        Returns:
            是否保存成功
        """
        try:
            output_file = self.output_dir / f"date_{date}.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"数据已保存到: {output_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")
            return False
    
    def update_index(self, available_dates: List[str]) -> bool:
        """
        更新数据索引文件
        
        Args:
            available_dates: 可用日期列表
            
        Returns:
            是否更新成功
        """
        try:
            index_data = {
                "last_updated": datetime.now().isoformat(),
                "available_dates": sorted(available_dates),
                "total_dates": len(available_dates)
            }
            
            index_file = self.output_dir / "index.json"
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"索引文件已更新: {index_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新索引失败: {e}")
            return False

    def process_all_files(self) -> bool:
        """
        处理所有数据文件

        Returns:
            是否处理成功
        """
        if not self.data_dir.exists():
            self.logger.error(f"数据目录不存在: {self.data_dir}")
            return False

        processed_dates = []

        # 扫描所有支持的文件
        for file_path in self.data_dir.iterdir():
            if file_path.suffix.lower() in self.supported_formats:
                self.logger.info(f"发现文件: {file_path.name}")

                # 提取日期
                date = self.extract_date_from_filename(file_path.name)
                if not date:
                    self.logger.warning(f"跳过文件（无法提取日期）: {file_path.name}")
                    continue

                # 读取和处理数据
                df = self.read_excel_file(file_path)
                if df is None:
                    self.logger.error(f"跳过文件（读取失败）: {file_path.name}")
                    continue

                # 清洗数据
                df = self.clean_data(df)

                # 转换格式
                json_data = self.convert_to_json_format(df, date)

                # 保存数据
                if self.save_date_data(json_data, date):
                    processed_dates.append(date)
                    self.logger.info(f"✅ 成功处理: {file_path.name} -> {date}")
                else:
                    self.logger.error(f"❌ 处理失败: {file_path.name}")

        # 更新索引
        if processed_dates:
            self.update_index(processed_dates)
            self.logger.info(f"🎉 处理完成！共处理 {len(processed_dates)} 个文件")
            return True
        else:
            self.logger.warning("⚠️ 没有成功处理任何文件")
            return False

    def process_single_file(self, filename: str) -> bool:
        """
        处理单个文件（用于增量更新）

        Args:
            filename: 文件名

        Returns:
            是否处理成功
        """
        file_path = self.data_dir / filename

        if not file_path.exists():
            self.logger.error(f"文件不存在: {file_path}")
            return False

        # 提取日期
        date = self.extract_date_from_filename(filename)
        if not date:
            self.logger.error(f"无法从文件名提取日期: {filename}")
            return False

        # 处理文件
        df = self.read_excel_file(file_path)
        if df is None:
            return False

        df = self.clean_data(df)
        json_data = self.convert_to_json_format(df, date)

        if self.save_date_data(json_data, date):
            # 更新索引
            index_file = self.output_dir / "index.json"
            if index_file.exists():
                with open(index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)

                if date not in index_data.get('available_dates', []):
                    index_data['available_dates'].append(date)
                    index_data['available_dates'].sort()
                    index_data['total_dates'] = len(index_data['available_dates'])
                    index_data['last_updated'] = datetime.now().isoformat()

                    with open(index_file, 'w', encoding='utf-8') as f:
                        json.dump(index_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"✅ 单文件处理成功: {filename} -> {date}")
            return True

        return False


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='通达信复盘数据处理器')
    parser.add_argument('--data-dir', default='../复盘数据', help='数据目录路径')
    parser.add_argument('--output-dir', default='reports/data', help='输出目录路径')
    parser.add_argument('--file', help='处理单个文件')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger('TongDaXinProcessor').setLevel(logging.DEBUG)

    # 创建处理器
    processor = TongDaXinProcessor(args.data_dir, args.output_dir)

    # 处理文件
    if args.file:
        success = processor.process_single_file(args.file)
    else:
        success = processor.process_all_files()

    if success:
        print("🎉 数据处理完成！")
        return 0
    else:
        print("❌ 数据处理失败！")
        return 1


if __name__ == '__main__':
    exit(main())
