<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整自动化系统使用指南</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        h2 { color: #34495e; border-bottom: 2px solid #27ae60; padding-bottom: 10px; }
        .section { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #27ae60; }
        .workflow { margin: 10px 0; padding: 15px; background: white; border-radius: 5px; border: 1px solid #e9ecef; }
        .step { margin: 8px 0; padding: 10px; background: #e8f5e8; border-radius: 4px; border-left: 3px solid #27ae60; }
        .auto { color: #28a745; font-weight: bold; }
        .manual { color: #dc3545; font-weight: bold; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; border: 1px solid #dee2e6; }
        .btn { padding: 10px 20px; background: #27ae60; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #219a52; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        .highlight { background: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 15px 0; }
        .flow-diagram { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
        .arrow { color: #27ae60; font-size: 24px; margin: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 完整端到端自动化系统使用指南</h1>
        
        <div class="status success">
            <strong>🎉 完整自动化工作流程：</strong>
            <ul>
                <li>✅ 监控"复盘数据"目录中的.xls文件变化</li>
                <li>✅ 自动调用excel_processor.py转换为JSON格式</li>
                <li>✅ 自动放置到reports/data目录</li>
                <li>✅ 自动更新index.json索引文件</li>
                <li>✅ 网页自动刷新显示最新数据</li>
                <li>✅ 完全无需手动操作</li>
            </ul>
        </div>
        
        <h2>📊 完整工作流程图</h2>
        
        <div class="flow-diagram">
            <div style="display: flex; justify-content: center; align-items: center; flex-wrap: wrap;">
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px;">
                    📁 通达信导出<br>
                    <strong>复盘数据/*.xls</strong>
                </div>
                <div class="arrow">→</div>
                <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px;">
                    👀 文件监控<br>
                    <strong>complete_monitor.py</strong>
                </div>
                <div class="arrow">→</div>
                <div style="background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 10px;">
                    🔄 自动转换<br>
                    <strong>excel_processor.py</strong>
                </div>
                <div class="arrow">→</div>
                <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px;">
                    📊 JSON输出<br>
                    <strong>reports/data/*.json</strong>
                </div>
                <div class="arrow">→</div>
                <div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px;">
                    🌐 网页显示<br>
                    <strong>自动刷新</strong>
                </div>
            </div>
        </div>
        
        <h2>🚀 快速开始</h2>
        
        <div class="section">
            <h3>一键启动（推荐）</h3>
            <div class="workflow">
                <div class="step">1. 双击运行 <code>启动监控服务器.py</code></div>
                <div class="step">2. 等待依赖检查和安装完成</div>
                <div class="step">3. 系统自动启动完整监控</div>
                <div class="step">4. 浏览器自动打开复盘分析页面</div>
                <div class="step">5. 开始使用！将.xls文件放入"复盘数据"目录即可</div>
                
                <div class="highlight">
                    <strong>💡 现在的工作流程：</strong>
                    <ol>
                        <li>从通达信导出数据到"复盘数据"目录</li>
                        <li>系统自动检测新文件</li>
                        <li>自动转换为JSON格式</li>
                        <li>网页自动刷新显示新数据</li>
                        <li>完全无需手动操作！</li>
                    </ol>
                </div>
            </div>
        </div>
        
        <h2>🔧 系统架构</h2>
        
        <div class="section">
            <h3>核心模块</h3>
            <div class="workflow">
                <strong>📁 complete_monitor.py</strong> - 完整端到端监控
                <ul>
                    <li>监控"复盘数据"目录的.xls文件变化</li>
                    <li>自动调用excel_processor.py进行转换</li>
                    <li>处理文件创建、修改、删除事件</li>
                    <li>智能防抖，避免重复处理</li>
                </ul>
                
                <strong>🔄 excel_processor.py</strong> - 数据转换核心
                <ul>
                    <li>读取通达信特殊格式的.xls文件（实际是TSV）</li>
                    <li>智能编码检测和数据清洗</li>
                    <li>转换为标准JSON格式</li>
                    <li>自动提取日期信息</li>
                </ul>
                
                <strong>🌐 web_monitor.py</strong> - 集成Web服务器
                <ul>
                    <li>集成完整监控和Web服务</li>
                    <li>提供API接口供网页调用</li>
                    <li>支持网页自动刷新</li>
                    <li>一体化解决方案</li>
                </ul>
                
                <strong>📊 data_monitor.py</strong> - JSON文件监控
                <ul>
                    <li>监控reports/data目录的JSON文件</li>
                    <li>自动更新index.json索引</li>
                    <li>提供API状态查询</li>
                </ul>
            </div>
        </div>
        
        <h2>⚡ 自动化功能详解</h2>
        
        <div class="section">
            <h3>文件监控和处理</h3>
            <div class="workflow">
                <strong class="auto">✅ 自动检测：</strong>
                <div class="step">• 新增.xls文件到"复盘数据"目录</div>
                <div class="step">• 修改现有.xls文件</div>
                <div class="step">• 删除.xls文件（同时清理对应JSON）</div>
                
                <strong class="auto">✅ 智能处理：</strong>
                <div class="step">• 3秒延迟处理，确保文件写入完成</div>
                <div class="step">• 文件锁检测，避免处理正在写入的文件</div>
                <div class="step">• 自动重试机制</div>
                <div class="step">• 详细的处理日志</div>
                
                <strong class="auto">✅ 格式转换：</strong>
                <div class="step">• 自动识别通达信TSV格式</div>
                <div class="step">• 智能编码检测（GBK/UTF-8等）</div>
                <div class="step">• 数据清洗和格式标准化</div>
                <div class="step">• 自动提取日期信息</div>
            </div>
        </div>
        
        <div class="section">
            <h3>网页自动更新</h3>
            <div class="workflow">
                <strong class="auto">✅ 智能刷新：</strong>
                <div class="step">• 每30秒检查数据更新</div>
                <div class="step">• 只在数据真正变化时刷新</div>
                <div class="step">• 保持当前筛选和选中状态</div>
                <div class="step">• 显示更新提示信息</div>
            </div>
        </div>
        
        <h2>📋 实际使用场景</h2>
        
        <div class="section">
            <h3>日常复盘工作流程</h3>
            <div class="workflow">
                <strong>传统方式（需要多步操作）：</strong>
                <div class="step manual">1. 从通达信导出数据</div>
                <div class="step manual">2. 手动运行处理脚本</div>
                <div class="step manual">3. 手动更新索引文件</div>
                <div class="step manual">4. 手动刷新网页</div>
                
                <strong>现在的方式（完全自动）：</strong>
                <div class="step auto">1. 启动监控系统（一次性）</div>
                <div class="step auto">2. 从通达信导出数据到"复盘数据"目录</div>
                <div class="step auto">3. 系统自动处理一切！</div>
                <div class="step auto">4. 网页自动显示最新数据</div>
            </div>
        </div>
        
        <div class="section">
            <h3>批量历史数据导入</h3>
            <div class="workflow">
                <div class="step">1. 启动监控系统</div>
                <div class="step">2. 将多个历史.xls文件复制到"复盘数据"目录</div>
                <div class="step">3. 系统自动逐个处理所有文件</div>
                <div class="step">4. 自动生成完整的日期索引</div>
                <div class="step">5. 网页显示所有可用日期</div>
            </div>
        </div>
        
        <h2>🔍 监控日志</h2>
        
        <div class="section">
            <h3>日志文件</h3>
            <div class="workflow">
                <strong>complete_monitor.log</strong> - 完整监控日志
                <div class="code">
2025-07-23 22:06:20 - INFO - 📁 检测到新文件: 临时条件股_20250704_1.xls
2025-07-23 22:06:23 - INFO - 🔄 开始处理文件: 临时条件股_20250704_1.xls
2025-07-23 22:06:25 - INFO - ✅ 文件处理成功: 临时条件股_20250704_1.xls
2025-07-23 22:06:25 - INFO - 📊 索引文件已更新
                </div>
                
                <strong>data_monitor.log</strong> - JSON文件监控日志
                <div class="code">
2025-07-23 22:06:25 - INFO - 📁 检测到新文件: date_2025-07-04.json
2025-07-23 22:06:25 - INFO - ✅ 新增日期: ['2025-07-04']
2025-07-23 22:06:25 - INFO - 🔄 索引文件已自动更新
                </div>
            </div>
        </div>
        
        <h2>🛠️ 故障排除</h2>
        
        <div class="section">
            <h3>常见问题</h3>
            <div class="workflow">
                <strong>问题：文件没有自动处理</strong>
                <div class="step">• 检查"复盘数据"目录是否存在</div>
                <div class="step">• 确认文件格式为.xls/.xlsx/.csv</div>
                <div class="step">• 查看complete_monitor.log日志</div>
                <div class="step">• 确认文件名包含日期信息</div>
                
                <strong>问题：转换失败</strong>
                <div class="step">• 检查文件是否损坏</div>
                <div class="step">• 确认文件编码（通常是GBK）</div>
                <div class="step">• 查看详细错误日志</div>
                <div class="step">• 尝试手动处理：python excel_processor.py --file 文件名</div>
                
                <strong>问题：网页不更新</strong>
                <div class="step">• 确认使用web_monitor.py启动</div>
                <div class="step">• 检查浏览器控制台错误</div>
                <div class="step">• 访问 /api/monitor/status 检查状态</div>
                <div class="step">• 强制刷新页面（Ctrl+F5）</div>
            </div>
        </div>
        
        <h2>🎯 最佳实践</h2>
        
        <div class="section">
            <h3>推荐工作流程</h3>
            <div class="workflow">
                <div class="step">1. <strong>系统启动</strong>：每天开始工作时运行 启动监控服务器.py</div>
                <div class="step">2. <strong>数据导出</strong>：从通达信按正常流程导出数据</div>
                <div class="step">3. <strong>文件保存</strong>：直接保存到"复盘数据"目录</div>
                <div class="step">4. <strong>自动处理</strong>：系统自动检测和转换</div>
                <div class="step">5. <strong>数据分析</strong>：在网页中进行复盘分析</div>
                <div class="step">6. <strong>持续监控</strong>：保持系统运行，随时添加新数据</div>
            </div>
        </div>
        
        <div class="section">
            <h3>文件命名建议</h3>
            <div class="workflow">
                <strong>推荐的文件命名格式：</strong>
                <div class="step">• 临时条件股_20250704_1.xls ✅</div>
                <div class="step">• 复盘数据_2025-07-04.xls ✅</div>
                <div class="step">• 20250704_股票数据.xls ✅</div>
                <div class="step">• 数据_2025_07_04.xls ✅</div>
                
                <strong>系统会自动识别以下日期格式：</strong>
                <div class="step">• YYYYMMDD（如：20250704）</div>
                <div class="step">• YYYY-MM-DD（如：2025-07-04）</div>
                <div class="step">• YYYY_MM_DD（如：2025_07_04）</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="复盘分析_精简版.html" class="btn">🔄 返回主页面</a>
            <button class="btn" onclick="checkSystemStatus()">🔍 检查系统状态</button>
            <button class="btn" onclick="viewLogs()">📋 查看日志</button>
        </div>
    </div>
    
    <script>
        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const response = await fetch('/api/monitor/status');
                if (response.ok) {
                    const status = await response.json();
                    if (status.running) {
                        alert('✅ 监控系统运行正常\n' + 
                              `📂 监控目录: ${status.data_dir}\n` +
                              `⏰ 检查时间: ${new Date(status.timestamp * 1000).toLocaleString()}\n\n` +
                              '💡 现在可以将.xls文件放入"复盘数据"目录，系统会自动处理');
                    } else {
                        alert('⚠️ 监控系统未运行\n请运行 启动监控服务器.py');
                    }
                } else {
                    alert('❌ 无法连接到监控API\n请确认使用完整监控系统启动');
                }
            } catch (error) {
                alert('❌ 系统状态检查失败\n' + error.message);
            }
        }
        
        // 查看日志（模拟功能）
        function viewLogs() {
            alert('📋 日志文件位置：\n\n' +
                  '• complete_monitor.log - 完整监控日志\n' +
                  '• data_monitor.log - 数据监控日志\n\n' +
                  '可以用文本编辑器打开查看详细信息');
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 完整自动化系统使用指南已加载');
            console.log('💡 提示：现在只需要将.xls文件放入"复盘数据"目录，系统会自动处理一切！');
        });
    </script>
</body>
</html>
