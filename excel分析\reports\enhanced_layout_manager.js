/**
 * enhanced_layout_manager.js
 * 增强布局管理器 - 实现动态布局调整、键盘快捷键、K线图集成等功能
 * 
 * 功能特性：
 * 1. 动态布局调整（K线图显示时上下分割）
 * 2. 表格固定表头
 * 3. 键盘快捷键支持（PageUp/PageDown切换日期）
 * 4. K线图与表格数据同步
 * 5. 平滑过渡动画
 * 6. 加载状态指示器
 */

class EnhancedLayoutManager {
    constructor() {
        this.isKlineVisible = false;
        this.currentSelectedStock = null;
        this.currentSelectedDate = null;
        this.keyboardHintsTimer = null;
        
        // DOM元素引用
        this.mainContainer = null;
        this.klineSection = null;
        this.tableSection = null;
        this.headerSection = null;
        this.klineLoadingIndicator = null;
        this.keyboardHints = null;
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化管理器
     */
    init() {
        console.log('🚀 EnhancedLayoutManager 开始初始化...');
        
        // 获取DOM元素
        this.initDOMElements();
        
        // 设置键盘事件监听
        this.setupKeyboardEvents();
        
        // 设置K线图事件监听
        this.setupKlineEvents();
        
        // 显示键盘快捷键提示
        this.showKeyboardHints();
        
        console.log('✅ EnhancedLayoutManager 初始化完成');
    }
    
    /**
     * 初始化DOM元素引用
     */
    initDOMElements() {
        this.mainContainer = document.getElementById('mainContainer');
        this.klineSection = document.getElementById('klineSection');
        this.tableSection = document.getElementById('tableSection');
        this.headerSection = document.querySelector('.header-section');
        this.klineLoadingIndicator = document.getElementById('klineLoadingIndicator');
        this.keyboardHints = document.getElementById('keyboardHints');

        if (!this.mainContainer || !this.klineSection || !this.tableSection) {
            console.error('❌ 关键DOM元素未找到');
            return false;
        }

        if (!this.headerSection) {
            console.warn('⚠️ Header区域未找到，动态隐藏功能将不可用');
        }

        console.log('✅ DOM元素初始化完成');
        return true;
    }
    
    /**
     * 设置键盘事件监听
     */
    setupKeyboardEvents() {
        // 检查是否已经绑定了分页键盘事件，避免重复绑定
        if (!window.layoutKeyboardEventsBound) {
            document.addEventListener('keydown', (event) => {
                console.log(`🔍 [键盘检查12] enhanced_layout_manager收到键盘事件: ${event.key}`);

                // 防止在输入框中触发
                if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                    console.log(`🔍 [键盘检查13] 跳过输入框中的键盘事件`);
                    return;
                }

                switch(event.key) {
                    case 'PageUp':
                        console.log(`🔍 [键盘检查14] enhanced_layout_manager跳过PageUp处理，由date_pagination_manager统一处理`);
                        // 不处理PageUp，避免与date_pagination_manager冲突
                        break;

                    case 'PageDown':
                        console.log(`🔍 [键盘检查15] enhanced_layout_manager跳过PageDown处理，由date_pagination_manager统一处理`);
                        // 不处理PageDown，避免与date_pagination_manager冲突
                        break;

                    case 'Escape':
                        event.preventDefault();
                        console.log(`🔍 [键盘检查16] 处理Escape事件`);
                        this.hideKlineChart();
                        break;

                    default:
                        console.log(`🔍 [键盘检查17] 非分页键，不处理: ${event.key}`);
                        break;
                }
            });

            window.layoutKeyboardEventsBound = true;
            console.log('✅ 布局管理器键盘事件已绑定（防重复）');
        } else {
            console.log('⚠️ 布局管理器键盘事件已存在，跳过重复绑定');
        }
    }
    
    /**
     * 设置K线图相关事件监听
     */
    setupKlineEvents() {
        // 监听表格行点击事件（委托事件）
        document.addEventListener('click', (event) => {
            const row = event.target.closest('table.dataframe tbody tr');
            if (row) {
                this.handleRowClick(row);
            }
        });
        
        console.log('📊 K线图事件监听已设置');
    }
    
    /**
     * 处理表格行点击事件
     */
    handleRowClick(row) {
        const cells = row.querySelectorAll('td');
        if (cells.length < 2) return;
        
        const stockCode = cells[0].textContent.trim();
        const stockName = cells[1].textContent.trim();
        const dateCell = row.querySelector('td[data-date]') || 
                        Array.from(cells).find(cell => cell.textContent.match(/\d{4}-\d{2}-\d{2}/));
        
        let date = null;
        if (dateCell) {
            date = dateCell.textContent.trim();
        } else {
            // 尝试从当前分页管理器获取日期
            const paginationManager = window.globalDatePaginationManager || window.datePaginationManager;
            if (paginationManager && paginationManager.currentDate) {
                date = paginationManager.currentDate;
            }
        }
        
        if (stockCode && stockName) {
            this.showKlineChart(stockCode, stockName, date);
        }
    }
    
    /**
     * 显示K线图（嵌入式模式）
     */
    showKlineChart(stockCode, stockName, date) {
        console.log(`📈 显示嵌入式K线图: ${stockCode} ${stockName} ${date}`);

        this.currentSelectedStock = { code: stockCode, name: stockName };
        this.currentSelectedDate = date;

        // 切换到K线图布局
        this.switchToKlineLayout();

        // 显示加载指示器
        this.showLoadingIndicator();

        // 确保使用嵌入式显示方法，完全禁用模态弹窗
        if (typeof globalKlineManager !== 'undefined' && globalKlineManager) {
            // 强制禁用模态弹窗
            if (globalKlineManager.modal) {
                console.log(`🔍 [布局管理器1] 强制禁用模态弹窗 - 这会阻止overlay更新`);
                globalKlineManager.modal.style.display = 'none';
                globalKlineManager.modal.style.visibility = 'hidden';
                globalKlineManager.modal.style.opacity = '0';
                globalKlineManager.modal.style.zIndex = '-1';
                console.log(`🔍 [布局管理器2] 模态弹窗已被强制隐藏`);
            }

            // 调用嵌入式显示方法
            console.log(`🔍 [布局管理器3] 准备调用showEmbedded: ${stockName} (${stockCode}) - ${date}`);
            globalKlineManager.showEmbedded(stockCode, stockName, date, 'klineChartContainer')
                .then(() => {
                    this.hideLoadingIndicator();
                    console.log('✅ 嵌入式K线图显示成功');
                })
                .catch((error) => {
                    console.error('❌ K线图加载失败:', error);
                    this.hideLoadingIndicator();
                    this.hideKlineChart();
                });
        } else {
            console.warn('⚠️ K线图管理器未找到');
            this.hideLoadingIndicator();
        }
    }
    
    /**
     * 隐藏K线图
     */
    hideKlineChart() {
        console.log('📉 隐藏K线图');
        
        this.isKlineVisible = false;
        this.currentSelectedStock = null;
        this.currentSelectedDate = null;
        
        // 切换到全屏表格布局
        this.switchToTableLayout();
        
        // 清空K线图容器
        const klineContainer = document.getElementById('klineChartContainer');
        if (klineContainer) {
            klineContainer.innerHTML = '';
        }
    }
    
    /**
     * 切换到K线图布局（上下分割）
     */
    switchToKlineLayout() {
        if (!this.mainContainer || !this.klineSection) return;

        this.isKlineVisible = true;
        this.mainContainer.classList.add('layout-with-kline');
        this.klineSection.classList.add('active');

        // 隐藏header区域以获得更多显示空间
        this.hideHeader();

        console.log('🔄 已切换到K线图布局（header已隐藏）');
    }
    
    /**
     * 切换到全屏表格布局
     */
    switchToTableLayout() {
        if (!this.mainContainer || !this.klineSection) return;

        this.isKlineVisible = false;
        this.mainContainer.classList.remove('layout-with-kline');
        this.klineSection.classList.remove('active');

        // 恢复显示header区域
        this.showHeader();

        console.log('🔄 已切换到全屏表格布局（header已恢复）');
    }
    
    /**
     * 显示加载指示器
     */
    showLoadingIndicator() {
        if (this.klineLoadingIndicator) {
            this.klineLoadingIndicator.style.display = 'block';
        }
    }
    
    /**
     * 隐藏加载指示器
     */
    hideLoadingIndicator() {
        if (this.klineLoadingIndicator) {
            this.klineLoadingIndicator.style.display = 'none';
        }
    }

    /**
     * 隐藏header区域
     */
    hideHeader() {
        if (this.headerSection) {
            this.headerSection.style.transform = 'translateY(-100%)';
            this.headerSection.style.opacity = '0';
            // 延迟设置display以保持动画效果
            setTimeout(() => {
                if (this.isKlineVisible && this.headerSection) {
                    this.headerSection.style.display = 'none';
                }
            }, 300);
            console.log('🙈 Header区域已隐藏');
        }
    }

    /**
     * 显示header区域
     */
    showHeader() {
        if (this.headerSection) {
            this.headerSection.style.display = 'flex';
            // 短暂延迟以确保display生效后再执行动画
            setTimeout(() => {
                if (this.headerSection) {
                    this.headerSection.style.transform = 'translateY(0)';
                    this.headerSection.style.opacity = '1';
                }
            }, 10);
            console.log('👁️ Header区域已显示');
        }
    }
    
    /**
     * 切换到上一个交易日期
     */
    switchToPreviousDate() {
        const paginationManager = window.globalDatePaginationManager || window.datePaginationManager;
        if (paginationManager) {
            const currentIndex = paginationManager.availableDates.indexOf(
                paginationManager.currentDate
            );

            if (currentIndex > 0) {
                const previousDate = paginationManager.availableDates[currentIndex - 1];
                paginationManager.switchToDate(previousDate);
                console.log(`📅 切换到上一个交易日: ${previousDate}`);

                // 如果当前有选中的股票，更新K线图
                if (this.isKlineVisible && this.currentSelectedStock) {
                    this.showKlineChart(
                        this.currentSelectedStock.code,
                        this.currentSelectedStock.name,
                        previousDate
                    );
                }
            } else {
                console.log('📅 已经是最早的交易日');
            }
        }
    }

    /**
     * 切换到下一个交易日期
     */
    switchToNextDate() {
        const paginationManager = window.globalDatePaginationManager || window.datePaginationManager;
        if (paginationManager) {
            const currentIndex = paginationManager.availableDates.indexOf(
                paginationManager.currentDate
            );

            if (currentIndex < paginationManager.availableDates.length - 1) {
                const nextDate = paginationManager.availableDates[currentIndex + 1];
                paginationManager.switchToDate(nextDate);
                console.log(`📅 切换到下一个交易日: ${nextDate}`);

                // 如果当前有选中的股票，更新K线图
                if (this.isKlineVisible && this.currentSelectedStock) {
                    this.showKlineChart(
                        this.currentSelectedStock.code,
                        this.currentSelectedStock.name,
                        nextDate
                    );
                }
            } else {
                console.log('📅 已经是最新的交易日');
            }
        }
    }
    
    /**
     * 显示键盘快捷键提示
     */
    showKeyboardHints() {
        if (!this.keyboardHints) return;
        
        // 显示提示
        this.keyboardHints.classList.add('show');
        
        // 3秒后自动隐藏
        if (this.keyboardHintsTimer) {
            clearTimeout(this.keyboardHintsTimer);
        }
        
        this.keyboardHintsTimer = setTimeout(() => {
            this.keyboardHints.classList.remove('show');
        }, 3000);
    }
}

// 确保在全局作用域中可用
window.EnhancedLayoutManager = EnhancedLayoutManager;
