<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复盘数据更新流程指南</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .section { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #3498db; }
        .flow-item { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border: 1px solid #e9ecef; }
        .step { margin: 5px 0; padding: 8px; background: #e3f2fd; border-radius: 4px; }
        .auto { color: #28a745; font-weight: bold; }
        .manual { color: #dc3545; font-weight: bold; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; border: 1px solid #dee2e6; }
        .btn { padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #2980b9; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        .file-structure { background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 复盘数据更新流程指南</h1>
        
        <div class="status info">
            <strong>📋 当前数据处理机制：</strong>
            <ul>
                <li>网页从 <code>data/</code> 目录加载JSON格式的股票数据</li>
                <li>K线图数据实时从东方财富API获取</li>
                <li>支持多日期数据切换和筛选功能</li>
            </ul>
        </div>
        
        <h2>🔄 数据更新流程</h2>
        
        <div class="section">
            <h3>1. 当前数据文件结构</h3>
            <div class="file-structure">
excel分析/reports/data/
├── index.json              # 数据索引文件（包含可用日期列表）
├── date_2025-07-01.json   # 2025-07-01的股票数据
├── date_2025-07-02.json   # 2025-07-02的股票数据
└── date_2025-07-03.json   # 2025-07-03的股票数据
            </div>
        </div>
        
        <div class="section">
            <h3>2. 数据加载机制</h3>
            <div class="flow-item">
                <strong>页面初始化流程：</strong>
                <div class="step">1. 加载 <code>data/index.json</code> 获取可用日期列表</div>
                <div class="step">2. 预加载所有日期的数据文件到内存</div>
                <div class="step">3. 显示最新日期的数据</div>
                <div class="step">4. 用户可以通过日期选择器切换不同日期</div>
            </div>
            
            <div class="flow-item">
                <strong>日期切换机制：</strong>
                <div class="step">1. 用户选择新日期或使用PageUp/PageDown</div>
                <div class="step">2. 调用 <code>loadDateData(date)</code> 函数</div>
                <div class="step">3. 从内存或重新fetch对应的JSON文件</div>
                <div class="step">4. 重新渲染表格，保持筛选状态</div>
            </div>
        </div>
        
        <h2>📝 数据更新操作指南</h2>
        
        <div class="section">
            <h3>3.1 更新现有日期数据</h3>
            <div class="flow-item">
                <strong class="auto">✅ 自动更新（无需额外操作）</strong>
                <div class="step">1. 直接替换 <code>data/date_YYYY-MM-DD.json</code> 文件</div>
                <div class="step">2. 刷新网页（F5）即可看到新数据</div>
                <div class="step">3. 如果数据没有更新，使用强制刷新（Ctrl+F5）</div>
                
                <div class="code">
# 示例：更新2025-07-03的数据
# 1. 将新的股票数据保存为 date_2025-07-03.json
# 2. 替换 excel分析/reports/data/date_2025-07-03.json
# 3. 刷新网页即可
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>3.2 添加新日期数据</h3>
            <div class="flow-item">
                <strong class="manual">❌ 需要手动更新index.json</strong>
                <div class="step">1. 添加新的日期数据文件 <code>data/date_YYYY-MM-DD.json</code></div>
                <div class="step">2. 更新 <code>data/index.json</code> 文件</div>
                <div class="step">3. 刷新网页查看新日期</div>
                
                <div class="code">
# 示例：添加2025-07-04的数据
# 1. 创建 date_2025-07-04.json 文件
# 2. 更新 index.json：
{
  "last_updated": "2025-07-23T22:06:20.741072",
  "available_dates": [
    "2025-07-01",
    "2025-07-02", 
    "2025-07-03",
    "2025-07-04"  ← 添加新日期
  ],
  "total_dates": 4  ← 更新总数
}
                </div>
            </div>
        </div>
        
        <h2>🤖 自动化更新方案</h2>
        
        <div class="section">
            <h3>4.1 Python自动更新脚本</h3>
            <div class="flow-item">
                <strong>功能：</strong>自动扫描data目录，更新index.json文件
                <div class="code">
import json
import os
from datetime import datetime
import re

def update_data_index():
    data_dir = "excel分析/reports/data"
    index_file = os.path.join(data_dir, "index.json")
    
    # 扫描所有日期文件
    date_files = []
    for file in os.listdir(data_dir):
        if file.startswith("date_") and file.endswith(".json"):
            # 提取日期
            match = re.search(r'date_(\d{4}-\d{2}-\d{2})\.json', file)
            if match:
                date_files.append(match.group(1))
    
    # 排序日期
    date_files.sort()
    
    # 更新index.json
    index_data = {
        "last_updated": datetime.now().isoformat(),
        "available_dates": date_files,
        "total_dates": len(date_files)
    }
    
    with open(index_file, 'w', encoding='utf-8') as f:
        json.dump(index_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 已更新index.json，包含{len(date_files)}个日期")
    return date_files

if __name__ == "__main__":
    dates = update_data_index()
    print("📅 可用日期：", dates)
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>4.2 批处理脚本（Windows）</h3>
            <div class="flow-item">
                <div class="code">
@echo off
echo 正在更新复盘数据索引...
cd /d "excel分析\reports"
python update_data_index.py
echo 数据索引更新完成！
pause
                </div>
            </div>
        </div>
        
        <h2>🔍 数据格式要求</h2>
        
        <div class="section">
            <h3>5.1 日期数据文件格式</h3>
            <div class="flow-item">
                <strong>文件名格式：</strong><code>date_YYYY-MM-DD.json</code>
                <div class="code">
[
  {
    "代码": "000001",
    "名称": "平安银行", 
    "涨幅%": "2.34",
    "量比": "1.25",
    "次日量比": "0.98",
    // ... 其他字段
  },
  // ... 更多股票数据
]
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>5.2 数据过滤机制</h3>
            <div class="flow-item">
                <strong class="auto">✅ 自动过滤功能：</strong>
                <div class="step">• 自动过滤包含"数据来源通达信"的行</div>
                <div class="step">• 支持复杂的筛选条件（涨幅、量比等）</div>
                <div class="step">• 筛选状态在日期切换时自动保持</div>
            </div>
        </div>
        
        <h2>⚡ 快速操作指南</h2>
        
        <div class="section">
            <h3>6.1 日常数据更新</h3>
            <div class="flow-item">
                <strong>更新现有日期数据：</strong>
                <div class="step">1. 替换对应的 <code>date_YYYY-MM-DD.json</code> 文件</div>
                <div class="step">2. 刷新网页（F5）</div>
                <div class="step">3. 验证数据是否正确显示</div>
            </div>
            
            <div class="flow-item">
                <strong>添加新日期数据：</strong>
                <div class="step">1. 添加新的 <code>date_YYYY-MM-DD.json</code> 文件</div>
                <div class="step">2. 运行自动更新脚本或手动更新 <code>index.json</code></div>
                <div class="step">3. 刷新网页查看新日期选项</div>
            </div>
        </div>
        
        <div class="section">
            <h3>6.2 故障排除</h3>
            <div class="flow-item">
                <strong>数据没有更新：</strong>
                <div class="step">• 使用强制刷新（Ctrl+F5）清除缓存</div>
                <div class="step">• 检查JSON文件格式是否正确</div>
                <div class="step">• 查看浏览器控制台是否有错误信息</div>
                
                <strong>新日期没有显示：</strong>
                <div class="step">• 检查 <code>index.json</code> 是否包含新日期</div>
                <div class="step">• 确认文件名格式正确：<code>date_YYYY-MM-DD.json</code></div>
                <div class="step">• 运行自动更新脚本重新生成索引</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="复盘分析_精简版.html" class="btn">🔄 返回主页面</a>
            <button class="btn" onclick="location.reload()">🔄 刷新当前页面</button>
        </div>
    </div>
    
    <script>
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 数据更新流程指南已加载');
            
            // 检查当前数据状态
            fetch('data/index.json')
                .then(response => response.json())
                .then(data => {
                    console.log('📅 当前可用日期：', data.available_dates);
                    console.log('📊 最后更新时间：', data.last_updated);
                })
                .catch(error => {
                    console.error('❌ 无法加载数据索引：', error);
                });
        });
    </script>
</body>
</html>
