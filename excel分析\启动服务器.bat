@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    通达信复盘分析系统 - 本地服务器
echo ========================================
echo.

cd /d "%~dp0\reports"

echo 🚀 正在启动本地HTTP服务器...
echo 📁 服务目录: %CD%
echo 🌐 访问地址: http://localhost:8000
echo.
echo ⚠️  请保持此窗口打开，关闭窗口将停止服务器
echo 💡 按 Ctrl+C 可以停止服务器
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo 请确保已安装Python并添加到PATH环境变量
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 启动HTTP服务器
echo 🔄 启动服务器中...

REM 在后台启动浏览器
echo 🌐 正在打开浏览器...
timeout /t 3 >nul
start http://localhost:8000/复盘分析.html

echo.
echo ✅ 服务器启动成功！
echo 📱 如果浏览器没有自动打开，请手动访问: http://localhost:8000/复盘分析.html
echo.

REM 尝试Python 3的http.server
python -m http.server 8000 2>nul
if errorlevel 1 (
    REM 如果失败，尝试Python 2的SimpleHTTPServer
    echo 尝试Python 2兼容模式...
    python -m SimpleHTTPServer 8000
)

echo.
echo 服务器已停止
pause
