/**
 * 筛选引擎模块 - 独立的筛选功能实现
 * 
 * 功能特性：
 * - 条件解析器：支持复杂的筛选语法
 * - 筛选执行器：高效的数据筛选处理
 * - 排序处理器：支持排序筛选语法
 * - 状态管理：筛选状态的保存和恢复
 * 
 * 作者：Augment Agent
 * 日期：2025-07-29
 * 版本：1.0.0
 */

(function(global) {
    'use strict';

    /**
     * 筛选引擎主类
     */
    class FilterEngine {
        constructor() {
            this.parseCache = new Map();
            this.debug = true;
        }

        /**
         * 解析筛选条件
         * @param {string} condition - 筛选条件字符串
         * @returns {Object|null} 解析后的条件对象
         */
        parseFilterCondition(condition) {
            if (!condition || typeof condition !== 'string') {
                return null;
            }

            const trimmed = condition.trim();
            if (!trimmed) {
                return null;
            }

            try {
                return this.parseExpression(trimmed, ['AND', 'OR']);
            } catch (error) {
                throw new Error(`条件解析失败: ${error.message}`);
            }
        }

        /**
         * 解析表达式（支持括号）
         * @param {string} expression - 表达式字符串
         * @param {Array} operators - 操作符数组
         * @returns {Object} 解析结果
         */
        parseExpression(expression, operators) {
            // 处理括号
            while (expression.includes('(')) {
                const start = expression.lastIndexOf('(');
                const end = expression.indexOf(')', start);

                if (end === -1) {
                    throw new Error('括号不匹配');
                }

                const innerExpression = expression.substring(start + 1, end);
                const innerResult = this.parseSimpleExpression(innerExpression, operators);

                // 用占位符替换括号内容
                const placeholder = `__PLACEHOLDER_${Math.random().toString(36).substr(2, 9)}__`;
                expression = expression.substring(0, start) + placeholder + expression.substring(end + 1);

                // 保存解析结果
                this.parseCache.set(placeholder, innerResult);
            }

            return this.parseSimpleExpression(expression, operators);
        }

        /**
         * 解析简单表达式
         * @param {string} expression - 表达式字符串
         * @param {Array} operators - 操作符数组
         * @returns {Object} 解析结果
         */
        parseSimpleExpression(expression, operators) {
            for (const operator of operators) {
                const parts = expression.split(new RegExp(`\\s+${operator}\\s+`, 'i'));
                if (parts.length > 1) {
                    const conditions = parts.map(part => {
                        const trimmed = part.trim();
                        if (trimmed.startsWith('__PLACEHOLDER_')) {
                            return this.parseCache.get(trimmed);
                        }
                        return this.parseCondition(trimmed);
                    });

                    return {
                        type: operator.toUpperCase(),
                        conditions: conditions
                    };
                }
            }

            // 单个条件
            return this.parseCondition(expression.trim());
        }

        /**
         * 解析单个条件
         * @param {string} condition - 条件字符串
         * @returns {Object} 条件对象
         */
        parseCondition(condition) {
            // 检查是否为排序筛选语法：字段名 倒序前N 或 字段名 正序前N
            const sortPattern = /^(.+?)\s+(倒序前|正序前)(\d+)$/;
            const sortMatch = condition.match(sortPattern);

            if (sortMatch) {
                const field = sortMatch[1].trim();
                const sortType = sortMatch[2];
                const count = parseInt(sortMatch[3]);

                return {
                    type: 'sort',
                    field: field,
                    direction: sortType === '倒序前' ? 'desc' : 'asc',
                    count: count
                };
            }

            // 原有的比较运算符处理
            const operators = ['>=', '<=', '!=', '>', '<', '='];

            for (const op of operators) {
                const index = condition.indexOf(op);
                if (index > 0) {
                    const field = condition.substring(0, index).trim();
                    const value = condition.substring(index + op.length).trim();

                    return {
                        field: field,
                        operator: op,
                        value: this.parseValue(value)
                    };
                }
            }

            throw new Error(`无效的条件格式: ${condition}`);
        }

        /**
         * 解析值
         * @param {string} value - 值字符串
         * @returns {string|number} 解析后的值
         */
        parseValue(value) {
            // 移除引号
            if ((value.startsWith('"') && value.endsWith('"')) ||
                (value.startsWith("'") && value.endsWith("'"))) {
                return value.slice(1, -1);
            }

            // 尝试解析为数字
            const num = parseFloat(value);
            if (!isNaN(num)) {
                return num;
            }

            return value;
        }

        /**
         * 应用筛选和排序
         * @param {Array} data - 数据数组
         * @param {Object} filterConditions - 筛选条件
         * @returns {Array} 筛选后的数据
         */
        applyFilterAndSort(data, filterConditions) {
            if (!filterConditions) {
                this.log('🔍 [筛选排序] 无筛选条件，返回原始数据');
                return [...data];
            }

            // 检查是否包含排序条件
            const sortCondition = this.extractSortCondition(filterConditions);

            if (sortCondition) {
                this.log(`🔍 [筛选排序] 发现排序条件: ${sortCondition.field} ${sortCondition.direction === 'desc' ? '倒序' : '正序'}前${sortCondition.count}`);

                // 如果有排序条件，先应用非排序筛选，再排序并取前N条
                const nonSortConditions = this.removeSortCondition(filterConditions);

                // 应用非排序筛选
                let filteredData = data;
                if (nonSortConditions) {
                    filteredData = data.filter(row => this.evaluateFilterConditions(row, nonSortConditions));
                    this.log(`🔍 [筛选排序] 非排序筛选后: ${data.length} -> ${filteredData.length} 条记录`);
                } else {
                    this.log(`🔍 [筛选排序] 无非排序筛选条件，使用全部数据: ${data.length} 条记录`);
                }

                // 应用排序
                const sortedData = [...filteredData].sort((a, b) => {
                    const aValue = this.getFieldValue(a, sortCondition.field);
                    const bValue = this.getFieldValue(b, sortCondition.field);

                    const aNum = parseFloat(aValue) || 0;
                    const bNum = parseFloat(bValue) || 0;

                    if (sortCondition.direction === 'desc') {
                        return bNum - aNum;
                    } else {
                        return aNum - bNum;
                    }
                });

                // 取前N条
                const result = sortedData.slice(0, sortCondition.count);
                this.log(`🔍 [筛选排序] 排序并取前${sortCondition.count}条: ${filteredData.length} -> ${result.length} 条记录`);
                return result;
            } else {
                // 没有排序条件，只应用普通筛选
                const result = data.filter(row => this.evaluateFilterConditions(row, filterConditions));
                this.log(`🔍 [筛选排序] 普通筛选: ${data.length} -> ${result.length} 条记录`);
                return result;
            }
        }

        /**
         * 提取排序条件
         * @param {Object} filterConditions - 筛选条件
         * @returns {Object|null} 排序条件
         */
        extractSortCondition(filterConditions) {
            if (!filterConditions) return null;

            // 单个条件
            if (filterConditions.type === 'sort') {
                return filterConditions;
            }

            // 复合条件中查找排序条件
            if (filterConditions.conditions) {
                for (const condition of filterConditions.conditions) {
                    const sortCondition = this.extractSortCondition(condition);
                    if (sortCondition) return sortCondition;
                }
            }

            return null;
        }

        /**
         * 移除排序条件，保留其他筛选条件
         * @param {Object} filterConditions - 筛选条件
         * @returns {Object|null} 移除排序条件后的筛选条件
         */
        removeSortCondition(filterConditions) {
            if (!filterConditions) return null;

            // 单个条件
            if (filterConditions.type === 'sort') {
                return null;
            }

            if (!filterConditions.conditions) {
                return filterConditions;
            }

            // 复合条件中移除排序条件
            const newConditions = [];
            for (const condition of filterConditions.conditions) {
                const filtered = this.removeSortCondition(condition);
                if (filtered) {
                    newConditions.push(filtered);
                }
            }

            if (newConditions.length === 0) {
                return null;
            } else if (newConditions.length === 1) {
                return newConditions[0];
            } else {
                return {
                    type: filterConditions.type,
                    conditions: newConditions
                };
            }
        }

        /**
         * 评估筛选条件
         * @param {Object} record - 数据记录
         * @param {Object} filterConditions - 筛选条件
         * @returns {boolean} 是否满足条件
         */
        evaluateFilterConditions(record, filterConditions) {
            if (!filterConditions) {
                return true;
            }

            // 排序条件不参与筛选评估
            if (filterConditions.type === 'sort') {
                return true;
            }

            // 如果是单个条件（没有type属性）
            if (!filterConditions.type) {
                return this.evaluateCondition(record, filterConditions);
            }

            // 处理AND条件
            if (filterConditions.type === 'AND') {
                for (const condition of filterConditions.conditions) {
                    if (!this.evaluateFilterConditions(record, condition)) {
                        return false;
                    }
                }
                return true;
            }

            // 处理OR条件
            if (filterConditions.type === 'OR') {
                for (const condition of filterConditions.conditions) {
                    if (this.evaluateFilterConditions(record, condition)) {
                        return true;
                    }
                }
                return false;
            }

            return true;
        }

        /**
         * 评估单个条件
         * @param {Object} record - 数据记录
         * @param {Object} condition - 条件对象
         * @returns {boolean} 是否满足条件
         */
        evaluateCondition(record, condition) {
            const fieldValue = this.getFieldValue(record, condition.field);
            const conditionValue = condition.value;

            switch (condition.operator) {
                case '>':
                    return parseFloat(fieldValue) > parseFloat(conditionValue);
                case '<':
                    return parseFloat(fieldValue) < parseFloat(conditionValue);
                case '>=':
                    return parseFloat(fieldValue) >= parseFloat(conditionValue);
                case '<=':
                    return parseFloat(fieldValue) <= parseFloat(conditionValue);
                case '=':
                    return fieldValue == conditionValue;
                case '!=':
                    return fieldValue != conditionValue;
                default:
                    return false;
            }
        }

        /**
         * 获取字段值
         * @param {Object} record - 数据记录
         * @param {string} fieldName - 字段名
         * @returns {*} 字段值
         */
        getFieldValue(record, fieldName) {
            // 直接字段匹配
            if (record.hasOwnProperty(fieldName)) {
                return record[fieldName];
            }

            // 模糊匹配（忽略大小写和空格）
            const normalizedFieldName = fieldName.toLowerCase().replace(/\s+/g, '');
            for (const key in record) {
                if (key.toLowerCase().replace(/\s+/g, '') === normalizedFieldName) {
                    return record[key];
                }
            }

            return null;
        }

        /**
         * 日志输出
         * @param {string} message - 日志消息
         */
        log(message) {
            if (this.debug && console && console.log) {
                console.log(message);
            }
        }

        /**
         * 设置调试模式
         * @param {boolean} enabled - 是否启用调试
         */
        setDebug(enabled) {
            this.debug = enabled;
        }

        /**
         * 清理缓存
         */
        clearCache() {
            this.parseCache.clear();
        }
    }

    /**
     * 筛选状态管理器
     */
    class FilterStateManager {
        constructor() {
            this.state = {
                isActive: false,
                query: '',
                filteredData: null
            };
        }

        /**
         * 保存筛选状态
         * @param {string} query - 筛选查询
         * @param {Array} filteredData - 筛选后的数据
         * @param {boolean} isActive - 是否激活
         */
        saveState(query, filteredData, isActive = true) {
            this.state = {
                isActive: isActive,
                query: query || '',
                filteredData: filteredData || null
            };
        }

        /**
         * 获取筛选状态
         * @returns {Object} 筛选状态
         */
        getState() {
            return { ...this.state };
        }

        /**
         * 清除筛选状态
         */
        clearState() {
            this.state = {
                isActive: false,
                query: '',
                filteredData: null
            };
        }

        /**
         * 检查是否有活动的筛选
         * @returns {boolean} 是否有活动筛选
         */
        hasActiveFilter() {
            return this.state.isActive && this.state.query;
        }
    }

    // 导出模块
    const filterEngine = new FilterEngine();
    const filterStateManager = new FilterStateManager();

    // 兼容不同的模块系统
    if (typeof module !== 'undefined' && module.exports) {
        // Node.js
        module.exports = {
            FilterEngine,
            FilterStateManager,
            filterEngine,
            filterStateManager
        };
    } else if (typeof define === 'function' && define.amd) {
        // AMD
        define(function() {
            return {
                FilterEngine,
                FilterStateManager,
                filterEngine,
                filterStateManager
            };
        });
    } else {
        // 浏览器全局变量
        global.FilterEngine = FilterEngine;
        global.FilterStateManager = FilterStateManager;
        global.filterEngine = filterEngine;
        global.filterStateManager = filterStateManager;
    }

})(typeof window !== 'undefined' ? window : this);
