<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选功能测试页 - 模块化版本</title>
    
    <!-- 现有样式文件 -->
    <link rel="stylesheet" href="date_pagination_styles.css">
    
    <!-- ECharts 图表库 - 使用本地文件 -->
    <script src="echarts.min.js" onerror="console.warn('echarts.min.js 加载失败')"></script>

    <!-- 筛选引擎模块 -->
    <script src="filter-engine.js" onerror="console.error('filter-engine.js 加载失败，测试页面无法正常工作')"></script>
    
    <style>
        /* 基础样式 */
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            min-height: 100vh;
            overflow-x: hidden;
        }

        html {
            height: 100%;
            overflow-x: hidden;
        }

        .main-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
        }

        .header-section {
            flex-shrink: 0;
            background: #fff;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .content-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }



        .test-info {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 20px;
            font-size: 14px;
        }

        .test-info h4 {
            margin: 0 0 8px 0;
            color: #0c5460;
        }

        .test-info ul {
            margin: 5px 0;
            padding-left: 20px;
        }

        .test-info li {
            margin: 3px 0;
        }

        /* 状态栏样式 */
        .status-section {
            background: #fff;
            padding: 10px 20px;
            border-top: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #6c757d;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* K线图容器 - 嵌入式显示 */
        .kline-section {
            flex-shrink: 0;
            background: #1e1e1e; /* 深色背景匹配K线图 */
            border: none;
            display: none; /* 默认隐藏 */
            position: relative;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }

        .kline-section.active {
            display: block;
        }

        /* K线图容器内部样式 */
        #klineChartContainer {
            width: 100%;
            height: 100%;
            background: #1e1e1e;
            border: none;
            margin: 0;
            padding: 0 10px;
            box-sizing: border-box;
        }

        /* 当K线图显示时的布局调整 - 完全清理模式 */
        .layout-with-kline .kline-section {
            height: 50vh;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
        }

        .layout-with-kline .table-section {
            height: 50vh;
            position: absolute;
            top: 50vh;
            left: 0;
            right: 0;
            min-height: 400px;
        }

        /* K线图模式下的表格优化 */
        .layout-with-kline .table-wrapper {
            max-height: calc(50vh - 40px);
            min-height: 480px;
            overflow-y: auto;
        }

        /* K线图显示时隐藏UI元素 */
        .layout-with-kline .header-section {
            transform: translateY(-100%);
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s ease;
        }

        /* K线图显示时调整内容区域 - 充分利用屏幕空间 */
        .layout-with-kline .content-section {
            height: 100vh;
            margin: 0;
            border-radius: 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 5;
            background: #1a1a1a;
        }

        /* 确保K线图从屏幕最顶部开始 */
        .layout-with-kline .main-container {
            height: 100vh;
            overflow: hidden;
        }

        /* K线图模式下隐藏筛选面板 */
        .layout-with-kline .filter-panel {
            display: none !important;
        }

        /* 表格样式 */
        .table-section {
            flex: 1;
            background: #fff;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .table-wrapper {
            flex: 1;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin: 10px;
            /* 使用相对定位来控制滚动条位置 */
            position: relative;
            min-height: 400px; /* 增加最小高度确保滚动条出现 */
            max-height: 80vh; /* 恢复高度限制，但增加到80vh以利用更多空间 */
            display: flex;
            flex-direction: column;
        }

        /* 表格内容区域 */
        .table-content {
            flex: 1;
            overflow: auto !important; /* 显示所有滚动条 */
            scrollbar-width: auto !important;
            -webkit-overflow-scrolling: touch;
        }

        /* 垂直滚动条样式 */
        .table-content::-webkit-scrollbar {
            width: 14px !important;
            display: block !important;
        }

        .table-content::-webkit-scrollbar-track {
            background: #f1f1f1 !important;
            border-radius: 7px;
            display: block !important;
        }

        .table-content::-webkit-scrollbar-thumb {
            background: #888 !important;
            border-radius: 7px;
            display: block !important;
            min-height: 20px; /* 确保滚动条拖拽块有最小高度 */
        }

        .table-content::-webkit-scrollbar-thumb:hover {
            background: #555 !important;
        }

        table.dataframe {
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
            background: white;
            /* 确保表格有足够高度触发滚动条 */
            min-height: 500px;
        }

        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
            white-space: nowrap;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
            cursor: pointer;
            user-select: none;
            white-space: nowrap;
            border: 1px solid #dee2e6;
            padding: 12px 15px;
            text-align: center;
            font-size: 13px;
        }

        /* 排序指示器 */
        th .sort-indicator {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 10px;
            opacity: 0.3;
            transition: opacity 0.2s ease;
        }

        th.sort-asc .sort-indicator::after {
            content: '▲';
            opacity: 1;
            color: #007bff;
        }

        th.sort-desc .sort-indicator::after {
            content: '▼';
            opacity: 1;
            color: #007bff;
        }

        th:hover .sort-indicator {
            opacity: 1;
        }

        /* 列焦点样式 - 极简设计 */
        .column-focused {
            background-color: #f8f9fa !important;
            position: relative;
        }

        /* 表头列焦点样式 - 极简设计 */
        th.column-focused {
            background-color: #e9ecef !important;
            font-weight: bold;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        /* 高亮行样式 */
        table.dataframe tbody tr.highlighted-row {
            background-color: #f0f8ff !important;
            border-left: 4px solid #2196F3;
        }

        /* 筛选面板样式 */
        .filter-panel {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 20px;
            display: none;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e1e5e9;
            backdrop-filter: blur(10px);
        }

        .filter-panel.active {
            display: block;
            animation: slideDownFromTop 0.3s ease;
        }

        .filter-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .filter-title {
            font-weight: 600;
            color: #374151;
            font-size: 16px;
            white-space: nowrap;
        }

        .filter-input-group {
            flex: 1;
            min-width: 300px;
        }

        .filter-input-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .filter-input-group input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .filter-buttons {
            display: flex;
            gap: 8px;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #fff;
            color: #374151;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .filter-btn:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .filter-btn.primary {
            background: #3b82f6;
            color: #fff;
            border-color: #3b82f6;
        }

        .filter-btn.primary:hover {
            background: #2563eb;
        }

        .filter-presets {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .preset-btn {
            background: #f3f4f6;
            border: 1px solid #e5e7eb;
            color: #6b7280;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .preset-btn:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .filter-close {
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background 0.2s ease;
            color: #6b7280;
        }

        .filter-close:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .filter-help-toggle {
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background 0.2s ease;
            color: #6b7280;
            margin-left: 8px;
        }

        .filter-help-toggle:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .filter-help {
            margin-top: 10px;
            padding: 12px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 12px;
        }

        .help-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .help-section {
            margin-bottom: 6px;
            line-height: 1.4;
        }

        .help-section strong {
            color: #1f2937;
        }

        .help-example {
            color: #6b7280;
            font-style: italic;
            display: block;
            margin-top: 2px;
        }

        .filter-status {
            position: fixed;
            top: 85px;
            right: 20px;
            z-index: 1001;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            max-width: 300px;
            display: none;
        }

        .filter-status.active {
            display: block;
        }

        .filter-status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .filter-status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        /* 动画效果 */
        @keyframes slideDownFromTop {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* 当筛选面板显示时，为内容区域添加顶部边距 */
        .filter-panel.active ~ .main-container .content-section {
            margin-top: 80px;
            height: calc(100vh - 80px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .filter-content {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-input-group {
                min-width: auto;
            }
            
            .filter-presets {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>


    <!-- 筛选面板 -->
    <div class="filter-panel" id="filterPanel">
        <div class="filter-content">
            <div class="filter-title">🔍 筛选</div>

            <div class="filter-input-group">
                <input type="text" id="filterInput" placeholder="例如: 涨幅% > 9.5 AND 量比 > 2 或 振幅 倒序前10 或 成交量 正序前20" onkeypress="handleFilterKeyPress(event)">
            </div>

            <div class="filter-buttons">
                <button onclick="applyFilter()" class="filter-btn primary">应用</button>
                <button onclick="clearFilter()" class="filter-btn">清除</button>
            </div>

            <div class="filter-presets">
                <button onclick="setPresetFilter('量比 > 2')" class="preset-btn">量比>2</button>
                <button onclick="setPresetFilter('振幅 正序前10')" class="preset-btn">振幅前10</button>
                <button onclick="setPresetFilter('倍55 倒序前10')" class="preset-btn">倍55前10</button>
                <button onclick="setPresetFilter('hupd33 倒序前10')" class="preset-btn">hupd33前10</button>
                <button onclick="setPresetFilter('hupd55 倒序前10')" class="preset-btn">hupd55前10</button>
                <button onclick="setPresetFilter('低量 倒序前10')" class="preset-btn">低量前10</button>
            </div>

            <div class="filter-help" id="filterHelp" style="display: none;">
                <div class="help-title">📖 筛选语法说明</div>
                <div class="help-content">
                    <div class="help-section">
                        <strong>比较筛选：</strong> 字段名 运算符 值<br>
                        <span class="help-example">例如：涨幅% > 9.5, 量比 >= 2, 名称 = "平安银行"</span>
                    </div>
                    <div class="help-section">
                        <strong>排序筛选：</strong> 字段名 倒序前N 或 字段名 正序前N<br>
                        <span class="help-example">例如：振幅 倒序前10, 成交量 正序前20</span>
                    </div>
                    <div class="help-section">
                        <strong>组合条件：</strong> 使用 AND、OR 连接多个条件<br>
                        <span class="help-example">例如：涨幅% > 5 AND 振幅 倒序前15</span>
                    </div>
                </div>
            </div>

            <span class="filter-close" onclick="toggleFilterSection()">&times;</span>
            <span class="filter-help-toggle" onclick="toggleFilterHelp()" title="显示/隐藏语法说明">❓</span>
        </div>
    </div>

    <!-- 筛选状态显示 -->
    <div class="filter-status" id="filterStatus"></div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">筛选功能测试页</div>
            <div class="header-controls">
                <select id="dateSelector" onchange="handleDateChange(event)">
                    <option value="">选择日期...</option>
                </select>
                <button onclick="toggleFilterSection()" class="filter-btn">🔍 筛选</button>
                <button onclick="refreshData()" class="filter-btn">🔄 刷新</button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section" id="contentSection">
            <!-- K线图区域 - 嵌入式显示 -->
            <div class="kline-section" id="klineSection">
                <div class="loading-indicator" id="klineLoadingIndicator" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; z-index: 100;">
                    <div style="text-align: center;">正在加载K线图...</div>
                </div>
                <div id="klineChartContainer" style="width: 100%; height: 100%;"></div>
            </div>

            <!-- 表格区域 -->
            <div class="table-section" id="tableSection">
                <div class="table-wrapper">
                    <div class="table-content">
                        <table class="dataframe" id="dataTable">
                            <thead id="tableHead">
                                <tr id="tableHeader">
                                    <!-- 表头将动态生成 -->
                                </tr>
                            </thead>
                            <tbody id="tableBody">
                                <!-- 数据将动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-section">
            <div class="status-left">
                <span>📊 当前日期: <span id="currentDate">-</span></span>
                <span>📈 记录数: <span id="recordCount">0</span></span>
                <span>🔍 筛选状态: <span id="filterStatusText">无</span></span>
            </div>
            <div class="status-right">
                <span id="statusText">准备就绪</span>
                <span>⏰ <span id="currentTime">--:--:--</span></span>
            </div>
        </div>
    </div>

    <!-- 只加载必要的JS模块 -->
    <script src="table_sorter.js"></script>
    
    <script>
        // 测试页面应用状态
        const testApp = {
            currentDate: null,
            currentData: null,
            originalData: null,
            indexData: null,
            highlightedIndex: -1,
            klineManager: null,
            // 使用模块化的筛选状态管理器
            searchState: filterStateManager,
            // 排序状态管理
            sortState: {
                column: -1,
                direction: 'asc'
            },
            // 表格导航状态
            currentFocusColumn: 0
        };

        // 全局变量以兼容现有JS模块
        window.globalHighlightedIndex = -1;
        window.globalKlineManager = null;
        window.globalDatePaginationManager = {
            setSortState: function(column, direction) {
                testApp.sortState = { column: column, direction: direction };
                console.log(`📊 排序状态已保存: 列${column}, 方向${direction}`);
            },
            _isApplyingSort: false
        };
        window.EMBEDDED_DATA = {};

        // 数据缓存
        const dataCache = new Map();

        console.log('🧪 [测试页面] 筛选功能测试页初始化开始...');
        console.log('🔧 [测试页面] 使用模块化筛选引擎:', typeof filterEngine);
        console.log('🔧 [测试页面] 使用模块化状态管理器:', typeof filterStateManager);

        // 初始化K线图管理器
        function initializeKlineManager() {
            if (typeof echarts === 'undefined') {
                console.warn('❌ ECharts库未加载，K线图功能将不可用');
                testApp.klineManager = {
                    chart: null,
                    show: function() { console.warn('K线图功能不可用'); },
                    hide: function() {},
                    init: function() {}
                };
                return;
            }

            console.log('✅ ECharts库已加载，版本:', echarts.version || 'unknown');

            testApp.klineManager = {
                chart: null,
                currentStock: null,
                dataCache: new Map(),

                init: function() {
                    const container = document.getElementById('klineChartContainer');
                    if (container) {
                        this.chart = echarts.init(container, 'dark');
                        console.log('✅ K线图初始化完成');
                    }
                },

                show: function(code, name, date) {
                    this.currentStock = { code, name, date };
                    this.showKlineSection();
                    this.loadKlineData(code, name, date);
                },

                hide: function() {
                    this.hideKlineSection();
                    this.currentStock = null;
                },

                showKlineSection: function() {
                    const klineSection = document.getElementById('klineSection');
                    const contentSection = document.getElementById('contentSection');
                    const mainContainer = document.querySelector('.main-container');

                    if (klineSection && contentSection && mainContainer) {
                        klineSection.classList.add('active');
                        contentSection.classList.add('layout-with-kline');
                        mainContainer.classList.add('layout-with-kline');

                        setTimeout(() => {
                            if (this.chart) {
                                this.chart.resize();
                            }
                        }, 100);

                        console.log('✅ K线图区域显示');
                    }
                },

                hideKlineSection: function() {
                    const klineSection = document.getElementById('klineSection');
                    const contentSection = document.getElementById('contentSection');
                    const mainContainer = document.querySelector('.main-container');

                    if (klineSection && contentSection && mainContainer) {
                        klineSection.classList.remove('active');
                        contentSection.classList.remove('layout-with-kline');
                        mainContainer.classList.remove('layout-with-kline');

                        console.log('✅ K线图区域隐藏');
                    }
                },

                loadKlineData: async function(code, name, date) {
                    if (!this.chart) return;

                    const loadingIndicator = document.getElementById('klineLoadingIndicator');
                    if (loadingIndicator) {
                        loadingIndicator.style.display = 'block';
                    }

                    this.chart.showLoading();

                    const data = await this.fetchKlineData(code, date);

                    this.chart.hideLoading();

                    if (data) {
                        this.renderChart(data, date, code, name);
                    } else {
                        console.error('❌ K线数据加载失败');
                    }

                    setTimeout(() => {
                        if (loadingIndicator) {
                            loadingIndicator.style.display = 'none';
                        }
                    }, 500);
                },

                fetchKlineData: async function(stockCode, selectedDate) {
                    const cacheKey = `${stockCode}_${selectedDate}`;
                    if (this.dataCache.has(cacheKey)) {
                        return this.dataCache.get(cacheKey);
                    }

                    const market = stockCode.startsWith('6') ? '1' : '0';
                    const secid = `${market}.${stockCode}`;
                    const end = new Date().toISOString().slice(0, 10).replace(/-/g, '');
                    const url = `https://push2his.eastmoney.com/api/qt/stock/kline/get?secid=${secid}&fields1=f1,f2,f3,f4,f5,f6&fields2=f51,f52,f53,f54,f55,f56,f57,f58&klt=101&fqt=1&end=${end}&lmt=240`;

                    try {
                        console.log(`🔍 正在获取K线数据: ${stockCode}`);
                        const response = await fetch(url);
                        const json = await response.json();

                        if (json && json.data && json.data.klines) {
                            const processed = this.processEastMoneyData(json.data);
                            this.dataCache.set(cacheKey, processed);
                            console.log(`✅ K线数据获取成功: ${stockCode}, ${processed.dates.length}条记录`);
                            return processed;
                        }
                    } catch (error) {
                        console.error(`❌ K线数据获取失败: ${stockCode}`, error);
                        return null;
                    }
                },

                processEastMoneyData: function(data) {
                    const dates = data.klines.map(item => item.split(',')[0]);
                    const klineData = data.klines.map(item => {
                        const parts = item.split(',');
                        return [parseFloat(parts[1]), parseFloat(parts[2]), parseFloat(parts[4]), parseFloat(parts[3])];
                    });
                    const volumes = data.klines.map(item => parseInt(item.split(',')[5]));
                    return { dates, klineData, volumes };
                },

                renderChart: function(data, selectedDate, stockCode, stockName) {
                    const selectedIndex = data.dates.indexOf(selectedDate);
                    const actualIndex = selectedIndex !== -1 ? selectedIndex : data.dates.length - 1;

                    const start = Math.max(0, actualIndex - 30);
                    const end = Math.min(data.dates.length, actualIndex + 30);

                    const option = {
                        title: {
                            text: `${stockName} (${stockCode})`,
                            left: 'center',
                            textStyle: { color: '#fff', fontSize: 16 }
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'cross' },
                            backgroundColor: 'rgba(0,0,0,0.8)',
                            textStyle: { color: '#fff' },
                            formatter: function (params) {
                                if (params && params.length > 0) {
                                    const klineParam = params.find(p => p.seriesType === 'candlestick');
                                    if (klineParam && klineParam.data) {
                                        const data = klineParam.data;
                                        const open = data[1];
                                        const close = data[2];
                                        const low = data[3];
                                        const high = data[4];

                                        // 计算涨幅
                                        const change = close - open;
                                        const changePercent = open !== 0 ? ((change / open) * 100) : 0;
                                        const changeColor = change >= 0 ? '#ef4444' : '#22c55e';
                                        const changeSign = change >= 0 ? '+' : '';

                                        return `
                                            <div style="text-align: left;">
                                                <div style="margin-bottom: 5px; font-weight: bold;">${klineParam.axisValue}</div>
                                                <div>开盘: <span style="color: #fff;">${open.toFixed(2)}</span></div>
                                                <div>收盘: <span style="color: #fff;">${close.toFixed(2)}</span></div>
                                                <div>最高: <span style="color: #fff;">${high.toFixed(2)}</span></div>
                                                <div>最低: <span style="color: #fff;">${low.toFixed(2)}</span></div>
                                                <div>涨跌: <span style="color: ${changeColor};">${changeSign}${change.toFixed(2)}</span></div>
                                                <div>涨幅: <span style="color: ${changeColor};">${changeSign}${changePercent.toFixed(2)}%</span></div>
                                            </div>
                                        `;
                                    }
                                }
                                return '';
                            }
                        },
                        grid: [
                            { left: '5%', right: '5%', top: '15%', height: '60%' },
                            { left: '5%', right: '5%', top: '80%', height: '15%' }
                        ],
                        xAxis: [
                            { type: 'category', data: data.dates, gridIndex: 0, axisLine: { lineStyle: { color: '#666' } } },
                            { type: 'category', data: data.dates, gridIndex: 1, axisLine: { lineStyle: { color: '#666' } } }
                        ],
                        yAxis: [
                            { gridIndex: 0, scale: true, axisLine: { lineStyle: { color: '#666' } } },
                            { gridIndex: 1, scale: true, axisLine: { lineStyle: { color: '#666' } } }
                        ],
                        dataZoom: [
                            { type: 'inside', xAxisIndex: [0, 1], start: (start / data.dates.length) * 100, end: (end / data.dates.length) * 100 },
                            { show: true, xAxisIndex: [0, 1], type: 'slider', top: '95%', start: (start / data.dates.length) * 100, end: (end / data.dates.length) * 100 }
                        ],
                        series: [
                            {
                                type: 'candlestick',
                                data: data.klineData,
                                markPoint: actualIndex !== -1 ? {
                                    data: [{
                                        name: 'Selected',
                                        coord: [actualIndex, data.klineData[actualIndex][3]],
                                        symbol: 'pin',
                                        symbolSize: 20,
                                        itemStyle: { color: '#FFD700' }
                                    }]
                                } : undefined
                            },
                            {
                                type: 'bar',
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                data: data.volumes,
                                itemStyle: {
                                    color: ({ dataIndex }) => data.klineData[dataIndex][1] > data.klineData[dataIndex][0] ? '#ef4444' : '#22c55e'
                                }
                            }
                        ]
                    };

                    this.chart.setOption(option);
                    console.log('📊 K线图渲染完成');
                }
            };

            testApp.klineManager.init();
            window.globalKlineManager = testApp.klineManager;
            console.log('✅ K线图管理器初始化完成');
        }

        // 筛选功能实现（使用模块化引擎）
        function toggleFilterSection() {
            const filterPanel = document.getElementById('filterPanel');

            if (filterPanel) {
                if (filterPanel.classList.contains('active')) {
                    filterPanel.classList.remove('active');
                    testApp.searchState.saveState('', null, false);
                    console.log('🔍 [测试页面] 筛选面板已关闭');
                } else {
                    filterPanel.classList.add('active');
                    const filterInput = document.getElementById('filterInput');
                    if (filterInput) {
                        filterInput.focus();
                    }
                    console.log('🔍 [测试页面] 筛选面板已显示');
                }
            }
        }

        function handleFilterKeyPress(event) {
            if (event.key === 'Enter') {
                applyFilter();
            }
        }

        function setPresetFilter(condition) {
            const filterInput = document.getElementById('filterInput');
            if (filterInput) {
                filterInput.value = condition;
                applyFilter();
            }
        }

        function toggleFilterHelp() {
            const helpPanel = document.getElementById('filterHelp');
            if (helpPanel) {
                if (helpPanel.style.display === 'none') {
                    helpPanel.style.display = 'block';
                } else {
                    helpPanel.style.display = 'none';
                }
            }
        }

        function applyFilter() {
            const filterInput = document.getElementById('filterInput');
            const filterStatus = document.getElementById('filterStatus');

            if (!filterInput || !testApp.originalData) {
                return;
            }

            const condition = filterInput.value.trim();

            if (!condition) {
                clearFilter();
                return;
            }

            try {
                const startTime = performance.now();

                console.log('🧪 [测试页面] 使用模块化引擎解析筛选条件:', condition);

                // 使用模块化筛选引擎解析条件
                const parsedConditions = filterEngine.parseFilterCondition(condition);

                // 使用模块化筛选引擎应用筛选和排序
                let filteredData = filterEngine.applyFilterAndSort(testApp.originalData, parsedConditions);

                testApp.currentData = filteredData;
                testApp.searchState.saveState(condition, filteredData, true);

                // 确保筛选面板保持可见状态
                const filterPanel = document.getElementById('filterPanel');
                if (filterPanel && !filterPanel.classList.contains('active')) {
                    filterPanel.classList.add('active');
                    console.log('🔍 [测试页面] 显示筛选面板');
                }

                renderTable(testApp.currentData);

                const endTime = performance.now();
                const duration = (endTime - startTime).toFixed(2);

                if (filterStatus) {
                    filterStatus.className = 'filter-status success active';
                    filterStatus.textContent = `✅ 筛选完成：找到 ${filteredData.length} 条记录 (${duration}ms)`;

                    // 3秒后自动隐藏状态
                    setTimeout(() => {
                        filterStatus.classList.remove('active');
                    }, 3000);
                }

                // 更新状态显示
                updateFilterStatusText(condition, filteredData.length);

                console.log(`🧪 [测试页面] 筛选完成: "${condition}" -> ${filteredData.length}条结果 (${duration}ms)`);

            } catch (error) {
                if (filterStatus) {
                    filterStatus.className = 'filter-status error active';
                    filterStatus.textContent = `❌ 筛选失败: ${error.message}`;

                    setTimeout(() => {
                        filterStatus.classList.remove('active');
                    }, 5000);
                }
                console.error('🧪 [测试页面] 筛选条件解析失败:', error);
            }
        }

        function clearFilter() {
            const filterInput = document.getElementById('filterInput');
            const filterStatus = document.getElementById('filterStatus');

            if (filterInput) {
                filterInput.value = '';
            }

            if (filterStatus) {
                filterStatus.className = 'filter-status';
                filterStatus.textContent = '';
                filterStatus.classList.remove('active');
            }

            // 恢复原始数据
            if (testApp.originalData) {
                testApp.currentData = [...testApp.originalData];
                testApp.searchState.clearState();
                renderTable(testApp.currentData);
                updateFilterStatusText('', testApp.currentData.length);
                console.log('🧪 [测试页面] 筛选已清除，恢复原始数据');
            }
        }

        function updateFilterStatusText(condition, count) {
            const statusElement = document.getElementById('filterStatusText');
            if (statusElement) {
                if (condition) {
                    statusElement.textContent = `已筛选 (${count}条)`;
                } else {
                    statusElement.textContent = '无';
                }
            }
        }

        // 数据加载功能
        async function loadDateData(date) {
            try {
                updateStatus(`正在加载 ${date} 数据...`);

                // 检查缓存
                if (dataCache.has(date)) {
                    console.log(`📦 [测试页面] 使用缓存数据: ${date}`);
                    const cachedData = dataCache.get(date);
                    testApp.currentData = cachedData;
                    testApp.originalData = [...cachedData];
                    testApp.currentDate = date;

                    // 如果有筛选状态，应用筛选
                    const state = testApp.searchState.getState();
                    if (state.query) {
                        try {
                            console.log(`🔍 [测试页面] 应用筛选条件: ${state.query}`);
                            const parsedConditions = filterEngine.parseFilterCondition(state.query);
                            const filteredData = filterEngine.applyFilterAndSort(testApp.originalData, parsedConditions);
                            testApp.currentData = filteredData;

                            // 渲染筛选后的数据
                            renderTable(testApp.currentData);

                            // 确保筛选面板状态保持
                            const filterInput = document.getElementById('filterInput');
                            const filterPanel = document.getElementById('filterPanel');

                            if (filterInput && filterInput.value !== state.query) {
                                filterInput.value = state.query;
                            }

                            // 确保筛选面板保持可见状态
                            if (filterPanel && !filterPanel.classList.contains('active')) {
                                filterPanel.classList.add('active');
                                console.log('🔍 [测试页面] 恢复筛选面板可见状态');
                            }

                            updateFilterStatusText(state.query, filteredData.length);
                            updateStatus(`已加载 ${date} 数据并应用筛选 (${filteredData.length}/${cachedData.length} 条记录)`);
                        } catch (error) {
                            console.error('🧪 [测试页面] 筛选状态恢复失败:', error);
                            renderTable(testApp.currentData);
                            updateStatus(`已加载 ${date} 数据 (${cachedData.length} 条记录)`);
                        }
                    } else {
                        // 没有筛选条件，直接渲染原始数据
                        renderTable(testApp.currentData);
                        updateStatus(`已加载 ${date} 数据 (${cachedData.length} 条记录)`);
                        updateFilterStatusText('', cachedData.length);
                    }

                    document.getElementById('currentDate').textContent = date;
                    document.getElementById('recordCount').textContent = testApp.currentData.length;
                    return;
                }

                // 从服务器加载数据
                const response = await fetch(`data/date_${date}.json`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // 缓存数据
                dataCache.set(date, data);
                console.log(`💾 [测试页面] 已缓存 ${date} 数据`);

                testApp.currentData = data;
                testApp.originalData = [...data];
                testApp.currentDate = date;

                // 如果有筛选状态，应用筛选
                const state = testApp.searchState.getState();
                if (state.query) {
                    try {
                        const parsedConditions = filterEngine.parseFilterCondition(state.query);
                        const filteredData = filterEngine.applyFilterAndSort(testApp.originalData, parsedConditions);
                        testApp.currentData = filteredData;
                        renderTable(testApp.currentData);

                        // 确保筛选面板状态保持
                        const filterInput = document.getElementById('filterInput');
                        const filterPanel = document.getElementById('filterPanel');

                        if (filterInput && filterInput.value !== state.query) {
                            filterInput.value = state.query;
                        }

                        if (filterPanel && !filterPanel.classList.contains('active')) {
                            filterPanel.classList.add('active');
                            console.log('🔍 [测试页面] 恢复筛选面板可见状态');
                        }

                        updateFilterStatusText(state.query, filteredData.length);
                        updateStatus(`已加载 ${date} 数据并应用筛选 (${filteredData.length}/${data.length} 条记录)`);
                    } catch (error) {
                        console.error('🧪 [测试页面] 筛选状态恢复失败:', error);
                        renderTable(testApp.currentData);
                        updateStatus(`已加载 ${date} 数据 (${data.length} 条记录)`);
                    }
                } else {
                    renderTable(testApp.currentData);
                    updateStatus(`已加载 ${date} 数据 (${data.length} 条记录)`);
                    updateFilterStatusText('', data.length);
                }

                document.getElementById('currentDate').textContent = date;
                document.getElementById('recordCount').textContent = testApp.currentData.length;

            } catch (error) {
                console.error('🧪 [测试页面] 数据加载失败:', error);
                updateStatus(`数据加载失败: ${error.message}`);
            }
        }

        // 表格渲染功能
        function renderTable(data) {
            const startTime = performance.now();

            if (!data || data.length === 0) {
                return;
            }

            // 过滤掉包含"数据来源通达信"的行
            const filteredData = data.filter(row => {
                const rowValues = Object.values(row).join('').toLowerCase();
                const shouldFilter = rowValues.includes('数据来源') || rowValues.includes('通达信');
                return !shouldFilter;
            });

            console.log(`📊 [表格渲染] 原始数据: ${data.length}行, 过滤后: ${filteredData.length}行`);

            const headers = Object.keys(filteredData[0]);
            const thead = document.getElementById('tableHead');
            const tbody = document.getElementById('tableBody');

            // 保存列索引到全局变量，供K线图使用
            window.columnIndexes = {
                code: headers.indexOf('代码'),
                name: headers.indexOf('名称')
            };

            const tableHeader = document.getElementById('tableHeader');
            const tableBody = document.getElementById('tableBody');

            // 生成表头（添加排序指示器）
            tableHeader.innerHTML = headers.map(header =>
                `<th>${header}<span class="sort-indicator"></span></th>`
            ).join('');

            // 生成表格内容（使用事件委托，不使用内联事件）
            tableBody.innerHTML = filteredData.map((row, index) => {
                const cells = headers.map(header => {
                    const value = row[header];
                    return `<td>${value !== null && value !== undefined ? value : ''}</td>`;
                }).join('');
                return `<tr data-row-index="${index}">${cells}</tr>`;
            }).join('');

            // 初始化表格排序功能
            const table = document.getElementById('dataTable');
            if (typeof makeTableSortable === 'function') {
                makeTableSortable(table);
                console.log('✅ 表格排序功能已启用');

                // 恢复排序状态
                setTimeout(() => {
                    applySortState();
                }, 100);
            } else {
                console.warn('⚠️ table_sorter.js 未加载，排序功能不可用');
            }

            const endTime = performance.now();
            console.log(`🧪 [测试页面] 表格渲染完成: ${filteredData.length} 条记录 (${(endTime - startTime).toFixed(2)}ms)`);
        }

        // 应用保存的排序状态
        function applySortState() {
            if (testApp.sortState.column >= 0) {
                const table = document.getElementById('dataTable');
                const headers = table.querySelectorAll('th');

                if (headers[testApp.sortState.column] && typeof sortTable === 'function') {
                    console.log(`🔄 恢复排序状态: 列${testApp.sortState.column}, 方向${testApp.sortState.direction}`);

                    // 应用排序
                    sortTable(table, testApp.sortState.column, testApp.sortState.direction);
                    updateHeaderStyles(headers, testApp.sortState.column, testApp.sortState.direction);

                    console.log('✅ 排序状态已恢复');
                } else {
                    console.warn('⚠️ 无法恢复排序状态：表格或排序函数未找到');
                }
            }
        }

        // 行点击事件处理（使用事件委托）
        function handleTableBodyClick(event) {
            const clickedRow = event.target.closest('tr');
            if (!clickedRow) return;

            const rows = document.querySelectorAll('#tableBody tr');
            const actualIndex = Array.from(rows).indexOf(clickedRow);

            console.log(`🖱️ [鼠标点击] DOM行索引: ${actualIndex}`);

            if (actualIndex >= 0) {
                selectRow(actualIndex);
            }
        }

        function selectRow(index) {
            // 清除之前的高亮
            const rows = document.querySelectorAll('#tableBody tr');
            rows.forEach(row => row.classList.remove('highlighted-row'));

            // 高亮当前行
            if (rows[index]) {
                rows[index].classList.add('highlighted-row');
                testApp.highlightedIndex = index;
                window.globalHighlightedIndex = index;

                // 滚动到可见区域
                rows[index].scrollIntoView({ behavior: 'smooth', block: 'nearest' });

                // 显示K线图
                showKlineChart(index);

                console.log(`🧪 [测试页面] 选中行: ${index}`);
            }
        }

        // 绑定行点击事件委托
        function bindRowClickEvents() {
            const tbody = document.getElementById('tableBody');
            if (tbody) {
                tbody.addEventListener('click', handleTableBodyClick);
                console.log('✅ 行点击事件委托已绑定');
            }
        }

        // K线图显示功能（修复排序后的数据索引映射）
        function showKlineChart(domIndex) {
            if (!testApp.klineManager) {
                console.warn('❌ K线图管理器不可用');
                return;
            }

            // 通过DOM索引获取实际的行数据（修复排序后错位问题）
            const rows = document.querySelectorAll('#tableBody tr');
            if (domIndex < 0 || domIndex >= rows.length) {
                console.warn(`⚠️ K线图更新失败: 无效DOM索引 ${domIndex}`);
                return;
            }

            const row = rows[domIndex];
            const cells = row.querySelectorAll('td');
            if (cells.length === 0) {
                console.warn(`⚠️ K线图更新失败: 行无数据`);
                return;
            }

            // 从DOM中提取股票数据（使用动态列索引）
            const codeIndex = window.columnIndexes ? window.columnIndexes.code : 0;
            const nameIndex = window.columnIndexes ? window.columnIndexes.name : 1;

            const stockData = {
                code: cells[codeIndex] ? cells[codeIndex].textContent.trim() : '',
                name: cells[nameIndex] ? cells[nameIndex].textContent.trim() : '',
                date: testApp.currentDate
            };

            console.log(`� [K线图更新] 使用列索引 - 代码:${codeIndex}, 名称:${nameIndex}`);
            console.log(`📊 [K线图] DOM索引: ${domIndex}, 股票数据:`, stockData);

            if (!stockData.code) {
                console.error(`❌ 股票代码为空: DOM索引${domIndex}`);
                return;
            }

            if (!stockData.name) {
                console.error(`❌ 股票名称为空: DOM索引${domIndex}`);
                return;
            }

            console.log(`📈 显示K线图: ${stockData.name} (${stockData.code}) - ${stockData.date}`);
            testApp.klineManager.show(stockData.code, stockData.name, stockData.date);
        }

        // 日期处理功能
        async function handleDateChange(event) {
            const selectedDate = event.target.value;
            if (!selectedDate) return;

            console.log(`📅 [测试页面] 用户选择日期: ${selectedDate}`);
            await loadDateData(selectedDate);
        }

        function refreshData() {
            const currentDate = testApp.currentDate;
            if (currentDate) {
                // 清除缓存并重新加载
                dataCache.delete(currentDate);
                loadDateData(currentDate);
                console.log(`🔄 [测试页面] 刷新数据: ${currentDate}`);
            }
        }

        // 工具函数
        function updateStatus(message) {
            document.getElementById('statusText').textContent = message;
            console.log(`🧪 [测试页面] ${message}`);
        }

        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.getElementById('currentTime').textContent = timeString;
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🧪 [测试页面] 页面加载完成，开始初始化...');
            console.log('🧪 [测试页面] 筛选引擎状态:', typeof filterEngine);
            console.log('🧪 [测试页面] 状态管理器状态:', typeof filterStateManager);

            // 更新时间
            updateTime();
            setInterval(updateTime, 1000);

            try {
                // 加载数据索引
                updateStatus('正在加载数据索引...');
                console.log('🧪 [测试页面] 开始加载数据索引...');

                const indexResponse = await fetch('data/index.json');
                console.log('🧪 [测试页面] 索引响应状态:', indexResponse.status);

                if (!indexResponse.ok) {
                    throw new Error(`数据索引加载失败: HTTP ${indexResponse.status}`);
                }

                testApp.indexData = await indexResponse.json();
                console.log('🧪 [测试页面] 索引数据:', testApp.indexData);

                // 填充日期选择器
                const dateSelector = document.getElementById('dateSelector');
                if (testApp.indexData && testApp.indexData.available_dates) {
                    console.log('🧪 [测试页面] 可用日期:', testApp.indexData.available_dates);

                    testApp.indexData.available_dates.forEach(date => {
                        const option = document.createElement('option');
                        option.value = date;
                        option.textContent = date;
                        dateSelector.appendChild(option);
                    });

                    // 默认选择最新日期
                    if (testApp.indexData.available_dates.length > 0) {
                        const latestDate = testApp.indexData.available_dates[testApp.indexData.available_dates.length - 1];
                        console.log('🧪 [测试页面] 选择最新日期:', latestDate);
                        dateSelector.value = latestDate;
                        await loadDateData(latestDate);
                    }
                } else {
                    console.error('🧪 [测试页面] 索引数据格式错误:', testApp.indexData);
                    throw new Error('索引数据格式错误');
                }

                updateStatus('测试页面初始化完成');
                console.log('🧪 [测试页面] 初始化完成');

                // 初始化K线图管理器
                initializeKlineManager();

                // 绑定键盘导航
                bindKeyboardNavigation();

                // 绑定行点击事件
                bindRowClickEvents();

            } catch (error) {
                console.error('🧪 [测试页面] 初始化失败:', error);
                updateStatus(`初始化失败: ${error.message}`);

                // 显示详细错误信息
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = 'position: fixed; top: 100px; left: 20px; right: 20px; background: #fee; border: 1px solid #f00; padding: 10px; z-index: 9999;';
                errorDiv.innerHTML = `
                    <h3>🚨 初始化错误</h3>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p><strong>可能原因:</strong></p>
                    <ul>
                        <li>数据文件不存在或路径错误</li>
                        <li>服务器未正确启动</li>
                        <li>网络连接问题</li>
                    </ul>
                    <p><strong>解决方法:</strong></p>
                    <ul>
                        <li>确认服务器已启动: python 启动监控服务器.py</li>
                        <li>检查数据文件是否存在: excel分析/reports/data/</li>
                        <li>刷新页面重试</li>
                    </ul>
                    <button onclick="this.parentElement.remove()">关闭</button>
                `;
                document.body.appendChild(errorDiv);
            }
        });

        // 键盘导航功能
        function bindKeyboardNavigation() {
            document.addEventListener('keydown', function(event) {
                // 如果在输入框中，不处理导航键
                if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                    if (event.key === 'Escape') {
                        event.target.blur();
                        return;
                    }
                    return;
                }

                // 处理Ctrl+快捷键（表格导航和排序）
                if (event.ctrlKey && ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(event.key)) {
                    event.preventDefault();
                    handleAdvancedTableNavigation(event);
                    return;
                }

                // 如果K线图显示，ESC键关闭K线图
                if (event.key === 'Escape') {
                    if (testApp.klineManager) {
                        testApp.klineManager.hide();
                    }
                    return;
                }

                if (!testApp.currentData || testApp.currentData.length === 0) {
                    return;
                }

                switch (event.key) {
                    case 'ArrowUp':
                        event.preventDefault();
                        navigateRow(-1);
                        break;
                    case 'ArrowDown':
                        event.preventDefault();
                        navigateRow(1);
                        break;
                    case 'PageUp':
                        event.preventDefault();
                        switchToPreviousDate();
                        break;
                    case 'PageDown':
                        event.preventDefault();
                        switchToNextDate();
                        break;
                    case 'Enter':
                    case ' ':
                        event.preventDefault();
                        if (testApp.highlightedIndex >= 0) {
                            showKlineChart(testApp.highlightedIndex);
                        }
                        break;
                }
            });

            console.log('✅ 键盘导航已绑定');
        }

        // 行导航功能
        function navigateRow(direction) {
            if (!testApp.currentData || testApp.currentData.length === 0) {
                return;
            }

            let newIndex = testApp.highlightedIndex + direction;

            // 边界检查
            if (newIndex < 0) {
                newIndex = 0;
            } else if (newIndex >= testApp.currentData.length) {
                newIndex = testApp.currentData.length - 1;
            }

            // 选中新行
            selectRow(newIndex);

            // 滚动到可见区域
            const rows = document.querySelectorAll('#tableBody tr');
            if (rows[newIndex]) {
                rows[newIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
        }

        // 高级表格导航功能
        function handleAdvancedTableNavigation(event) {
            const table = document.getElementById('dataTable');
            if (!table) return;

            const headers = table.querySelectorAll('th');
            if (headers.length === 0) return;

            switch (event.key) {
                case 'ArrowLeft':
                    // Ctrl + 左箭头：向左移动列焦点
                    navigateColumn(-1, headers);
                    break;
                case 'ArrowRight':
                    // Ctrl + 右箭头：向右移动列焦点
                    navigateColumn(1, headers);
                    break;
                case 'ArrowUp':
                    // Ctrl + 上箭头：对当前列升序排序
                    sortCurrentColumn('asc', headers);
                    break;
                case 'ArrowDown':
                    // Ctrl + 下箭头：对当前列降序排序
                    sortCurrentColumn('desc', headers);
                    break;
            }
        }

        // 列导航功能（快速移动8列）
        function navigateColumn(direction, headers) {
            // 清除当前列高亮
            clearColumnFocus();

            // 计算新的列索引（每次移动8列）
            testApp.currentFocusColumn += (direction * 8);

            // 边界检查
            if (testApp.currentFocusColumn < 0) {
                testApp.currentFocusColumn = 0;
            } else if (testApp.currentFocusColumn >= headers.length) {
                testApp.currentFocusColumn = headers.length - 1;
            }

            // 高亮新的列
            highlightColumn(testApp.currentFocusColumn);

            console.log(`📊 快速列导航: ${direction > 0 ? '向右' : '向左'}移动8列，当前列: ${testApp.currentFocusColumn} (${headers[testApp.currentFocusColumn].textContent})`);
        }

        // 键盘排序功能
        function sortCurrentColumn(direction, headers) {
            if (testApp.currentFocusColumn >= 0 && testApp.currentFocusColumn < headers.length) {
                const table = document.getElementById('dataTable');
                if (table && typeof sortTable === 'function') {
                    // 使用table_sorter.js的排序功能
                    sortTable(table, testApp.currentFocusColumn, direction);
                    updateHeaderStyles(headers, testApp.currentFocusColumn, direction);

                    // 保存排序状态
                    testApp.sortState = {
                        column: testApp.currentFocusColumn,
                        direction: direction
                    };

                    console.log(`📊 键盘排序: 列${testApp.currentFocusColumn} ${direction === 'asc' ? '升序' : '降序'}`);
                } else {
                    console.warn('❌ 排序功能不可用');
                }
            }
        }

        // 高亮指定列
        function highlightColumn(columnIndex) {
            const table = document.getElementById('dataTable');
            if (!table) return;

            // 清除之前的列高亮
            table.querySelectorAll('th, td').forEach(cell => {
                cell.classList.remove('column-focused');
            });

            // 高亮表头
            const headers = table.querySelectorAll('th');
            if (headers[columnIndex]) {
                headers[columnIndex].classList.add('column-focused');
            }

            // 高亮该列的所有单元格
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells[columnIndex]) {
                    cells[columnIndex].classList.add('column-focused');
                }
            });

            // 滚动到可见区域
            if (headers[columnIndex]) {
                headers[columnIndex].scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'center'
                });
            }
        }

        // 清除列焦点高亮
        function clearColumnFocus() {
            const table = document.getElementById('dataTable');
            if (!table) return;

            // 清除所有列焦点样式
            table.querySelectorAll('.column-focused').forEach(element => {
                element.classList.remove('column-focused');
            });
        }

        // 日期切换功能（增强排序状态保持）
        function switchToPreviousDate() {
            const selector = document.getElementById('dateSelector');
            const currentIndex = selector.selectedIndex;
            if (currentIndex > 0) {
                console.log(`📅 [日期切换] 切换到上一日期，当前排序状态: 列${testApp.sortState.column}, 方向${testApp.sortState.direction}`);
                selector.selectedIndex = currentIndex - 1;
                loadDateData(selector.value);
            }
        }

        function switchToNextDate() {
            const selector = document.getElementById('dateSelector');
            const currentIndex = selector.selectedIndex;
            if (currentIndex < selector.options.length - 1) {
                console.log(`📅 [日期切换] 切换到下一日期，当前排序状态: 列${testApp.sortState.column}, 方向${testApp.sortState.direction}`);
                selector.selectedIndex = currentIndex + 1;
                loadDateData(selector.value);
            }
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === 'f') {
                event.preventDefault();
                toggleFilterSection();
            }
        });

        // 暴露函数到全局作用域以兼容HTML onclick事件
        window.toggleFilterSection = toggleFilterSection;
        window.handleFilterKeyPress = handleFilterKeyPress;
        window.setPresetFilter = setPresetFilter;
        window.applyFilter = applyFilter;
        window.clearFilter = clearFilter;
        window.toggleFilterHelp = toggleFilterHelp;
        window.handleDateChange = handleDateChange;
        window.refreshData = refreshData;
        window.selectRow = selectRow;

        console.log('🧪 [测试页面] 所有功能已加载完成');
    </script>
</body>
</html>
