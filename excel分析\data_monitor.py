#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据文件夹自动监控模块
功能：监控data目录变化，自动更新索引文件
作者：AI Assistant
日期：2025-07-23
"""

import os
import json
import time
import re
from datetime import datetime
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import threading
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataIndexManager:
    """数据索引管理器"""
    
    def __init__(self, data_dir="reports/data"):
        self.data_dir = Path(data_dir)
        self.index_file = self.data_dir / "index.json"
        self.lock = threading.Lock()
        
    def scan_date_files(self):
        """扫描日期文件"""
        if not self.data_dir.exists():
            logger.warning(f"数据目录不存在: {self.data_dir}")
            return []
            
        date_files = []
        pattern = re.compile(r'date_(\d{4}-\d{2}-\d{2})\.json')
        
        for file_path in self.data_dir.glob("date_*.json"):
            match = pattern.match(file_path.name)
            if match:
                date_str = match.group(1)
                date_files.append(date_str)
                logger.debug(f"发现日期文件: {file_path.name} -> {date_str}")
        
        return sorted(date_files)
    
    def validate_json_file(self, file_path):
        """验证JSON文件格式"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                return False, "数据不是数组格式"
                
            if len(data) == 0:
                return False, "数据为空"
                
            # 检查必需字段
            first_record = data[0]
            required_fields = ['代码', '名称']
            missing_fields = [field for field in required_fields if field not in first_record]
            
            if missing_fields:
                return False, f"缺少必需字段: {missing_fields}"
                
            return True, f"格式正确，包含{len(data)}条记录"
            
        except json.JSONDecodeError as e:
            return False, f"JSON格式错误: {e}"
        except Exception as e:
            return False, f"文件读取失败: {e}"
    
    def update_index(self):
        """更新索引文件"""
        with self.lock:
            try:
                # 扫描日期文件
                date_files = self.scan_date_files()
                
                if not date_files:
                    logger.warning("未找到有效的日期文件")
                    return False
                
                # 读取现有索引（如果存在）
                existing_data = {}
                if self.index_file.exists():
                    try:
                        with open(self.index_file, 'r', encoding='utf-8') as f:
                            existing_data = json.load(f)
                    except Exception as e:
                        logger.warning(f"读取现有索引失败: {e}")
                
                # 创建新索引
                index_data = {
                    "last_updated": datetime.now().isoformat(),
                    "available_dates": date_files,
                    "total_dates": len(date_files),
                    "data_directory": str(self.data_dir),
                    "file_pattern": "date_YYYY-MM-DD.json",
                    "auto_generated": True
                }
                
                # 保留元数据
                if "metadata" in existing_data:
                    index_data["metadata"] = existing_data["metadata"]
                
                # 写入索引文件
                with open(self.index_file, 'w', encoding='utf-8') as f:
                    json.dump(index_data, f, indent=2, ensure_ascii=False)
                
                # 记录变化
                old_dates = set(existing_data.get('available_dates', []))
                new_dates = set(date_files)
                
                added_dates = new_dates - old_dates
                removed_dates = old_dates - new_dates
                
                if added_dates:
                    logger.info(f"✅ 新增日期: {sorted(added_dates)}")
                if removed_dates:
                    logger.info(f"➖ 移除日期: {sorted(removed_dates)}")
                if not added_dates and not removed_dates and old_dates:
                    logger.info("🔄 日期列表无变化")
                else:
                    logger.info(f"📊 索引更新完成: {len(date_files)} 个日期")
                
                return True
                
            except Exception as e:
                logger.error(f"更新索引失败: {e}")
                return False

class DataFileHandler(FileSystemEventHandler):
    """数据文件变化处理器"""
    
    def __init__(self, index_manager):
        self.index_manager = index_manager
        self.last_update = 0
        self.update_delay = 2  # 延迟2秒更新，避免频繁触发
        
    def on_any_event(self, event):
        """处理任何文件系统事件"""
        if event.is_directory:
            return
            
        # 只处理JSON文件
        if not event.src_path.endswith('.json'):
            return
            
        # 只处理日期文件
        filename = os.path.basename(event.src_path)
        if not filename.startswith('date_'):
            return
            
        current_time = time.time()
        
        # 防抖处理
        if current_time - self.last_update < self.update_delay:
            return
            
        self.last_update = current_time
        
        # 延迟更新
        threading.Timer(self.update_delay, self._delayed_update, [event]).start()
    
    def _delayed_update(self, event):
        """延迟更新索引"""
        filename = os.path.basename(event.src_path)
        
        if event.event_type == 'created':
            logger.info(f"📁 检测到新文件: {filename}")
            # 验证文件格式
            is_valid, message = self.index_manager.validate_json_file(event.src_path)
            if is_valid:
                logger.info(f"✅ 文件格式验证通过: {message}")
            else:
                logger.warning(f"⚠️ 文件格式验证失败: {message}")
                
        elif event.event_type == 'modified':
            logger.info(f"📝 检测到文件修改: {filename}")
            
        elif event.event_type == 'deleted':
            logger.info(f"🗑️ 检测到文件删除: {filename}")
            
        # 更新索引
        if self.index_manager.update_index():
            logger.info("🔄 索引文件已自动更新")
        else:
            logger.error("❌ 索引文件更新失败")

class DataMonitor:
    """数据监控主类"""
    
    def __init__(self, data_dir="reports/data"):
        self.data_dir = Path(data_dir)
        self.index_manager = DataIndexManager(data_dir)
        self.observer = None
        self.running = False
        
    def start(self):
        """启动监控"""
        if self.running:
            logger.warning("监控已在运行中")
            return
            
        if not self.data_dir.exists():
            logger.error(f"数据目录不存在: {self.data_dir}")
            return False
            
        # 初始化索引
        logger.info("🚀 启动数据文件夹监控...")
        logger.info(f"📂 监控目录: {self.data_dir.absolute()}")
        
        if self.index_manager.update_index():
            logger.info("✅ 初始索引创建完成")
        else:
            logger.warning("⚠️ 初始索引创建失败")
        
        # 启动文件监控
        event_handler = DataFileHandler(self.index_manager)
        self.observer = Observer()
        self.observer.schedule(event_handler, str(self.data_dir), recursive=False)
        
        try:
            self.observer.start()
            self.running = True
            logger.info("👀 文件监控已启动，等待文件变化...")
            return True
        except Exception as e:
            logger.error(f"启动监控失败: {e}")
            return False
    
    def stop(self):
        """停止监控"""
        if not self.running:
            return
            
        if self.observer:
            self.observer.stop()
            self.observer.join()
            
        self.running = False
        logger.info("🛑 文件监控已停止")
    
    def is_running(self):
        """检查是否在运行"""
        return self.running
    
    def manual_update(self):
        """手动更新索引"""
        logger.info("🔄 手动更新索引...")
        if self.index_manager.update_index():
            logger.info("✅ 手动更新完成")
            return True
        else:
            logger.error("❌ 手动更新失败")
            return False

def main():
    """主函数 - 用于独立运行"""
    import signal
    import sys
    
    # 查找数据目录
    possible_paths = [
        "reports/data",
        "data", 
        "../reports/data",
        "excel分析/reports/data"
    ]
    
    data_dir = None
    for path in possible_paths:
        if os.path.exists(path):
            data_dir = path
            break
    
    if not data_dir:
        logger.error("❌ 找不到数据目录")
        logger.info(f"预期路径: {possible_paths}")
        return
    
    # 创建监控器
    monitor = DataMonitor(data_dir)
    
    # 信号处理
    def signal_handler(sig, frame):
        logger.info("收到停止信号...")
        monitor.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动监控
    if monitor.start():
        try:
            while monitor.is_running():
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            monitor.stop()
    else:
        logger.error("监控启动失败")

if __name__ == "__main__":
    main()
