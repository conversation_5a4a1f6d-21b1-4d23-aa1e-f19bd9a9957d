/**
 * 智能表格渲染器 - 系统重构核心组件
 * 实现按日期智能渲染，解决DOM性能瓶颈
 * 
 * @version 2.0.0
 * @date 2025-07-20
 * <AUTHOR> Assistant
 */

class SmartTableRenderer {
    constructor() {
        this.currentDate = null;
        this.dateGroups = window.EMBEDDED_DATA || {};
        this.availableDates = Object.keys(this.dateGroups).sort().reverse(); // 最新日期在前
        this.tableHeaders = [];
        this.isRendering = false;
        this.renderQueue = [];
        this.currentStockIndex = -1; // 当前高亮的股票索引
        this.performanceMonitor = {
            enabled: false,
            startTime: 0,
            endTime: 0,
            start: function() { this.startTime = performance.now(); },
            end: function() { this.endTime = performance.now(); return this.endTime - this.startTime; }
        };
        
        console.log('🚀 智能表格渲染器初始化...');
        console.log('📊 可用日期:', this.availableDates);
        console.log('📈 总数据量:', Object.values(this.dateGroups).reduce((sum, group) => sum + group.length, 0), '行');
        
        this.init();
    }
    
    init() {
        // 等待DOM完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeRenderer());
        } else {
            this.initializeRenderer();
        }
    }
    
    initializeRenderer() {
        try {
            // 1. 提取表头信息
            this.extractTableHeaders();
            
            // 2. 更新数据概览
            this.updateDataSummary();
            
            // 3. 显示第一个日期的数据
            if (this.availableDates.length > 0) {
                this.switchToDate(this.availableDates[0]);
            } else {
                console.error('❌ 没有可用的日期数据');
                this.showNoDataMessage();
            }
            
            // 4. 绑定事件
            this.bindEvents();
            
            console.log('✅ 智能表格渲染器初始化完成');
            
        } catch (error) {
            console.error('❌ 智能表格渲染器初始化失败:', error);
        }
    }
    
    extractTableHeaders() {
        const headerRow = document.querySelector('table.dataframe thead tr');
        if (headerRow) {
            this.tableHeaders = Array.from(headerRow.querySelectorAll('th')).map(th => th.textContent.trim());
            console.log('📋 提取表头:', this.tableHeaders.length, '列');
        } else if (this.availableDates.length > 0) {
            // 从数据中提取表头
            const firstDateData = this.dateGroups[this.availableDates[0]];
            if (firstDateData && firstDateData.length > 0) {
                this.tableHeaders = Object.keys(firstDateData[0]);
                this.generateTableHeaders();
                console.log('📋 从数据生成表头:', this.tableHeaders.length, '列');
            }
        }
    }
    
    generateTableHeaders() {
        const thead = document.querySelector('table.dataframe thead');
        if (!thead) return;
        
        thead.innerHTML = '';
        const headerRow = document.createElement('tr');
        
        this.tableHeaders.forEach(header => {
            const th = document.createElement('th');
            th.textContent = header;
            th.style.cursor = 'pointer';
            th.setAttribute('data-column', header);
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
    }
    
    updateDataSummary() {
        const totalRecords = Object.values(this.dateGroups).reduce((sum, group) => sum + group.length, 0);
        const totalDates = this.availableDates.length;
        
        // 更新页面上的统计信息（如果存在）
        const summaryElements = {
            'total-records': totalRecords.toLocaleString(),
            'total-dates': totalDates,
            'data-count': totalRecords.toLocaleString()
        };
        
        Object.entries(summaryElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        });
        
        console.log('📊 数据概览更新:', { totalRecords, totalDates });
    }
    
    switchToDate(targetDate) {
        if (!this.dateGroups[targetDate]) {
            console.error('❌ 日期数据不存在:', targetDate);
            return;
        }
        
        if (this.isRendering) {
            console.log('⏳ 正在渲染中，加入队列:', targetDate);
            this.renderQueue = [targetDate]; // 只保留最新的请求
            return;
        }
        
        const dataCount = this.dateGroups[targetDate].length;
        console.log(`📅 切换到日期: ${targetDate} (${dataCount}行)`);
        
        this.currentDate = targetDate;
        this.renderDateData(targetDate);
        this.updateCurrentDateSummary(targetDate);
        this.updateDateNavigation(targetDate);
    }
    
    renderDateData(date) {
        this.isRendering = true;
        this.performanceMonitor.start();
        
        const tbody = document.querySelector('table.dataframe tbody');
        if (!tbody) {
            console.error('❌ 未找到表格tbody');
            this.isRendering = false;
            return;
        }
        
        const dateData = this.dateGroups[date];
        
        // 显示加载提示
        tbody.innerHTML = `
            <tr>
                <td colspan="${this.tableHeaders.length}" style="text-align: center; padding: 20px; color: #666; font-style: italic;">
                    🔄 正在渲染 ${date} 的 ${dateData.length} 行数据...
                </td>
            </tr>
        `;
        
        // 异步渲染，避免阻塞UI
        setTimeout(() => {
            this.renderInBatches(dateData, (fragment) => {
                tbody.innerHTML = '';
                tbody.appendChild(fragment);
                
                const renderTime = this.performanceMonitor.end();
                console.log(`✅ 渲染完成: ${date} (${dateData.length}行, ${renderTime.toFixed(2)}ms)`);
                
                // 重新绑定功能
                this.rebindAllFeatures();
                
                this.isRendering = false;
                
                // 处理渲染队列
                if (this.renderQueue.length > 0) {
                    const nextDate = this.renderQueue.pop();
                    this.renderQueue = [];
                    this.switchToDate(nextDate);
                }
            });
        }, 10);
    }
    
    renderInBatches(data, callback, batchSize = 25) {
        const fragment = document.createDocumentFragment();
        let index = 0;
        
        const renderBatch = () => {
            const endIndex = Math.min(index + batchSize, data.length);
            
            for (let i = index; i < endIndex; i++) {
                const row = this.createTableRow(data[i], i);
                fragment.appendChild(row);
            }
            
            index = endIndex;
            
            if (index < data.length) {
                requestAnimationFrame(renderBatch);
            } else {
                callback(fragment);
            }
        };
        
        renderBatch();
    }
    
    createTableRow(rowData, rowIndex) {
        const tr = document.createElement('tr');
        tr.setAttribute('data-row-index', rowIndex);
        tr.style.cursor = 'pointer'; // 表示可点击
        
        this.tableHeaders.forEach(header => {
            const td = document.createElement('td');
            const value = rowData[header];
            
            // 数值格式化和样式
            if (typeof value === 'number') {
                if (header.includes('涨幅') || header.includes('%')) {
                    td.textContent = value.toFixed(2) + '%';
                    if (value > 0) td.style.color = '#ff4444';
                    else if (value < 0) td.style.color = '#00aa00';
                } else if (header.includes('额') || header.includes('量')) {
                    td.textContent = value.toLocaleString();
                } else {
                    td.textContent = value.toFixed(2);
                }
            } else {
                td.textContent = value || '';
            }
            
            tr.appendChild(td);
        });
        
        return tr;
    }
    
    updateCurrentDateSummary(date) {
        const currentRecordsEl = document.getElementById('current-date-records');
        if (currentRecordsEl && this.dateGroups[date]) {
            currentRecordsEl.textContent = this.dateGroups[date].length;
        }
        
        // 更新页面标题中的日期信息
        const titleElements = document.querySelectorAll('h1, h2, .current-date');
        titleElements.forEach(el => {
            if (el.textContent.includes('数据分析报告')) {
                const originalText = el.textContent.replace(/\s*\(.*?\)\s*$/, '');
                el.textContent = `${originalText} (当前: ${date})`;
            }
        });
    }
    
    updateDateNavigation(currentDate) {
        // 更新日期导航显示（如果存在）
        const dateNavElements = document.querySelectorAll('[data-date]');
        dateNavElements.forEach(el => {
            if (el.getAttribute('data-date') === currentDate) {
                el.classList.add('active');
            } else {
                el.classList.remove('active');
            }
        });
        
        // 更新分页导航信息
        const currentIndex = this.availableDates.indexOf(currentDate);
        const pageInfo = document.querySelector('.page-info');
        if (pageInfo) {
            pageInfo.textContent = `${currentIndex + 1} / ${this.availableDates.length}`;
        }
    }
    
    rebindAllFeatures() {
        // 重新绑定表格排序
        if (typeof makeTableSortable === 'function') {
            const table = document.querySelector('table.dataframe');
            try {
                makeTableSortable(table);
                console.log('✅ 表格排序功能已重新绑定');
            } catch (e) {
                console.log('ℹ️ 表格排序功能无需重新绑定');
            }
        }
        
        // 重新绑定K线图事件 - 但阻止原始的事件绑定
        this.initializeKlineChartManager();

        // 阻止原始的K线图事件绑定，避免弹出窗口
        this.preventOriginalKlineBinding();
        
        // 重新绑定行点击事件
        this.bindRowClickEvents();
    }
    
    bindRowClickEvents() {
        const tbody = document.querySelector('table.dataframe tbody');
        if (!tbody) return;
        
        // 移除旧的事件监听器
        tbody.removeEventListener('click', this.handleRowClick);
        
        // 绑定新的事件监听器
        this.handleRowClick = (e) => {
            const row = e.target.closest('tr');
            if (!row || !row.hasAttribute('data-row-index')) return;

            const rowIndex = parseInt(row.getAttribute('data-row-index'));
            const rowData = this.dateGroups[this.currentDate][rowIndex];

            // 更新当前股票索引
            this.currentStockIndex = rowIndex;

            // 设置行高亮
            const currentRows = document.querySelectorAll('table.dataframe tbody tr');
            currentRows.forEach((r, i) => {
                const rIndex = parseInt(r.getAttribute('data-row-index') || '-1');
                r.style.backgroundColor = rIndex === rowIndex ? '#f0f8ff' : '';
                r.style.borderLeft = rIndex === rowIndex ? '3px solid #4A90E2' : '';
            });

            // 触发K线图显示 - 使用现有的K线图系统
            if (rowData['代码']) {
                console.log('📈 显示K线图:', rowData['代码'], rowData['名称']);

                // 首先激活上下分割布局
                this.activateSplitLayout();

                // 在分割布局中显示K线图（不使用弹出窗口）
                this.showKlineInSplitContainer(rowData['代码'], rowData['名称'], this.currentDate);
            }
        };
        
        tbody.addEventListener('click', this.handleRowClick);
    }
    
    bindEvents() {
        // 键盘导航
        document.addEventListener('keydown', (e) => {
            if (e.key === 'PageUp') {
                e.preventDefault();
                this.switchToPreviousDate();
            } else if (e.key === 'PageDown') {
                e.preventDefault();
                this.switchToNextDate();
            } else if (e.key === 'Escape') {
                this.closeKlineChart();
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.navigateToNextStock();
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.navigateToPreviousStock();
            }
        });

        console.log('⌨️ 键盘导航已绑定 (PageUp/PageDown/Escape/ArrowUp/ArrowDown)');
    }
    
    switchToPreviousDate() {
        const currentIndex = this.availableDates.indexOf(this.currentDate);
        if (currentIndex > 0) {
            this.switchToDate(this.availableDates[currentIndex - 1]);
        } else {
            console.log('📅 已经是第一个日期');
        }
    }
    
    switchToNextDate() {
        const currentIndex = this.availableDates.indexOf(this.currentDate);
        if (currentIndex < this.availableDates.length - 1) {
            this.switchToDate(this.availableDates[currentIndex + 1]);
        } else {
            console.log('📅 已经是最后一个日期');
        }
    }
    
    // 激活上下分割布局
    activateSplitLayout() {
        console.log('🎯 激活上下分割布局');

        // 1. 首先关闭任何现有的弹出窗口
        this.closeExistingModals();

        // 2. 激活分割布局CSS类
        const container = document.querySelector('.container') || document.body;
        container.classList.add('layout-with-kline');

        // 3. 显示K线图区域
        const klineSection = document.getElementById('klineSection');
        if (klineSection) {
            klineSection.classList.add('active');
            klineSection.style.display = 'block';
        }

        // 4. 调整表格区域高度
        const tableSection = document.getElementById('tableSection');
        if (tableSection) {
            tableSection.style.height = '50vh';
        }

        console.log('✅ 上下分割布局已激活');
    }

    // 在分割布局容器中显示K线图
    async showKlineInSplitContainer(stockCode, stockName, selectedDate) {
        console.log('📊 在分割布局容器中显示K线图:', stockCode, stockName);

        // 1. 关闭所有弹出窗口
        this.closeExistingModals();

        // 2. 获取分割布局中的K线图容器
        const klineContainer = document.getElementById('klineChartContainer');
        if (!klineContainer) {
            console.error('❌ 未找到K线图容器 #klineChartContainer');
            return;
        }

        // 3. 检查ECharts是否可用
        if (typeof echarts === 'undefined') {
            console.error('❌ ECharts库未加载');
            return;
        }

        // 4. 检查K线图管理器是否可用
        if (typeof globalKlineManager === 'undefined' || !globalKlineManager) {
            console.error('❌ K线图管理器未初始化');
            return;
        }

        // 5. 清理容器并创建新的图表实例
        klineContainer.innerHTML = '';
        const chart = echarts.init(klineContainer);

        // 6. 显示加载状态
        chart.showLoading('default', {
            text: '正在加载K线图...',
            color: '#4CAF50',
            textColor: '#fff',
            maskColor: 'rgba(0, 0, 0, 0.8)'
        });

        try {
            // 7. 使用K线图管理器的数据获取方法
            const data = await globalKlineManager.fetchKlineData(stockCode, selectedDate);

            // 8. 创建K线图配置（使用真实数据）
            const option = this.createKlineChartOptionWithRealData(stockCode, stockName, selectedDate, data);

            chart.hideLoading();
            chart.setOption(option);
            console.log('✅ K线图已在分割布局中显示（真实数据）');

        } catch (error) {
            console.error('❌ K线图数据获取失败:', error);
            chart.hideLoading();

            // 显示错误信息，不使用示例数据
            chart.setOption({
                title: {
                    text: `${stockName} (${stockCode}) - 数据获取失败`,
                    left: 'center',
                    textStyle: { color: '#ff6b6b', fontSize: 16 }
                },
                backgroundColor: '#1e1e1e',
                graphic: {
                    type: 'text',
                    left: 'center',
                    top: 'middle',
                    style: {
                        text: `K线数据获取失败\n${error.message}`,
                        textAlign: 'center',
                        fill: '#ff6b6b',
                        fontSize: 14
                    }
                }
            });

            throw error; // 重新抛出错误
        }
    }

    // 创建K线图配置（使用真实数据）
    createKlineChartOptionWithRealData(stockCode, stockName, selectedDate, klineData) {
        if (!klineData || !klineData.dates || !klineData.klineData) {
            console.error('❌ K线数据格式不正确:', klineData);
            throw new Error(`K线数据格式错误: 缺少必要字段 dates 或 klineData`);
        }

        return {
            title: {
                text: `${stockName} (${stockCode})`,
                left: 'center',
                textStyle: {
                    color: '#fff',
                    fontSize: 16
                }
            },
            backgroundColor: '#1e1e1e',
            grid: {
                left: '8%',
                right: '8%',
                top: '15%',
                bottom: '15%'
            },
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0,0,0,0.8)',
                textStyle: { color: '#fff' },
                formatter: function (params) {
                    const data = params[0].data;
                    return `日期: ${params[0].axisValue}<br/>
                            开盘: ${data[1].toFixed(2)}<br/>
                            收盘: ${data[2].toFixed(2)}<br/>
                            最低: ${data[3].toFixed(2)}<br/>
                            最高: ${data[4].toFixed(2)}`;
                }
            },
            xAxis: {
                type: 'category',
                data: klineData.dates,
                axisLine: { lineStyle: { color: '#666' } },
                axisLabel: { color: '#ccc' }
            },
            yAxis: {
                type: 'value',
                axisLine: { lineStyle: { color: '#666' } },
                axisLabel: { color: '#ccc' },
                splitLine: { lineStyle: { color: '#333' } }
            },
            series: [{
                name: 'K线图',
                type: 'candlestick',
                data: klineData.klineData,  // 使用正确的字段名
                itemStyle: {
                    color: '#ef4444',      // 阳线颜色
                    color0: '#22c55e',     // 阴线颜色
                    borderColor: '#ef4444',
                    borderColor0: '#22c55e'
                }
            }]
        };
    }



    // 关闭现有的弹出窗口
    closeExistingModals() {
        // 关闭K线图弹出窗口
        const modal = document.getElementById('kline-modal');
        if (modal) {
            modal.style.display = 'none';
            console.log('🔒 已关闭K线图弹出窗口');
        }

        // 关闭其他可能的弹出窗口
        const modals = document.querySelectorAll('.modal, .popup, .overlay');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
    }





    // 阻止原始的K线图事件绑定
    preventOriginalKlineBinding() {
        console.log('🚫 阻止原始K线图事件绑定，避免弹出窗口冲突');

        // 重写bindKlineChartEvents函数，让它什么都不做
        if (typeof window.bindKlineChartEvents !== 'undefined') {
            window.originalBindKlineChartEvents = window.bindKlineChartEvents;
        }

        window.bindKlineChartEvents = function() {
            console.log('🚫 原始K线图事件绑定已被智能渲染器阻止');
            // 什么都不做，阻止原始的事件绑定
        };

        // 重写rebindAllFeatures函数，只保留表格排序功能
        if (typeof window.rebindAllFeatures !== 'undefined') {
            window.originalRebindAllFeatures = window.rebindAllFeatures;
        }

        window.rebindAllFeatures = function() {
            console.log('🔄 智能渲染器接管rebindAllFeatures');

            // 只重新绑定表格排序功能，不绑定K线图事件
            const table = document.querySelector('table.dataframe');
            if (table && typeof makeTableSortable === 'function') {
                makeTableSortable(table);
                console.log('✅ 表格排序功能已重新绑定');
            }

            // 不调用K线图事件绑定，避免冲突
        };
    }

    // 初始化K线图管理器
    initializeKlineChartManager() {
        console.log('🔄 初始化K线图管理器...');

        // 检查依赖
        if (typeof echarts === 'undefined') {
            console.error('❌ ECharts库未加载');
            return;
        }

        // 只初始化管理器，不绑定事件
        if (typeof initializeKlineChart === 'function') {
            try {
                // 检查globalKlineManager变量（不是window属性）
                if (typeof globalKlineManager === 'undefined' || !globalKlineManager) {
                    // 临时阻止事件绑定
                    const originalBindKlineChartEvents = window.bindKlineChartEvents;
                    window.bindKlineChartEvents = function() {
                        console.log('🚫 临时阻止initializeKlineChart中的事件绑定');
                    };

                    initializeKlineChart();

                    // 恢复原始函数（虽然我们后面会重写它）
                    window.bindKlineChartEvents = originalBindKlineChartEvents;

                    console.log('✅ K线图管理器已初始化（无事件绑定）');
                } else {
                    console.log('ℹ️ K线图管理器已存在');
                }
            } catch (e) {
                console.error('❌ initializeKlineChart失败:', e);
                this.fallbackKlineManagerInit();
            }
        } else {
            console.warn('⚠️ 未找到initializeKlineChart函数，尝试备用方案');
            this.fallbackKlineManagerInit();
        }
    }

    // 备用K线图管理器初始化
    fallbackKlineManagerInit() {
        console.log('🔄 尝试备用K线图管理器初始化...');

        if (typeof echarts === 'undefined') {
            console.error('❌ ECharts库未加载，无法初始化K线图');
            return;
        }

        // 检查是否有KlineChartManager类
        if (typeof KlineChartManager !== 'undefined') {
            try {
                if (typeof globalKlineManager === 'undefined' || !globalKlineManager) {
                    // 注意：这里我们需要在全局作用域中设置变量
                    window.eval('globalKlineManager = new KlineChartManager();');
                    console.log('✅ 备用方案：K线图管理器已初始化');
                }
            } catch (e) {
                console.error('❌ 备用初始化失败:', e);
            }
        } else {
            console.error('❌ KlineChartManager类未找到');
        }
    }

    // 导航到下一个股票
    navigateToNextStock() {
        const currentRows = document.querySelectorAll('table.dataframe tbody tr');
        if (currentRows.length === 0) return;

        const newIndex = this.currentStockIndex < currentRows.length - 1 ?
                        this.currentStockIndex + 1 : 0;

        this.setActiveStock(newIndex);
    }

    // 导航到上一个股票
    navigateToPreviousStock() {
        const currentRows = document.querySelectorAll('table.dataframe tbody tr');
        if (currentRows.length === 0) return;

        const newIndex = this.currentStockIndex > 0 ?
                        this.currentStockIndex - 1 : currentRows.length - 1;

        this.setActiveStock(newIndex);
    }

    // 设置当前活跃的股票
    setActiveStock(index) {
        const currentRows = document.querySelectorAll('table.dataframe tbody tr');
        if (index < 0 || index >= currentRows.length) return;

        // 获取目标行的实际数据索引
        const targetRow = currentRows[index];
        const dataRowIndex = parseInt(targetRow.getAttribute('data-row-index') || '-1');
        if (dataRowIndex === -1) return;

        // 清除之前的高亮
        currentRows.forEach((row, i) => {
            row.style.backgroundColor = i === index ? '#f0f8ff' : '';
            row.style.borderLeft = i === index ? '3px solid #4A90E2' : '';
        });

        this.currentStockIndex = index;

        // 滚动到当前行
        if (targetRow) {
            targetRow.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 从数据组中获取股票数据
            const rowData = this.dateGroups[this.currentDate][dataRowIndex];
            if (rowData && rowData['代码']) {
                console.log('🎯 键盘导航到股票:', rowData['代码'], rowData['名称']);

                // 激活分割布局
                this.activateSplitLayout();

                // 显示K线图
                this.showKlineInSplitContainer(rowData['代码'], rowData['名称'], this.currentDate);
            }
        }
    }

    closeKlineChart() {
        console.log('🔄 关闭K线图，恢复纯表格视图');

        // 1. 移除分割布局类
        const container = document.querySelector('.container') || document.body;
        container.classList.remove('layout-with-kline');

        // 2. 隐藏K线图区域
        const klineSection = document.getElementById('klineSection');
        if (klineSection) {
            klineSection.classList.remove('active');
            klineSection.style.display = 'none';
        }

        // 3. 恢复表格区域
        const tableSection = document.getElementById('tableSection');
        if (tableSection) {
            tableSection.style.height = '';
        }

        // 4. 清理K线图
        const klineContainer = document.getElementById('klineChartContainer');
        if (klineContainer) {
            klineContainer.innerHTML = '';
        }

        // 5. 隐藏弹出窗口（如果存在）
        const modal = document.getElementById('kline-modal');
        if (modal) {
            modal.style.display = 'none';
        }

        // 6. 重置股票索引
        this.currentStockIndex = -1;

        // 7. 清除所有行的高亮
        const currentRows = document.querySelectorAll('table.dataframe tbody tr');
        currentRows.forEach(row => {
            row.style.backgroundColor = '';
            row.style.borderLeft = '';
        });

        console.log('✅ 已恢复纯表格视图');
    }
    
    showNoDataMessage() {
        const tbody = document.querySelector('table.dataframe tbody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" style="text-align: center; padding: 40px; color: #999; font-size: 18px;">
                        📊 暂无数据<br>
                        <small style="color: #ccc;">请检查数据源或重新生成报告</small>
                    </td>
                </tr>
            `;
        }
    }
    
    // 为未来筛选功能预留的方法
    getCurrentDateData() {
        return this.dateGroups[this.currentDate] || [];
    }
    
    renderFilteredData(filteredData) {
        console.log(`🔍 渲染筛选结果: ${filteredData.length}行`);
        this.performanceMonitor.start();
        
        const tbody = document.querySelector('table.dataframe tbody');
        if (!tbody) return;
        
        this.renderInBatches(filteredData, (fragment) => {
            tbody.innerHTML = '';
            tbody.appendChild(fragment);
            
            const renderTime = this.performanceMonitor.end();
            console.log(`✅ 筛选渲染完成: ${filteredData.length}行, ${renderTime.toFixed(2)}ms`);
            
            this.rebindAllFeatures();
        });
    }
}

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待其他脚本加载完成
    setTimeout(() => {
        if (window.EMBEDDED_DATA && Object.keys(window.EMBEDDED_DATA).length > 0) {
            window.smartRenderer = new SmartTableRenderer();
            console.log('🎉 智能分页系统已启动');
        } else {
            console.error('❌ 未找到嵌入数据，智能渲染器无法启动');
        }
    }, 100);
});

// 导出到全局作用域
window.SmartTableRenderer = SmartTableRenderer;
