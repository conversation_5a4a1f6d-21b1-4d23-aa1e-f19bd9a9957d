/**
 * Excel分析报告性能优化器
 * 解决大数据量页面加载缓慢问题
 * 
 * 核心功能：
 * 1. 虚拟滚动表格
 * 2. 数据懒加载
 * 3. 分批渲染
 * 4. 缓存管理
 * 
 * @version 1.0.0
 * @date 2025-07-19
 */

class PerformanceOptimizer {
    constructor(options = {}) {
        this.options = {
            // 虚拟滚动配置
            virtualScrolling: true,
            visibleRowCount: 20,        // 可见行数
            bufferRowCount: 5,          // 缓冲行数
            rowHeight: 40,              // 行高(px)
            
            // 分批加载配置
            batchSize: 50,              // 每批加载行数
            loadDelay: 16,              // 加载延迟(ms) - 约60fps
            
            // 缓存配置
            enableCache: true,
            cacheSize: 1000,            // 缓存行数
            
            // 性能监控
            enablePerfMonitor: true,
            
            ...options
        };
        
        this.data = [];                 // 原始数据
        this.filteredData = [];         // 过滤后数据
        this.renderedRows = new Map();  // 已渲染行缓存
        this.scrollTop = 0;             // 当前滚动位置
        this.isLoading = false;         // 加载状态
        
        this.tableContainer = null;
        this.tableBody = null;
        this.virtualContainer = null;
        this.loadingIndicator = null;
        
        this.init();
    }
    
    /**
     * 初始化优化器
     */
    init() {
        console.log('🚀 性能优化器初始化开始...');
        
        if (this.options.enablePerfMonitor) {
            this.startPerformanceMonitoring();
        }
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupOptimization());
        } else {
            this.setupOptimization();
        }
    }
    
    /**
     * 设置性能优化
     */
    setupOptimization() {
        console.log('⚙️ 开始设置性能优化...');
        
        // 查找表格元素
        this.findTableElements();
        
        if (!this.tableContainer || !this.tableBody) {
            console.warn('⚠️ 未找到表格元素，跳过优化');
            return;
        }
        
        // 提取现有数据
        this.extractExistingData();
        
        // 设置虚拟滚动
        if (this.options.virtualScrolling) {
            this.setupVirtualScrolling();
        }
        
        // 设置分批渲染
        this.setupBatchRendering();
        
        // 设置加载指示器
        this.setupLoadingIndicator();
        
        console.log('✅ 性能优化设置完成');
    }
    
    /**
     * 查找表格元素
     */
    findTableElements() {
        this.tableContainer = document.querySelector('.table-wrapper');

        // 尝试多种方式查找表格主体
        this.tableBody = document.querySelector('table.dataframe tbody');

        if (!this.tableBody) {
            // 如果没有tbody，查找table本身
            const table = document.querySelector('table.dataframe');
            if (table) {
                // 检查是否有tbody
                let tbody = table.querySelector('tbody');
                if (!tbody) {
                    // 如果没有tbody，创建一个并移动所有tr（除了thead中的）
                    tbody = document.createElement('tbody');
                    const rows = table.querySelectorAll('tr');
                    const thead = table.querySelector('thead');
                    const headerRowCount = thead ? thead.querySelectorAll('tr').length : 0;

                    // 移动数据行到tbody
                    for (let i = headerRowCount; i < rows.length; i++) {
                        tbody.appendChild(rows[i]);
                    }
                    table.appendChild(tbody);
                }
                this.tableBody = tbody;
            }
        }

        if (this.tableContainer) {
            console.log('📋 找到表格容器');
        }
        if (this.tableBody) {
            console.log('📊 找到表格主体，包含', this.tableBody.querySelectorAll('tr').length, '行数据');
        } else {
            console.warn('⚠️ 未找到表格主体，尝试查找所有可能的表格结构...');
            this.debugTableStructure();
        }
    }

    /**
     * 调试表格结构
     */
    debugTableStructure() {
        console.log('🔍 调试表格结构...');

        // 查找所有可能的表格
        const allTables = document.querySelectorAll('table');
        console.log(`找到 ${allTables.length} 个表格元素`);

        allTables.forEach((table, index) => {
            console.log(`表格 ${index + 1}:`);
            console.log(`  类名: ${table.className}`);
            console.log(`  ID: ${table.id}`);

            const thead = table.querySelector('thead');
            const tbody = table.querySelector('tbody');
            const allRows = table.querySelectorAll('tr');

            console.log(`  thead: ${thead ? '存在' : '不存在'}`);
            console.log(`  tbody: ${tbody ? '存在' : '不存在'}`);
            console.log(`  总行数: ${allRows.length}`);

            if (thead) {
                const headerRows = thead.querySelectorAll('tr');
                console.log(`  表头行数: ${headerRows.length}`);
            }

            if (tbody) {
                const dataRows = tbody.querySelectorAll('tr');
                console.log(`  数据行数: ${dataRows.length}`);
            } else if (allRows.length > 0) {
                console.log(`  可能的数据行数: ${allRows.length - (thead ? thead.querySelectorAll('tr').length : 0)}`);
            }
        });

        // 查找表格容器
        const containers = document.querySelectorAll('.table-wrapper, .table-container, .dataframe-container');
        console.log(`找到 ${containers.length} 个可能的表格容器`);

        containers.forEach((container, index) => {
            console.log(`容器 ${index + 1}: ${container.className}`);
        });
    }

    /**
     * 提取现有表格数据
     */
    extractExistingData() {
        console.log('📤 开始提取现有数据...');

        if (!this.tableBody) {
            console.error('❌ 无法提取数据：表格主体不存在');
            return;
        }

        const rows = this.tableBody.querySelectorAll('tr');
        console.log(`🔍 在表格主体中找到 ${rows.length} 行`);

        this.data = Array.from(rows).map((row, index) => {
            const cells = row.querySelectorAll('td');
            const cellData = Array.from(cells).map(cell => cell.textContent.trim());

            return {
                index,
                element: row.cloneNode(true),
                data: cellData,
                visible: false
            };
        });

        this.filteredData = [...this.data];

        console.log(`📊 成功提取了 ${this.data.length} 行数据`);

        // 输出前几行数据用于调试
        if (this.data.length > 0) {
            console.log('📋 前3行数据示例:');
            for (let i = 0; i < Math.min(3, this.data.length); i++) {
                console.log(`  行${i + 1}: [${this.data[i].data.slice(0, 5).join(', ')}...]`);
            }
        }
    }
    
    /**
     * 设置虚拟滚动
     */
    setupVirtualScrolling() {
        console.log('🔄 设置虚拟滚动...');
        
        // 创建虚拟容器
        this.createVirtualContainer();
        
        // 设置滚动事件
        this.setupScrollEvents();
        
        // 初始渲染
        this.renderVisibleRows();
        
        console.log('✅ 虚拟滚动设置完成');
    }
    
    /**
     * 创建虚拟滚动容器
     */
    createVirtualContainer() {
        // 清空现有内容
        this.tableBody.innerHTML = '';
        
        // 创建虚拟容器
        this.virtualContainer = document.createElement('div');
        this.virtualContainer.className = 'virtual-scroll-container';
        this.virtualContainer.style.cssText = `
            position: relative;
            height: ${this.filteredData.length * this.options.rowHeight}px;
        `;
        
        // 创建可见区域
        const visibleArea = document.createElement('div');
        visibleArea.className = 'virtual-visible-area';
        visibleArea.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
        `;
        
        this.virtualContainer.appendChild(visibleArea);
        
        // 将虚拟容器插入表格
        const tbody = document.createElement('tbody');
        const tr = document.createElement('tr');
        const td = document.createElement('td');
        
        // 获取列数
        const headerCells = document.querySelectorAll('table.dataframe thead th');
        td.colSpan = headerCells.length;
        td.style.padding = '0';
        td.appendChild(this.virtualContainer);
        
        tr.appendChild(td);
        tbody.appendChild(tr);
        
        this.tableBody.parentNode.replaceChild(tbody, this.tableBody);
        this.tableBody = tbody;
        this.visibleArea = visibleArea;
    }
    
    /**
     * 设置滚动事件
     */
    setupScrollEvents() {
        let scrollTimeout;
        
        this.tableContainer.addEventListener('scroll', () => {
            this.scrollTop = this.tableContainer.scrollTop;
            
            // 防抖处理
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.renderVisibleRows();
            }, 16); // 约60fps
        });
    }
    
    /**
     * 渲染可见行
     */
    renderVisibleRows() {
        const containerHeight = this.tableContainer.clientHeight;
        const startIndex = Math.floor(this.scrollTop / this.options.rowHeight);
        const endIndex = Math.min(
            startIndex + Math.ceil(containerHeight / this.options.rowHeight) + this.options.bufferRowCount,
            this.filteredData.length
        );
        
        // 清空可见区域
        this.visibleArea.innerHTML = '';
        
        // 创建表格结构
        const table = document.createElement('table');
        table.className = 'dataframe virtual-table';
        table.style.cssText = `
            width: 100%;
            border-collapse: collapse;
            position: absolute;
            top: ${startIndex * this.options.rowHeight}px;
        `;
        
        const tbody = document.createElement('tbody');
        
        // 渲染可见行
        for (let i = startIndex; i < endIndex; i++) {
            if (this.filteredData[i]) {
                const row = this.getOrCreateRow(i);
                tbody.appendChild(row);
            }
        }
        
        table.appendChild(tbody);
        this.visibleArea.appendChild(table);
        
        // 更新性能指标
        if (this.options.enablePerfMonitor) {
            this.updatePerformanceMetrics(startIndex, endIndex);
        }
    }
    
    /**
     * 获取或创建行元素
     */
    getOrCreateRow(index) {
        if (this.renderedRows.has(index)) {
            return this.renderedRows.get(index);
        }
        
        const rowData = this.filteredData[index];
        const row = rowData.element.cloneNode(true);
        
        // 添加行索引
        row.setAttribute('data-index', index);
        
        // 缓存行元素
        if (this.renderedRows.size >= this.options.cacheSize) {
            // 清理最旧的缓存
            const firstKey = this.renderedRows.keys().next().value;
            this.renderedRows.delete(firstKey);
        }
        
        this.renderedRows.set(index, row);
        return row;
    }
    
    /**
     * 设置分批渲染
     */
    setupBatchRendering() {
        console.log('⏱️ 设置分批渲染...');
        
        // 如果数据量较小，直接渲染
        if (this.data.length <= this.options.batchSize) {
            return;
        }
        
        // 分批处理大数据量
        this.renderInBatches();
    }
    
    /**
     * 分批渲染数据
     */
    renderInBatches() {
        let currentBatch = 0;
        const totalBatches = Math.ceil(this.data.length / this.options.batchSize);
        
        const renderBatch = () => {
            if (currentBatch >= totalBatches) {
                this.hideLoadingIndicator();
                console.log('✅ 分批渲染完成');
                return;
            }
            
            const startIndex = currentBatch * this.options.batchSize;
            const endIndex = Math.min(startIndex + this.options.batchSize, this.data.length);
            
            // 预处理当前批次数据
            for (let i = startIndex; i < endIndex; i++) {
                this.preprocessRowData(i);
            }
            
            // 更新进度
            this.updateLoadingProgress(currentBatch + 1, totalBatches);
            
            currentBatch++;
            
            // 使用requestAnimationFrame确保不阻塞UI
            requestAnimationFrame(() => {
                setTimeout(renderBatch, this.options.loadDelay);
            });
        };
        
        this.showLoadingIndicator();
        renderBatch();
    }
    
    /**
     * 预处理行数据
     */
    preprocessRowData(index) {
        const rowData = this.data[index];
        
        // 添加搜索索引
        rowData.searchText = rowData.data.join(' ').toLowerCase();
        
        // 预计算样式类
        rowData.cssClass = this.calculateRowClass(rowData);
        
        // 标记为已预处理
        rowData.preprocessed = true;
    }
    
    /**
     * 计算行样式类
     */
    calculateRowClass(rowData) {
        const classes = [];
        
        // 奇偶行样式
        if (rowData.index % 2 === 0) {
            classes.push('even-row');
        } else {
            classes.push('odd-row');
        }
        
        // 根据数据添加特殊样式
        // 例如：涨幅为正的行
        const changePercent = parseFloat(rowData.data[2]); // 假设涨幅在第3列
        if (changePercent > 0) {
            classes.push('positive-change');
        } else if (changePercent < 0) {
            classes.push('negative-change');
        }
        
        return classes.join(' ');
    }
    
    /**
     * 设置加载指示器
     */
    setupLoadingIndicator() {
        this.loadingIndicator = document.createElement('div');
        this.loadingIndicator.className = 'performance-loading-indicator';
        this.loadingIndicator.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 10000;
            display: none;
            text-align: center;
            min-width: 200px;
        `;
        
        this.loadingIndicator.innerHTML = `
            <div class="loading-spinner" style="
                border: 3px solid #f3f3f3;
                border-top: 3px solid #3498db;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                animation: spin 1s linear infinite;
                margin: 0 auto 10px;
            "></div>
            <div class="loading-text">正在优化数据加载...</div>
            <div class="loading-progress">0%</div>
        `;
        
        // 添加旋转动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(this.loadingIndicator);
    }
    
    /**
     * 显示加载指示器
     */
    showLoadingIndicator() {
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = 'block';
        }
    }
    
    /**
     * 隐藏加载指示器
     */
    hideLoadingIndicator() {
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = 'none';
        }
    }
    
    /**
     * 更新加载进度
     */
    updateLoadingProgress(current, total) {
        if (this.loadingIndicator) {
            const progress = Math.round((current / total) * 100);
            const progressElement = this.loadingIndicator.querySelector('.loading-progress');
            if (progressElement) {
                progressElement.textContent = `${progress}%`;
            }
        }
    }
    
    /**
     * 开始性能监控
     */
    startPerformanceMonitoring() {
        this.performanceMetrics = {
            startTime: performance.now(),
            renderCount: 0,
            lastRenderTime: 0,
            averageRenderTime: 0
        };
        
        console.log('📊 性能监控已启动');
    }
    
    /**
     * 更新性能指标
     */
    updatePerformanceMetrics(startIndex, endIndex) {
        if (!this.performanceMetrics) return;
        
        const now = performance.now();
        const renderTime = now - this.performanceMetrics.lastRenderTime;
        
        this.performanceMetrics.renderCount++;
        this.performanceMetrics.averageRenderTime = 
            (this.performanceMetrics.averageRenderTime * (this.performanceMetrics.renderCount - 1) + renderTime) / 
            this.performanceMetrics.renderCount;
        
        this.performanceMetrics.lastRenderTime = now;
        
        // 每100次渲染输出一次性能报告
        if (this.performanceMetrics.renderCount % 100 === 0) {
            console.log(`📊 性能报告: 平均渲染时间 ${this.performanceMetrics.averageRenderTime.toFixed(2)}ms, 渲染行数 ${endIndex - startIndex}`);
        }
    }
    
    /**
     * 搜索和过滤
     */
    filterData(searchTerm) {
        if (!searchTerm) {
            this.filteredData = [...this.data];
        } else {
            const term = searchTerm.toLowerCase();
            this.filteredData = this.data.filter(row => 
                row.searchText && row.searchText.includes(term)
            );
        }
        
        // 更新虚拟容器高度
        if (this.virtualContainer) {
            this.virtualContainer.style.height = `${this.filteredData.length * this.options.rowHeight}px`;
        }
        
        // 重新渲染
        this.renderVisibleRows();
        
        console.log(`🔍 过滤结果: ${this.filteredData.length} / ${this.data.length} 行`);
    }
    
    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        if (!this.performanceMetrics) return null;
        
        const totalTime = performance.now() - this.performanceMetrics.startTime;
        
        return {
            totalTime: totalTime.toFixed(2),
            renderCount: this.performanceMetrics.renderCount,
            averageRenderTime: this.performanceMetrics.averageRenderTime.toFixed(2),
            dataSize: this.data.length,
            cacheSize: this.renderedRows.size,
            memoryUsage: this.estimateMemoryUsage()
        };
    }
    
    /**
     * 估算内存使用量
     */
    estimateMemoryUsage() {
        // 粗略估算
        const rowSize = 1000; // 每行约1KB
        const cacheMemory = this.renderedRows.size * rowSize;
        const dataMemory = this.data.length * 500; // 原始数据约500B/行
        
        return {
            cache: `${(cacheMemory / 1024).toFixed(1)}KB`,
            data: `${(dataMemory / 1024).toFixed(1)}KB`,
            total: `${((cacheMemory + dataMemory) / 1024).toFixed(1)}KB`
        };
    }
}

/**
 * 数据懒加载管理器
 * 处理大量历史数据文件的按需加载
 */
class DataLazyLoader {
    constructor() {
        this.dataCache = new Map();
        this.loadingPromises = new Map();
        this.maxCacheSize = 10; // 最多缓存10个文件的数据
    }

    /**
     * 异步加载数据文件
     */
    async loadDataFile(dateString) {
        // 检查缓存
        if (this.dataCache.has(dateString)) {
            return this.dataCache.get(dateString);
        }

        // 检查是否正在加载
        if (this.loadingPromises.has(dateString)) {
            return this.loadingPromises.get(dateString);
        }

        // 开始加载
        const loadPromise = this.fetchDataFile(dateString);
        this.loadingPromises.set(dateString, loadPromise);

        try {
            const data = await loadPromise;

            // 缓存数据
            this.cacheData(dateString, data);

            // 清理加载Promise
            this.loadingPromises.delete(dateString);

            return data;
        } catch (error) {
            this.loadingPromises.delete(dateString);
            throw error;
        }
    }

    /**
     * 获取数据文件
     */
    async fetchDataFile(dateString) {
        const response = await fetch(`../Excel数据/竞价选股数据${dateString}.csv`);
        if (!response.ok) {
            throw new Error(`Failed to load data for ${dateString}`);
        }

        const csvText = await response.text();
        return this.parseCSV(csvText);
    }

    /**
     * 解析CSV数据
     */
    parseCSV(csvText) {
        const lines = csvText.split('\n');
        const headers = lines[0].split(',');
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) {
                const values = lines[i].split(',');
                const row = {};
                headers.forEach((header, index) => {
                    row[header.trim()] = values[index]?.trim() || '';
                });
                data.push(row);
            }
        }

        return { headers, data };
    }

    /**
     * 缓存数据
     */
    cacheData(dateString, data) {
        // 如果缓存已满，删除最旧的数据
        if (this.dataCache.size >= this.maxCacheSize) {
            const firstKey = this.dataCache.keys().next().value;
            this.dataCache.delete(firstKey);
        }

        this.dataCache.set(dateString, data);
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.dataCache.clear();
        this.loadingPromises.clear();
    }
}

// 自动初始化
window.dataLazyLoader = new DataLazyLoader();

// 自动初始化性能优化器
window.performanceOptimizer = new PerformanceOptimizer({
    virtualScrolling: true,
    visibleRowCount: 15,
    batchSize: 30,
    enablePerfMonitor: true
});

console.log('🚀 Excel分析报告性能优化器已加载');
