/**
 * 纯外部速度优化器
 * 不修改原HTML文件，只通过外部脚本优化加载速度
 * 
 * @version 1.0.0
 * @date 2025-07-19
 */

(function() {
    'use strict';
    
    console.log('🚀 速度优化器启动...');
    
    // 检查是否已经运行过
    if (window.speedBoosterLoaded) {
        return;
    }
    window.speedBoosterLoaded = true;
    
    let originalTable = null;
    let originalRows = [];
    let isOptimizing = false;
    
    /**
     * 等待页面完全加载后开始优化
     */
    function waitForPageReady() {
        if (document.readyState === 'complete') {
            setTimeout(startOptimization, 500); // 延迟500ms确保所有脚本都执行完
        } else {
            window.addEventListener('load', function() {
                setTimeout(startOptimization, 500);
            });
        }
    }
    
    /**
     * 开始优化
     */
    function startOptimization() {
        console.log('🔧 开始速度优化...');
        
        // 查找表格
        originalTable = document.querySelector('table.dataframe');
        if (!originalTable) {
            console.log('⚠️ 未找到表格，跳过优化');
            return;
        }
        
        const tbody = originalTable.querySelector('tbody');
        if (!tbody) {
            console.log('⚠️ 未找到表格主体，跳过优化');
            return;
        }
        
        // 获取原始行数据
        const rows = tbody.querySelectorAll('tr');
        console.log(`📊 找到 ${rows.length} 行数据`);
        
        // 如果行数不多，不需要优化
        if (rows.length < 50) {
            console.log('ℹ️ 数据量较小，无需优化');
            return;
        }
        
        // 保存原始行数据
        originalRows = Array.from(rows).map(row => row.cloneNode(true));
        
        // 开始分批渲染优化
        optimizeTableRendering(tbody);
    }
    
    /**
     * 优化表格渲染
     */
    function optimizeTableRendering(tbody) {
        if (isOptimizing) return;
        isOptimizing = true;
        
        console.log('⚡ 开始分批渲染优化...');
        
        // 清空表格内容
        tbody.innerHTML = '';
        
        // 分批渲染参数
        const batchSize = 30;
        let currentBatch = 0;
        const totalBatches = Math.ceil(originalRows.length / batchSize);
        
        function renderBatch() {
            const start = currentBatch * batchSize;
            const end = Math.min(start + batchSize, originalRows.length);
            
            // 创建文档片段
            const fragment = document.createDocumentFragment();
            
            for (let i = start; i < end; i++) {
                fragment.appendChild(originalRows[i].cloneNode(true));
            }
            
            tbody.appendChild(fragment);
            
            currentBatch++;
            
            if (currentBatch < totalBatches) {
                // 使用requestAnimationFrame确保不阻塞UI
                requestAnimationFrame(renderBatch);
            } else {
                // 渲染完成
                onRenderComplete();
            }
        }
        
        // 开始渲染
        renderBatch();
    }
    
    /**
     * 渲染完成后的处理
     */
    function onRenderComplete() {
        console.log('✅ 分批渲染完成');
        isOptimizing = false;
        
        // 确保原有功能正常工作
        restoreOriginalFunctionality();
    }
    
    /**
     * 恢复原有功能
     */
    function restoreOriginalFunctionality() {
        // 重新初始化表格排序
        if (typeof makeTableSortable === 'function' && originalTable) {
            try {
                makeTableSortable(originalTable);
                console.log('✅ 表格排序功能已恢复');
            } catch (e) {
                console.log('⚠️ 表格排序功能恢复失败:', e);
            }
        }
        
        // 重新初始化K线图功能
        if (typeof initializeKlineChart === 'function') {
            try {
                initializeKlineChart();
                console.log('✅ K线图功能已恢复');
            } catch (e) {
                console.log('⚠️ K线图功能恢复失败:', e);
            }
        }
        
        // 触发自定义事件，通知其他模块表格已更新
        const event = new CustomEvent('tableOptimized', {
            detail: { table: originalTable }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * CSS优化 - 提高渲染性能
     */
    function applyCSSOptimizations() {
        const style = document.createElement('style');
        style.textContent = `
            /* 硬件加速优化 */
            table.dataframe {
                transform: translateZ(0);
                will-change: scroll-position;
            }
            
            /* 表格渲染优化 */
            table.dataframe tbody tr {
                contain: layout style;
            }
            
            /* 滚动优化 */
            .table-wrapper {
                transform: translateZ(0);
                -webkit-overflow-scrolling: touch;
            }
        `;
        document.head.appendChild(style);
        console.log('✅ CSS性能优化已应用');
    }
    
    /**
     * 内存优化
     */
    function optimizeMemory() {
        // 定期清理不需要的引用
        setInterval(function() {
            if (window.gc && typeof window.gc === 'function') {
                window.gc();
            }
        }, 30000); // 每30秒清理一次
    }
    
    // 应用CSS优化
    applyCSSOptimizations();
    
    // 应用内存优化
    optimizeMemory();
    
    // 等待页面准备就绪后开始优化
    waitForPageReady();
    
    console.log('📊 速度优化器已加载');
    
})();
