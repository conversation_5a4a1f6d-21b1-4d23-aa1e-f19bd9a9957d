<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K线图功能测试</title>
    <style>
        body { font-family: 'Microsoft YaHei', <PERSON>l, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        .test-section { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #3498db; }
        .btn { padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #2980b9; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .test-result { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border: 1px solid #e9ecef; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 K线图功能测试</h1>
        
        <div class="status info">
            <strong>📋 测试目标：</strong>验证K线图管理器是否正确初始化和工作
        </div>
        
        <div class="test-section">
            <h3>1. 基础环境检查</h3>
            <button class="btn" onclick="testEnvironment()">🔍 检查环境</button>
            <div id="envResult" class="test-result">点击按钮开始检查...</div>
        </div>
        
        <div class="test-section">
            <h3>2. K线图管理器测试</h3>
            <button class="btn" onclick="testKlineManager()">📈 测试K线图管理器</button>
            <div id="klineResult" class="test-result">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>3. 模拟股票数据测试</h3>
            <button class="btn" onclick="testStockData()">📊 测试股票数据显示</button>
            <div id="stockResult" class="test-result">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>4. 完整功能测试</h3>
            <button class="btn" onclick="runFullTest()">🚀 运行完整测试</button>
            <div id="fullResult" class="test-result">点击按钮开始完整测试...</div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="复盘分析_精简版.html" class="btn">🔄 返回主页面</a>
        </div>
    </div>

    <!-- 加载必要的JS模块 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="kline_chart_helper.js"></script>
    
    <script>
        let testKlineManager = null;
        
        function updateResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function testEnvironment() {
            updateResult('envResult', '正在检查环境...', 'info');
            
            const checks = [];
            
            // 检查ECharts
            if (typeof echarts !== 'undefined') {
                checks.push('✅ ECharts库已加载');
            } else {
                checks.push('❌ ECharts库未加载');
            }
            
            // 检查KlineChartManager
            if (typeof KlineChartManager !== 'undefined') {
                checks.push('✅ KlineChartManager类已定义');
            } else {
                checks.push('❌ KlineChartManager类未定义');
            }
            
            // 检查DOM
            if (document.body) {
                checks.push('✅ DOM已准备就绪');
            } else {
                checks.push('❌ DOM未准备就绪');
            }
            
            const allPassed = checks.every(check => check.startsWith('✅'));
            const resultType = allPassed ? 'success' : 'error';
            
            updateResult('envResult', `
                <h4>环境检查结果：</h4>
                ${checks.map(check => `<div>${check}</div>`).join('')}
            `, resultType);
        }
        
        function testKlineManager() {
            updateResult('klineResult', '正在测试K线图管理器...', 'info');
            
            try {
                // 创建K线图管理器实例
                testKlineManager = new KlineChartManager();
                
                const checks = [];
                
                // 检查实例创建
                if (testKlineManager) {
                    checks.push('✅ K线图管理器实例创建成功');
                } else {
                    checks.push('❌ K线图管理器实例创建失败');
                }
                
                // 检查方法存在
                if (typeof testKlineManager.show === 'function') {
                    checks.push('✅ show方法存在');
                } else {
                    checks.push('❌ show方法不存在');
                }
                
                if (typeof testKlineManager.hide === 'function') {
                    checks.push('✅ hide方法存在');
                } else {
                    checks.push('❌ hide方法不存在');
                }
                
                // 检查模态框是否创建
                const modal = document.getElementById('kline-modal');
                if (modal) {
                    checks.push('✅ K线图模态框已创建');
                } else {
                    checks.push('❌ K线图模态框未创建');
                }
                
                const allPassed = checks.every(check => check.startsWith('✅'));
                const resultType = allPassed ? 'success' : 'error';
                
                updateResult('klineResult', `
                    <h4>K线图管理器测试结果：</h4>
                    ${checks.map(check => `<div>${check}</div>`).join('')}
                `, resultType);
                
            } catch (error) {
                updateResult('klineResult', `
                    <h4>K线图管理器测试失败：</h4>
                    <div>❌ 错误信息: ${error.message}</div>
                    <div>❌ 详细错误: ${error.stack}</div>
                `, 'error');
            }
        }
        
        function testStockData() {
            updateResult('stockResult', '正在测试股票数据显示...', 'info');
            
            if (!testKlineManager) {
                updateResult('stockResult', '❌ 请先运行K线图管理器测试', 'error');
                return;
            }
            
            try {
                // 模拟股票数据
                const testStock = {
                    code: '688520',
                    name: '神州细胞',
                    date: '2025-07-03'
                };
                
                // 尝试显示K线图
                testKlineManager.show(testStock.code, testStock.name, testStock.date);
                
                // 检查模态框是否显示
                setTimeout(() => {
                    const modal = document.getElementById('kline-modal');
                    const isVisible = modal && modal.style.display !== 'none';
                    
                    if (isVisible) {
                        updateResult('stockResult', `
                            <h4>股票数据测试成功：</h4>
                            <div>✅ K线图模态框已显示</div>
                            <div>✅ 股票代码: ${testStock.code}</div>
                            <div>✅ 股票名称: ${testStock.name}</div>
                            <div>✅ 日期: ${testStock.date}</div>
                            <div style="margin-top: 10px;">
                                <button class="btn" onclick="testKlineManager.hide()">关闭K线图</button>
                            </div>
                        `, 'success');
                    } else {
                        updateResult('stockResult', `
                            <h4>股票数据测试失败：</h4>
                            <div>❌ K线图模态框未显示</div>
                        `, 'error');
                    }
                }, 500);
                
            } catch (error) {
                updateResult('stockResult', `
                    <h4>股票数据测试失败：</h4>
                    <div>❌ 错误信息: ${error.message}</div>
                `, 'error');
            }
        }
        
        function runFullTest() {
            updateResult('fullResult', '正在运行完整测试...', 'info');
            
            // 依次运行所有测试
            testEnvironment();
            
            setTimeout(() => {
                testKlineManager();
                
                setTimeout(() => {
                    testStockData();
                    
                    setTimeout(() => {
                        updateResult('fullResult', `
                            <h4>完整测试完成！</h4>
                            <div>✅ 所有测试已执行</div>
                            <div>📋 请查看上方各项测试结果</div>
                            <div style="margin-top: 10px;">
                                <a href="复盘分析_精简版.html" class="btn">🚀 前往主页面测试</a>
                            </div>
                        `, 'success');
                    }, 1000);
                }, 500);
            }, 500);
        }
        
        // 页面加载完成后自动检查环境
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 K线图功能测试页面已加载');
            setTimeout(testEnvironment, 500);
        });
    </script>
</body>
</html>
