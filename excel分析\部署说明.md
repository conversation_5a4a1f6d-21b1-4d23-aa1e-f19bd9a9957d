# 通达信复盘系统部署说明

## 问题描述
在其他机器上运行时出现错误：
```
❌ 无法导入excel_processor模块，请确保在正确的目录下运行
```

## 解决方案

### 方法一：使用部署检查脚本（推荐）

1. **拷贝整个excel分析文件夹**到目标机器
2. **运行部署检查脚本**：
   ```bash
   python 部署到其他机器.py
   ```
3. **按照提示修复问题**，脚本会自动：
   - 检查Python版本
   - 验证必要文件
   - 安装依赖
   - 创建目录
   - 测试模块导入

### 方法二：手动部署

1. **确保Python版本**：需要Python 3.7或更高版本
2. **拷贝所有文件**：
   ```
   excel分析/
   ├── excel_processor.py          # 核心处理器
   ├── web_monitor.py             # Web服务器
   ├── complete_monitor.py        # 完整监控
   ├── data_monitor.py            # 数据监控
   ├── 启动监控服务器.py           # 启动脚本
   ├── requirements.txt           # 依赖列表
   └── 部署到其他机器.py           # 部署检查脚本
   ```

3. **安装依赖**：
   ```bash
   pip install -r requirements.txt
   ```

4. **创建目录结构**：
   ```
   项目根目录/
   ├── excel分析/                 # 程序文件
   └── 复盘数据/                  # 数据文件夹
   ```

5. **运行系统**：
   ```bash
   python 启动监控服务器.py
   ```

## 修复内容

### 1. 模块导入路径修复
- 在所有主要脚本中添加了当前目录到Python路径
- 改进了错误提示信息，显示详细的路径信息

### 2. 启动脚本增强
- `启动监控服务器.py`：自动切换到脚本所在目录
- 检查必要文件是否存在
- 提供更详细的错误信息

### 3. 部署检查脚本
- `部署到其他机器.py`：全面的部署环境检查
- 自动安装依赖
- 创建必要目录
- 测试模块导入

## 使用流程

### 首次部署
1. 将整个`excel分析`文件夹拷贝到目标机器
2. 运行`python 部署到其他机器.py`
3. 按照提示解决任何问题
4. 运行`python 启动监控服务器.py`

### 日常使用
1. 直接运行`python 启动监控服务器.py`
2. 将通达信导出的.xls文件放入`复盘数据`目录
3. 访问 http://localhost:8000 查看结果

## 故障排除

### 常见问题

1. **模块导入失败**
   - 确保所有.py文件在同一目录下
   - 运行部署检查脚本验证环境

2. **依赖缺失**
   - 运行：`pip install pandas watchdog openpyxl`
   - 或使用：`pip install -r requirements.txt`

3. **目录不存在**
   - 手动创建`复盘数据`目录
   - 或运行部署检查脚本自动创建

4. **权限问题**
   - 确保对目录有读写权限
   - Windows下可能需要以管理员身份运行

### 验证部署
运行以下命令验证系统是否正常：
```bash
python -c "from excel_processor import TongDaXinProcessor; print('✅ 导入成功')"
```

## 技术说明

### 修复原理
原问题是Python无法找到`excel_processor`模块，因为：
1. 脚本不在Python的模块搜索路径中
2. 相对导入在不同工作目录下失效

### 解决方法
1. 动态添加脚本所在目录到`sys.path`
2. 使用绝对路径确定脚本位置
3. 在导入前切换到正确的工作目录

这样确保无论从哪个目录运行脚本，都能正确找到所需的模块。
