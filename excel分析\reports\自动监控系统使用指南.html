<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动监控系统使用指南</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        h2 { color: #34495e; border-bottom: 2px solid #e74c3c; padding-bottom: 10px; }
        .section { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #e74c3c; }
        .feature { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border: 1px solid #e9ecef; }
        .step { margin: 5px 0; padding: 8px; background: #ffe6e6; border-radius: 4px; }
        .auto { color: #28a745; font-weight: bold; }
        .manual { color: #dc3545; font-weight: bold; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; border: 1px solid #dee2e6; }
        .btn { padding: 10px 20px; background: #e74c3c; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #c0392b; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        .highlight { background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 自动监控系统使用指南</h1>
        
        <div class="status success">
            <strong>🎉 新功能：</strong>文件夹自动监控 + Web服务器集成
            <ul>
                <li>✅ 自动检测data目录中的文件变化</li>
                <li>✅ 自动更新index.json索引文件</li>
                <li>✅ 网页自动刷新显示最新数据</li>
                <li>✅ 模块化设计，不影响现有功能</li>
            </ul>
        </div>
        
        <h2>🚀 快速开始</h2>
        
        <div class="section">
            <h3>方法1：一键启动（推荐）</h3>
            <div class="feature">
                <div class="step">1. 双击运行 <code>启动监控服务器.py</code></div>
                <div class="step">2. 等待依赖检查和安装完成</div>
                <div class="step">3. 服务器自动启动，浏览器打开页面</div>
                <div class="step">4. 开始使用，文件变化将自动检测</div>
                
                <div class="highlight">
                    <strong>💡 优势：</strong>
                    <ul>
                        <li>自动检查和安装依赖</li>
                        <li>自动启动Web服务器和监控</li>
                        <li>一键解决所有问题</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>方法2：手动启动</h3>
            <div class="feature">
                <strong>步骤1：安装依赖</strong>
                <div class="code">
# 在excel分析目录下运行
python install_dependencies.py
                </div>
                
                <strong>步骤2：启动监控服务器</strong>
                <div class="code">
python web_monitor.py
                </div>
                
                <strong>步骤3：访问页面</strong>
                <div class="code">
http://localhost:8000/复盘分析_精简版.html
                </div>
            </div>
        </div>
        
        <h2>🔧 系统架构</h2>
        
        <div class="section">
            <h3>模块组成</h3>
            <div class="feature">
                <strong>📁 data_monitor.py</strong> - 数据监控核心模块
                <ul>
                    <li>监控 reports/data 目录的文件变化</li>
                    <li>自动更新 index.json 索引文件</li>
                    <li>验证JSON文件格式</li>
                    <li>支持独立运行</li>
                </ul>
                
                <strong>🌐 web_monitor.py</strong> - Web服务器集成模块
                <ul>
                    <li>集成HTTP服务器和数据监控</li>
                    <li>提供API端点供网页调用</li>
                    <li>自动切换到正确的工作目录</li>
                    <li>一体化解决方案</li>
                </ul>
                
                <strong>📦 install_dependencies.py</strong> - 依赖管理模块
                <ul>
                    <li>自动检查Python环境</li>
                    <li>安装所需的依赖包</li>
                    <li>提供详细的安装反馈</li>
                </ul>
            </div>
        </div>
        
        <h2>⚡ 自动化功能</h2>
        
        <div class="section">
            <h3>文件监控功能</h3>
            <div class="feature">
                <strong class="auto">✅ 自动检测以下操作：</strong>
                <div class="step">• 新增日期文件（date_YYYY-MM-DD.json）</div>
                <div class="step">• 修改现有日期文件</div>
                <div class="step">• 删除日期文件</div>
                <div class="step">• 文件重命名</div>
                
                <strong class="auto">✅ 自动执行以下操作：</strong>
                <div class="step">• 验证JSON文件格式</div>
                <div class="step">• 更新index.json索引文件</div>
                <div class="step">• 记录详细的操作日志</div>
                <div class="step">• 防抖处理，避免频繁更新</div>
            </div>
        </div>
        
        <div class="section">
            <h3>网页自动刷新</h3>
            <div class="feature">
                <strong class="auto">✅ 智能刷新机制：</strong>
                <div class="step">• 每30秒检查一次数据更新</div>
                <div class="step">• 只在数据真正变化时刷新</div>
                <div class="step">• 保持当前筛选状态</div>
                <div class="step">• 保持当前选中行</div>
                <div class="step">• 显示更新提示信息</div>
            </div>
        </div>
        
        <h2>📋 使用场景</h2>
        
        <div class="section">
            <h3>日常数据更新</h3>
            <div class="feature">
                <strong>场景：</strong>每日复盘数据处理
                <div class="step">1. 启动监控服务器</div>
                <div class="step">2. 将新的Excel文件处理成JSON格式</div>
                <div class="step">3. 复制到 reports/data 目录</div>
                <div class="step">4. 系统自动检测并更新索引</div>
                <div class="step">5. 网页自动刷新显示新数据</div>
                <div class="step">6. 无需任何手动操作</div>
            </div>
        </div>
        
        <div class="section">
            <h3>批量数据导入</h3>
            <div class="feature">
                <strong>场景：</strong>导入历史数据或批量更新
                <div class="step">1. 启动监控服务器</div>
                <div class="step">2. 批量复制多个日期文件到data目录</div>
                <div class="step">3. 系统自动检测所有变化</div>
                <div class="step">4. 自动更新索引，包含所有新日期</div>
                <div class="step">5. 网页显示完整的日期列表</div>
            </div>
        </div>
        
        <h2>🔍 API接口</h2>
        
        <div class="section">
            <h3>监控API端点</h3>
            <div class="feature">
                <strong>GET /api/monitor/status</strong> - 获取监控状态
                <div class="code">
{
  "running": true,
  "timestamp": 1690123456.789,
  "data_dir": "reports/data"
}
                </div>
                
                <strong>GET /api/monitor/update</strong> - 手动触发索引更新
                <div class="code">
{
  "success": true,
  "message": "索引更新成功",
  "timestamp": 1690123456.789
}
                </div>
                
                <strong>GET /api/data/refresh</strong> - 获取最新数据索引
                <div class="code">
{
  "success": true,
  "data": {
    "available_dates": ["2025-07-01", "2025-07-02"],
    "total_dates": 2,
    "last_updated": "2025-07-23T22:06:20.741072"
  },
  "timestamp": 1690123456.789
}
                </div>
            </div>
        </div>
        
        <h2>📊 监控日志</h2>
        
        <div class="section">
            <h3>日志文件</h3>
            <div class="feature">
                <strong>data_monitor.log</strong> - 详细的监控日志
                <div class="code">
2025-07-23 22:06:20 - INFO - 📁 检测到新文件: date_2025-07-04.json
2025-07-23 22:06:20 - INFO - ✅ 文件格式验证通过: 格式正确，包含150条记录
2025-07-23 22:06:20 - INFO - ✅ 新增日期: ['2025-07-04']
2025-07-23 22:06:20 - INFO - 📊 索引更新完成: 4 个日期
2025-07-23 22:06:20 - INFO - 🔄 索引文件已自动更新
                </div>
            </div>
        </div>
        
        <h2>🛠️ 故障排除</h2>
        
        <div class="section">
            <h3>常见问题</h3>
            <div class="feature">
                <strong>问题：依赖安装失败</strong>
                <div class="step">• 确保Python版本 >= 3.6</div>
                <div class="step">• 检查网络连接</div>
                <div class="step">• 尝试手动安装：pip install watchdog</div>
                
                <strong>问题：监控不工作</strong>
                <div class="step">• 检查data目录是否存在</div>
                <div class="step">• 查看data_monitor.log日志文件</div>
                <div class="step">• 确认文件名格式：date_YYYY-MM-DD.json</div>
                
                <strong>问题：网页不自动刷新</strong>
                <div class="step">• 确认使用web_monitor.py启动</div>
                <div class="step">• 检查浏览器控制台错误</div>
                <div class="step">• 访问 /api/monitor/status 检查API</div>
            </div>
        </div>
        
        <h2>🎯 最佳实践</h2>
        
        <div class="section">
            <h3>推荐工作流程</h3>
            <div class="feature">
                <div class="step">1. <strong>启动监控</strong>：运行 启动监控服务器.py</div>
                <div class="step">2. <strong>数据处理</strong>：使用现有的Excel处理工具</div>
                <div class="step">3. <strong>文件复制</strong>：将JSON文件复制到data目录</div>
                <div class="step">4. <strong>自动更新</strong>：系统自动检测和更新</div>
                <div class="step">5. <strong>验证结果</strong>：在网页中查看新数据</div>
                <div class="step">6. <strong>持续监控</strong>：保持服务器运行，随时更新</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="复盘分析_精简版.html" class="btn">🔄 返回主页面</a>
            <button class="btn" onclick="checkMonitorStatus()">🔍 检查监控状态</button>
        </div>
    </div>
    
    <script>
        // 检查监控状态
        async function checkMonitorStatus() {
            try {
                const response = await fetch('/api/monitor/status');
                if (response.ok) {
                    const status = await response.json();
                    if (status.running) {
                        alert('✅ 监控系统运行正常\n' + 
                              `📂 监控目录: ${status.data_dir}\n` +
                              `⏰ 检查时间: ${new Date(status.timestamp * 1000).toLocaleString()}`);
                    } else {
                        alert('⚠️ 监控系统未运行');
                    }
                } else {
                    alert('❌ 无法连接到监控API\n请确认使用 web_monitor.py 启动服务器');
                }
            } catch (error) {
                alert('❌ 监控状态检查失败\n' + error.message);
            }
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🤖 自动监控系统使用指南已加载');
        });
    </script>
</body>
</html>
