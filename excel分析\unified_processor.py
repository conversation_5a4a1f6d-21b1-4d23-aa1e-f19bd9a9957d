#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一处理器 - 整合两种方案的优势
结合excel_processor.py的智能检测和enhanced_auto_fixer.py的容错能力
"""

import pandas as pd
import json
import re
from pathlib import Path
import chardet
import logging
from typing import Optional, List, Dict
from datetime import datetime

class UnifiedProcessor:
    """
    统一处理器 - 整合新旧方案优势
    
    优势整合：
    1. 智能编码检测（来自excel_processor.py）
    2. 容错读取能力（来自enhanced_auto_fixer.py）
    3. 完整数据清洗（来自excel_processor.py）
    4. 特殊编码处理（来自enhanced_auto_fixer.py）
    5. 统一的股票代码格式处理
    """
    
    def __init__(self, data_dir: str = "../复盘数据", output_dir: str = "reports/data"):
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.logger = self._setup_logger()
        
        # 编码检测顺序（智能+历史成功）
        self.encodings = ['gbk', 'gb18030', 'gb2312', 'utf-8']
        
        # 字段类型映射
        self.field_types = {
            '代码': str,      # 股票代码必须是字符串
            '名称': str,      # 股票名称
            '涨幅%': float,   # 涨跌幅
            '收盘': float,    # 收盘价
            '总金额': int,    # 成交金额
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('UnifiedProcessor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def detect_encoding_smart(self, file_path: Path) -> str:
        """
        智能编码检测（来自excel_processor.py）
        """
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)
                result = chardet.detect(raw_data)
                detected_encoding = result.get('encoding', 'gbk')
                confidence = result.get('confidence', 0)
            
            self.logger.info(f"文件 {file_path.name} 检测编码: {detected_encoding} (置信度: {confidence:.2f})")
            
            # 对于通达信文件，优先使用中文编码
            if detected_encoding and detected_encoding.lower() in ['gb2312', 'gbk', 'gb18030']:
                return detected_encoding.lower()
            
            # 如果置信度较低，使用默认编码
            if confidence < 0.7:
                return 'gbk'
            
            return detected_encoding.lower() if detected_encoding else 'gbk'
        except Exception as e:
            self.logger.warning(f"编码检测失败: {e}，使用默认编码 gbk")
            return 'gbk'
    
    def read_file_standard(self, file_path: Path) -> Optional[pd.DataFrame]:
        """
        标准方式读取文件（来自excel_processor.py的逻辑）
        """
        try:
            # 智能编码检测
            detected_encoding = self.detect_encoding_smart(file_path)
            
            # 构建编码尝试列表
            encoding_list = [detected_encoding]
            for enc in self.encodings:
                if enc != detected_encoding:
                    encoding_list.append(enc)
            
            # 尝试TSV格式读取
            for encoding in encoding_list:
                try:
                    self.logger.info(f"尝试标准TSV格式，编码: {encoding}")
                    
                    # 智能检测需要跳过的行数
                    for skip_rows in [1, 2, 3, 0]:
                        try:
                            df = pd.read_csv(file_path, encoding=encoding, sep='\t',
                                           skiprows=skip_rows, low_memory=False, dtype=str)
                            
                            if not df.empty and len(df.columns) > 5:
                                # 验证列名是否包含中文字符
                                column_names = list(df.columns)
                                has_chinese = any('\u4e00' <= char <= '\u9fff' for col in column_names for char in str(col))
                                
                                if has_chinese:
                                    self.logger.info(f"✅ 标准TSV读取成功，编码: {encoding}，数据形状: {df.shape}")
                                    return df
                        except:
                            continue
                except Exception as e:
                    self.logger.debug(f"标准TSV读取失败，编码{encoding}: {str(e)[:100]}")
                    continue
            
            return None
        except Exception as e:
            self.logger.error(f"标准读取失败: {e}")
            return None
    
    def read_file_fallback(self, file_path: Path) -> Optional[List[Dict]]:
        """
        容错方式读取文件（来自enhanced_auto_fixer.py的逻辑）
        """
        try:
            self.logger.info("使用容错方式读取文件...")
            
            # 历史成功的编码顺序
            historical_encodings = ['gb2312', 'gbk', 'gb18030', 'utf-8']
            
            for encoding in historical_encodings:
                try:
                    self.logger.info(f"尝试容错读取，编码: {encoding}")
                    
                    # 容错读取
                    with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                        lines = f.readlines()
                    
                    # 智能表头查找
                    header_line_idx = -1
                    for i, line in enumerate(lines):
                        if '代码' in line and '名称' in line:
                            header_line_idx = i
                            break
                    
                    if header_line_idx == -1:
                        continue
                    
                    # 手动TSV解析
                    header_line = lines[header_line_idx].strip()
                    headers = [col.strip() for col in header_line.split('\t')]
                    
                    # 验证列名
                    has_chinese = any('\u4e00' <= char <= '\u9fff' for col in headers for char in str(col))
                    if not has_chinese:
                        continue
                    
                    # 解析数据行
                    data_rows = []
                    for i in range(header_line_idx + 1, len(lines)):
                        line = lines[i].strip()
                        if line:
                            values = line.split('\t')
                            if len(values) >= len(headers):
                                row_dict = {}
                                for j, header in enumerate(headers):
                                    if j < len(values):
                                        value = values[j].strip()
                                        # 清理Excel公式格式
                                        value = self.clean_excel_formula(value)
                                        
                                        if value == 'nan' or value == '':
                                            row_dict[header] = None
                                        else:
                                            row_dict[header] = value
                                    else:
                                        row_dict[header] = None
                                data_rows.append(row_dict)
                    
                    if len(data_rows) > 0:
                        self.logger.info(f"✅ 容错读取成功，编码: {encoding}，数据行数: {len(data_rows)}")
                        return data_rows
                        
                except Exception as e:
                    self.logger.debug(f"容错读取失败，编码{encoding}: {str(e)[:100]}")
                    continue
            
            return None
        except Exception as e:
            self.logger.error(f"容错读取失败: {e}")
            return None
    
    def clean_excel_formula(self, value: str) -> str:
        """清理Excel公式格式"""
        if isinstance(value, str):
            # 移除Excel公式前缀 ="
            value = re.sub(r'^="', '', value)
            value = re.sub(r'"$', '', value)
            value = value.strip()
        return value
    
    def clean_data_unified(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        统一数据清洗（来自excel_processor.py，增强版）
        """
        if df is None or df.empty:
            return df
        
        self.logger.info(f"开始统一数据清洗，原始形状: {df.shape}")
        
        # 1. 移除完全空的行和列
        df = df.dropna(how='all')
        df = df.dropna(axis=1, how='all')
        
        # 2. 处理通达信特殊格式
        if len(df) > 0 and any('历史' in str(cell) or '回测' in str(cell) for cell in df.iloc[0]):
            self.logger.info("检测到标题行，移除第一行")
            df = df.iloc[1:].reset_index(drop=True)
        
        # 3. 清理列名
        df.columns = [str(col).strip() for col in df.columns]
        
        # 4. 处理数据中的特殊字符和Excel公式
        for col in df.columns:
            if df[col].dtype == 'object':
                # 清理Excel公式格式
                df[col] = df[col].astype(str).apply(self.clean_excel_formula)
                # 清理空白字符
                df[col] = df[col].str.strip()
        
        # 5. 重置索引
        df = df.reset_index(drop=True)
        
        # 6. 移除完全空的行
        df = df.dropna(how='all')
        
        self.logger.info(f"统一数据清洗完成，最终形状: {df.shape}")
        return df
    
    def convert_to_json_unified(self, data, date: str) -> List[Dict]:
        """
        统一JSON转换（智能类型处理）
        """
        if isinstance(data, pd.DataFrame):
            # pandas DataFrame处理
            records = data.to_dict('records')
        else:
            # 已经是字典列表
            records = data
        
        # 统一的类型转换和格式处理
        for record in records:
            for key, value in record.items():
                if pd.isna(value) if hasattr(pd, 'isna') else value in [None, 'nan', '']:
                    record[key] = None
                elif key == '代码':
                    # 股票代码必须保持字符串格式
                    if isinstance(value, float):
                        record[key] = str(value).replace('.0', '')
                    else:
                        record[key] = str(value)
                else:
                    # 其他字段智能类型转换
                    try:
                        if isinstance(value, str):
                            if '.' in value and value.replace('.', '').replace('-', '').isdigit():
                                record[key] = float(value)
                            elif value.isdigit():
                                record[key] = int(value)
                            else:
                                record[key] = value
                        elif isinstance(value, float) and value.is_integer():
                            record[key] = int(value)
                        else:
                            record[key] = value
                    except:
                        record[key] = value
        
        self.logger.info(f"JSON转换完成，日期 {date} 包含 {len(records)} 条记录")
        return records
    
    def process_file(self, file_path: Path) -> bool:
        """
        统一文件处理流程
        """
        self.logger.info(f"=== 开始处理文件: {file_path.name} ===")
        
        # 提取日期
        date = self.extract_date_from_filename(file_path.name)
        if not date:
            self.logger.error(f"无法从文件名提取日期: {file_path.name}")
            return False
        
        # 第一步：尝试标准方式读取
        df = self.read_file_standard(file_path)
        
        if df is not None:
            self.logger.info("✅ 使用标准方式成功读取")
            # 标准数据清洗
            df = self.clean_data_unified(df)
            # 转换为JSON
            json_data = self.convert_to_json_unified(df, date)
        else:
            self.logger.warning("❌ 标准方式失败，尝试容错方式...")
            # 第二步：使用容错方式读取
            data_rows = self.read_file_fallback(file_path)
            
            if data_rows is not None:
                self.logger.info("✅ 使用容错方式成功读取")
                # 转换为JSON
                json_data = self.convert_to_json_unified(data_rows, date)
            else:
                self.logger.error("❌ 所有方式都失败了")
                return False
        
        # 保存数据
        if self.save_date_data(json_data, date):
            self.logger.info(f"🎉 文件处理成功: {file_path.name} -> {date}")
            return True
        else:
            self.logger.error(f"❌ 数据保存失败: {file_path.name}")
            return False
    
    def extract_date_from_filename(self, filename: str) -> Optional[str]:
        """从文件名提取日期"""
        import re
        
        # 匹配 YYYYMMDD 格式
        pattern = r'(\d{8})'
        match = re.search(pattern, filename)
        
        if match:
            date_str = match.group(1)
            try:
                # 转换为 YYYY-MM-DD 格式
                year = date_str[:4]
                month = date_str[4:6]
                day = date_str[6:8]
                return f"{year}-{month}-{day}"
            except:
                pass
        
        return None
    
    def save_date_data(self, data: List[Dict], date: str) -> bool:
        """保存数据到JSON文件"""
        try:
            output_file = self.output_dir / f"date_{date}.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"数据已保存到: {output_file}")
            return True
        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='统一处理器 - 整合新旧方案优势')
    parser.add_argument('--data-dir', default='../复盘数据', help='数据目录路径')
    parser.add_argument('--output-dir', default='reports/data', help='输出目录路径')
    parser.add_argument('--file', help='处理单个文件')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger('UnifiedProcessor').setLevel(logging.DEBUG)
    
    # 创建处理器
    processor = UnifiedProcessor(args.data_dir, args.output_dir)
    
    if args.file:
        # 处理单个文件
        file_path = Path(args.file)
        if file_path.exists():
            success = processor.process_file(file_path)
            if success:
                print("🎉 文件处理成功！")
            else:
                print("❌ 文件处理失败！")
        else:
            print(f"❌ 文件不存在: {file_path}")
    else:
        # 处理目录中的所有文件
        data_dir = Path(args.data_dir)
        if not data_dir.exists():
            print(f"❌ 数据目录不存在: {data_dir}")
            return
        
        success_count = 0
        total_count = 0
        
        for file_path in data_dir.glob("*.xls*"):
            total_count += 1
            if processor.process_file(file_path):
                success_count += 1
        
        print(f"🎉 处理完成！成功: {success_count}/{total_count}")

if __name__ == "__main__":
    main()
