#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强的自动化修复工具 - 主要编码修复工具
集成历史成功方法到主处理流程，实现特殊编码文件的自动处理

这是项目中的主要编码修复工具，整合了所有成功的修复方法：
- 基于2025年7月17日成功修复经验
- 支持批量处理多个特殊编码文件
- 包含完整的验证和错误处理机制
- 自动应用历史成功的编码处理策略

使用方法：
1. 批量修复所有特殊文件：python enhanced_auto_fixer.py
2. 集成到主处理流程：python enhanced_auto_fixer.py --integrate

支持的特殊文件：
- 临时条件股_20250711_1.xls (2025-07-11)
- 临时条件股_20250715_1.xls (2025-07-15)
- 临时条件股_20250717_1.xls (2025-07-17)

创建日期：2025-08-01
最后更新：2025-08-01
状态：主要工具，已验证有效
"""

import pandas as pd
import json
from pathlib import Path
import chardet
import logging
import sys
import re

def setup_logger():
    """设置日志"""
    logger = logging.getLogger('EnhancedAutoFixer')
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def enhanced_auto_fix_special_files():
    """增强的自动化修复特殊编码文件"""
    
    logger = setup_logger()
    
    # 需要修复的特殊文件列表
    special_files = [
        ("临时条件股_20250711_1.xls", "date_2025-07-11.json"),
        ("临时条件股_20250715_1.xls", "date_2025-07-15.json"),
        ("临时条件股_20250717_1.xls", "date_2025-07-17.json"),
        ("临时条件股_20250730_1.xls", "date_2025-07-30.json")
    ]
    
    success_count = 0
    
    for source_file, output_file in special_files:
        logger.info(f"=== 处理文件: {source_file} ===")
        
        file_path = Path(f"../复盘数据/{source_file}")
        output_path = Path(f"reports/data/{output_file}")
        
        if not file_path.exists():
            logger.warning(f"源文件不存在: {file_path}")
            continue
        
        # 使用历史成功方法修复
        success = fix_single_special_file(file_path, output_path, logger)
        if success:
            success_count += 1
            logger.info(f"✅ {source_file} 修复成功")
        else:
            logger.error(f"❌ {source_file} 修复失败")
    
    logger.info(f"🎉 修复完成！成功: {success_count}/{len(special_files)}")
    return success_count == len(special_files)

def clean_excel_formula(value):
    """清理Excel公式格式，处理 ="300066" -> 300066"""
    if isinstance(value, str):
        # 移除Excel公式前缀 ="
        value = re.sub(r'^="', '', value)
        value = re.sub(r'"$', '', value)
        value = value.strip()
    return value

def fix_single_special_file(file_path: Path, output_path: Path, logger):
    """修复单个特殊编码文件"""
    
    # 历史成功的编码顺序
    historical_encodings = ['gb2312', 'gbk', 'gb18030', 'utf-8']
    
    for encoding in historical_encodings:
        try:
            logger.info(f"尝试编码: {encoding}")
            
            # 使用历史成功的容错模式
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                lines = f.readlines()
            
            logger.info(f"文件总行数: {len(lines)}")
            
            # 历史成功的智能表头查找方法
            header_line_idx = -1
            for i, line in enumerate(lines):
                if '代码' in line and '名称' in line:
                    header_line_idx = i
                    logger.info(f"找到表头行: {i+1}")
                    break
            
            if header_line_idx == -1:
                logger.warning("未找到表头行，尝试下一个编码")
                continue
            
            # 历史成功的手动TSV解析方法
            header_line = lines[header_line_idx].strip()
            headers = [col.strip() for col in header_line.split('\t')]
            logger.info(f"列名数量: {len(headers)}")
            logger.info(f"前5个列名: {headers[:5]}")
            
            # 验证列名是否正确（历史验证方法）
            has_chinese = any('\u4e00' <= char <= '\u9fff' for col in headers for char in str(col))
            if not has_chinese:
                logger.warning(f"列名可能有乱码，尝试下一个编码")
                continue
            
            # 历史成功的数据解析方法
            data_rows = []
            for i in range(header_line_idx + 1, len(lines)):
                line = lines[i].strip()
                if line:  # 跳过空行
                    values = line.split('\t')
                    if len(values) >= len(headers):
                        # 创建数据行字典（历史方法）
                        row_dict = {}
                        for j, header in enumerate(headers):
                            if j < len(values):
                                value = values[j].strip()
                                # 清理Excel公式格式（新增）
                                value = clean_excel_formula(value)

                                # 历史成功的特殊值处理
                                if value == 'nan' or value == '':
                                    row_dict[header] = None
                                else:
                                    # 智能类型转换（修复股票代码问题）
                                    if header == '代码':
                                        # 股票代码必须保持字符串格式，保留前导零
                                        row_dict[header] = value
                                    else:
                                        # 其他字段进行数值转换
                                        try:
                                            if '.' in value:
                                                row_dict[header] = float(value)
                                            elif value.isdigit():
                                                row_dict[header] = int(value)
                                            else:
                                                row_dict[header] = value
                                        except:
                                            row_dict[header] = value
                            else:
                                row_dict[header] = None
                        data_rows.append(row_dict)
            
            logger.info(f"成功解析数据行: {len(data_rows)}")
            
            if len(data_rows) > 0:
                # 验证第一行数据的中文显示（历史验证方法）
                first_row = data_rows[0]
                first_row_values = [str(val) for val in first_row.values() if val is not None]
                first_row_chinese = any('\u4e00' <= char <= '\u9fff' for val in first_row_values for char in val)
                
                logger.info(f"第一行数据示例: {dict(list(first_row.items())[:3])}")
                logger.info(f"第一行包含中文: {first_row_chinese}")
                
                if first_row_chinese or '代码' in first_row:
                    # 确保输出目录存在
                    output_path.parent.mkdir(exist_ok=True)
                    
                    # 使用更严格的UTF-8保存方法
                    try:
                        # 先转换为JSON字符串，确保编码正确
                        json_str = json.dumps(data_rows, ensure_ascii=False, indent=2)
                        
                        # 验证JSON字符串中的中文
                        if any('\u4e00' <= char <= '\u9fff' for char in json_str):
                            # 使用UTF-8编码保存
                            with open(output_path, 'w', encoding='utf-8', newline='') as f:
                                f.write(json_str)
                            
                            logger.info(f"✅ 文件修复成功！")
                            logger.info(f"输出文件: {output_path}")
                            logger.info(f"数据行数: {len(data_rows)}")
                            logger.info(f"列数: {len(headers)}")
                            logger.info(f"使用编码: {encoding}")
                            
                            # 验证保存的文件
                            try:
                                with open(output_path, 'r', encoding='utf-8') as f:
                                    test_data = json.load(f)
                                    if len(test_data) > 0 and '名称' in test_data[0]:
                                        test_name = test_data[0]['名称']
                                        if any('\u4e00' <= char <= '\u9fff' for char in str(test_name)):
                                            logger.info(f"✅ 文件验证成功，中文显示正常: {test_name}")
                                            return True
                                        else:
                                            logger.warning(f"⚠️ 文件保存成功但中文显示异常: {test_name}")
                                    else:
                                        logger.warning("⚠️ 文件保存成功但结构异常")
                            except Exception as e:
                                logger.error(f"❌ 文件验证失败: {e}")
                                
                        else:
                            logger.warning("JSON字符串中没有中文字符，可能有问题")
                            
                    except Exception as e:
                        logger.error(f"保存文件时出错: {e}")
                        continue
                else:
                    logger.warning("数据中文显示异常，尝试下一个编码")
                    continue
            else:
                logger.warning("未找到有效数据行")
                continue
                
        except Exception as e:
            logger.warning(f"编码 {encoding} 失败: {str(e)[:100]}")
            continue
    
    logger.error("❌ 所有方法都失败了")
    return False

def integrate_to_main_processor():
    """将历史修复方法集成到主处理器"""
    
    logger = setup_logger()
    logger.info("=== 集成历史修复方法到主处理流程 ===")
    
    # 读取现有的excel_processor.py
    processor_path = Path("excel_processor.py")
    
    if not processor_path.exists():
        logger.error("主处理器文件不存在")
        return False
    
    # 这里可以添加代码来修改主处理器，集成历史修复方法
    # 为了安全起见，暂时不自动修改主处理器
    logger.info("建议手动将历史修复方法集成到主处理器中")
    logger.info("可以在excel_processor.py的read_file方法中添加特殊文件处理逻辑")
    
    return True

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--integrate":
        integrate_to_main_processor()
    else:
        enhanced_auto_fix_special_files()
