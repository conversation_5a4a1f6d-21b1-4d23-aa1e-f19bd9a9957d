<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化调试版本</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }
        .status { padding: 15px; margin: 10px 0; border-radius: 8px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .loading { text-align: center; padding: 20px; }
    </style>
</head>
<body>
    <h1>🔧 简化调试版本</h1>
    
    <div id="status" class="status info">开始初始化...</div>
    
    <div id="progress"></div>
    
    <div id="dataContainer"></div>
    
    <script>
        let step = 0;
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = `步骤 ${++step}: ${message}`;
            statusEl.className = `status ${type}`;
            console.log(`[步骤 ${step}] ${message}`);
        }
        
        function updateProgress(message) {
            const progressEl = document.getElementById('progress');
            progressEl.innerHTML += `<div>✓ ${message}</div>`;
        }
        
        async function simpleTest() {
            try {
                updateStatus('开始简化测试', 'info');
                updateProgress('测试开始');
                
                // 1. 测试基本fetch功能
                updateStatus('测试基本网络连接');
                const testResponse = await fetch(window.location.href);
                if (testResponse.ok) {
                    updateProgress('网络连接正常');
                } else {
                    throw new Error('网络连接异常');
                }
                
                // 2. 测试数据索引加载
                updateStatus('加载数据索引');
                const indexResponse = await fetch('data/index.json');
                if (!indexResponse.ok) {
                    throw new Error(`索引文件加载失败: ${indexResponse.status}`);
                }
                const indexData = await indexResponse.json();
                updateProgress(`索引加载成功: ${indexData.total_dates} 个日期`);
                
                // 3. 测试单个数据文件加载
                updateStatus('加载第一个数据文件');
                const firstDate = indexData.available_dates[0];
                const dataResponse = await fetch(`data/date_${firstDate}.json`);
                if (!dataResponse.ok) {
                    throw new Error(`数据文件加载失败: ${dataResponse.status}`);
                }
                const data = await dataResponse.json();
                updateProgress(`数据加载成功: ${data.length} 条记录`);
                
                // 4. 显示数据预览
                updateStatus('生成数据预览');
                displayDataPreview(data, firstDate);
                updateProgress('数据预览生成完成');
                
                updateStatus('所有测试通过！', 'success');
                
            } catch (error) {
                updateStatus(`测试失败: ${error.message}`, 'error');
                console.error('详细错误:', error);
            }
        }
        
        function displayDataPreview(data, date) {
            const container = document.getElementById('dataContainer');
            
            if (data.length === 0) {
                container.innerHTML = '<div class="status error">数据为空</div>';
                return;
            }
            
            const headers = Object.keys(data[0]);
            const displayHeaders = headers.slice(0, 8); // 只显示前8列
            
            let html = `
                <h3>📊 数据预览 - ${date}</h3>
                <p>总记录数: ${data.length} | 总字段数: ${headers.length}</p>
                <table>
                    <thead>
                        <tr>${displayHeaders.map(h => `<th>${h}</th>`).join('')}</tr>
                    </thead>
                    <tbody>
            `;
            
            // 显示前5行数据
            for (let i = 0; i < Math.min(5, data.length); i++) {
                const row = data[i];
                html += '<tr>';
                for (const header of displayHeaders) {
                    const value = row[header] || '';
                    html += `<td>${value}</td>`;
                }
                html += '</tr>';
            }
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面DOM加载完成，开始简化测试');
            setTimeout(simpleTest, 100); // 稍微延迟以确保页面完全加载
        });
    </script>
</body>
</html>
