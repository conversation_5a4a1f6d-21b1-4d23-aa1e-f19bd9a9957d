<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太初复盘</title>
    
    <!-- 现有样式文件 -->
    <link rel="stylesheet" href="date_pagination_styles.css">
    
    <!-- ECharts 图表库 - 使用本地文件 -->
    <script src="echarts.min.js"></script>
    
    <style>
        /* 基础样式 */
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            /* 确保页面稳定性 */
            min-height: 100vh;
            overflow-x: hidden; /* 防止水平滚动条 */
        }

        /* 确保HTML根元素稳定 */
        html {
            height: 100%;
            overflow-x: hidden;
        }

        .main-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
        }

        .header-section {
            flex-shrink: 0;
            background: #fff;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            margin: 0;
            color: #2c3e50;
            font-size: 1.5em;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-info {
            font-size: 12px;
            color: #666;
            font-weight: normal;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .date-selector {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            min-width: 150px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 10px;
            background: #3498db;
            color: white;
        }

        .btn:hover {
            background: #2980b9;
        }

        .content-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background: #fff;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 状态栏已合并到标题中，此样式已废弃 */

        /* 表格样式 */
        .table-section {
            flex: 1;
            background: #fff;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .table-wrapper {
            flex: 1;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin: 10px;
            /* 使用相对定位来控制滚动条位置 */
            position: relative;
            min-height: 400px; /* 增加最小高度确保滚动条出现 */
            max-height: 80vh; /* 恢复高度限制，但增加到80vh以利用更多空间 */
            display: flex;
            flex-direction: column;
        }

        /* 表格内容区域 */
        .table-content {
            flex: 1;
            overflow: auto !important; /* 显示所有滚动条 */
            scrollbar-width: auto !important;
            -webkit-overflow-scrolling: touch;
        }

        /* 横向滚动条容器 - 暂时隐藏 */
        .horizontal-scroll-container {
            display: none; /* 暂时隐藏，使用表格自带滚动条 */
        }

        /* 横向滚动条的内容占位 */
        .horizontal-scroll-content {
            height: 1px;
            /* 宽度将通过JavaScript动态设置 */
        }

        /* 垂直滚动条样式 */
        .table-content::-webkit-scrollbar {
            width: 14px !important;
            display: block !important;
        }

        .table-content::-webkit-scrollbar-track {
            background: #f1f1f1 !important;
            border-radius: 7px;
            display: block !important;
        }

        .table-content::-webkit-scrollbar-thumb {
            background: #888 !important;
            border-radius: 7px;
            display: block !important;
            min-height: 20px; /* 确保滚动条拖拽块有最小高度 */
        }

        .table-content::-webkit-scrollbar-thumb:hover {
            background: #555 !important;
        }

        /* 横向滚动条样式 */
        .horizontal-scroll-container::-webkit-scrollbar {
            height: 14px !important;
            display: block !important;
        }

        .horizontal-scroll-container::-webkit-scrollbar-track {
            background: #f1f1f1 !important;
            border-radius: 7px;
        }

        .horizontal-scroll-container::-webkit-scrollbar-thumb {
            background: #888 !important;
            border-radius: 7px;
            min-width: 20px;
        }

        .horizontal-scroll-container::-webkit-scrollbar-thumb:hover {
            background: #555 !important;
        }

        /* K线图容器 - 嵌入式显示 */
        .kline-section {
            flex-shrink: 0;
            background: #1e1e1e; /* 深色背景匹配K线图 */
            border: none;
            display: none; /* 默认隐藏 */
            position: relative;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }

        .kline-section.active {
            display: block;
        }

        /* K线图容器内部样式 */
        #klineChartContainer {
            width: 100%;
            height: 100%;
            background: #1e1e1e;
            border: none;
            margin: 0;
            padding: 0 10px;
            box-sizing: border-box;
        }

        /* 当K线图显示时的布局调整 - 完全清理模式 */
        .layout-with-kline .kline-section {
            height: 50vh;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
        }

        .layout-with-kline .table-section {
            height: 50vh;
            position: absolute;
            top: 50vh;
            left: 0;
            right: 0;
            min-height: 400px;
        }

        /* K线图模式下的表格优化 */
        .layout-with-kline .table-wrapper {
            max-height: calc(50vh - 40px);
            min-height: 480px;
            overflow-y: auto;
        }

        /* K线图显示时隐藏UI元素 */
        .layout-with-kline .header-section {
            transform: translateY(-100%);
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s ease;
        }

        /* 状态栏已合并到header-section中，无需单独隐藏 */

        /* K线图显示时调整内容区域 - 充分利用屏幕空间 */
        .layout-with-kline .content-section {
            height: 100vh;
            margin: 0;
            border-radius: 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 5;
            background: #1a1a1a;
        }

        /* 确保K线图从屏幕最顶部开始 */
        .layout-with-kline .main-container {
            height: 100vh;
            overflow: hidden;
        }

        /* 筛选面板样式 - 极简轻量化设计 */
        .filter-panel {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            display: none;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e1e5e9;
            backdrop-filter: blur(10px);
        }

        .filter-panel.active {
            display: block;
            animation: slideDownFromTop 0.3s ease;
        }

        .filter-content {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
            max-height: 80px;
            overflow: hidden;
        }

        .filter-title {
            font-weight: 500;
            font-size: 14px;
            white-space: nowrap;
            margin-right: 10px;
            color: #666;
        }

        .filter-input-group {
            flex: 1;
            min-width: 300px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-input-group input {
            flex: 1;
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            background: white;
            color: #333;
            transition: all 0.2s ease;
        }

        .filter-input-group input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .filter-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .filter-btn {
            background: white;
            border: 1px solid #d1d5db;
            color: #374151;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .filter-btn:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .filter-btn.primary {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        .filter-btn.primary:hover {
            background: #2563eb;
            border-color: #2563eb;
        }

        .filter-presets {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .preset-btn {
            background: #f3f4f6;
            border: 1px solid #e5e7eb;
            color: #6b7280;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .preset-btn:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .filter-close {
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background 0.2s ease;
            color: #6b7280;
        }

        .filter-close:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .filter-help-toggle {
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background 0.2s ease;
            color: #6b7280;
            margin-left: 8px;
        }

        .filter-help-toggle:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .filter-help {
            margin-top: 10px;
            padding: 12px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 12px;
        }

        .help-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .help-section {
            margin-bottom: 6px;
            line-height: 1.4;
        }

        .help-section strong {
            color: #1f2937;
        }

        .help-example {
            color: #6b7280;
            font-style: italic;
            display: block;
            margin-top: 2px;
        }

        .filter-status {
            position: fixed;
            top: 85px;
            right: 20px;
            z-index: 1001;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            max-width: 300px;
            display: none;
        }

        .filter-status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .filter-status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .filter-status.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes slideDownFromTop {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* K线图模式下隐藏筛选面板 */
        .layout-with-kline .filter-panel {
            display: none !important;
        }

        /* 当筛选面板显示时，为内容区域添加顶部边距 */
        .filter-panel.active ~ .main-container .content-section {
            margin-top: 80px;
            height: calc(100vh - 80px);
        }

        /* 加载指示器 */
        .loading-indicator {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            z-index: 10;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #333;
            border-top: 4px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        table.dataframe {
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
            background: white;
            /* 确保表格有足够高度触发滚动条 */
            min-height: 500px;
        }

        /* 确保表格容器有足够内容 */
        table.dataframe tbody {
            min-height: 400px;
            display: table-row-group;
        }

        table.dataframe th {
            white-space: nowrap;
            border: 1px solid #dee2e6;
            padding: 12px 15px;
            text-align: center;
            background: #f8f9fa;
            color: #333;
            font-weight: 600;
            font-size: 13px;
            position: sticky;
            top: 0;
            z-index: 10;
            cursor: pointer;
        }

        table.dataframe td {
            white-space: nowrap;
            border: 1px solid #dee2e6;
            padding: 10px 15px;
            text-align: center;
            font-size: 13px;
        }

        table.dataframe tr:nth-child(even) {
            background: #f8f9fa;
        }

        /* 表格行交互样式 */
        table.dataframe tbody tr {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        table.dataframe tbody tr:hover {
            background-color: #e3f2fd !important;
        }

        /* 高亮行样式 */
        table.dataframe tbody tr.highlighted {
            background-color: #f0f8ff !important;
            border-left: 4px solid #2196F3;
        }

        /* 列焦点样式 - 极简设计 */
        .column-focused {
            background-color: #f8f9fa !important;
            position: relative;
        }

        /* 表头列焦点样式 - 极简设计 */
        th.column-focused {
            background-color: #e9ecef !important;
            font-weight: bold;
        }

        /* 排序指示器样式 - 极简风格 */
        .sort-indicator {
            font-size: 12px;
            margin-left: 6px;
            color: #6c757d;
            font-weight: normal;
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        /* 排序指示器悬停效果 */
        th:hover .sort-indicator {
            opacity: 1;
        }

        .kline-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        /* K线图按钮样式 */

        .loading {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 18px;
            color: #666;
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 顶部区域 -->
        <div class="header-section">
            <h1 class="header-title">
                📊 太初复盘
                <span class="status-info">
                    <span id="statusText">正在初始化...</span> |
                    数据量: <span id="recordCount">-</span> |
                    当前日期: <span id="currentDate">-</span>
                </span>
            </h1>
            <div>
                <select id="dateSelector" class="date-selector">
                    <option value="">加载中...</option>
                </select>
                <button id="refreshBtn" class="btn">🔄 刷新</button>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="content-section" id="contentSection">
            <div id="loadingIndicator" class="loading">
                正在加载数据...
            </div>

            <div id="mainContent" class="hidden">
                <!-- K线图区域 - 嵌入式显示 -->
                <div class="kline-section" id="klineSection">
                    <div class="loading-indicator" id="klineLoadingIndicator">
                        <div class="loading-spinner"></div>
                        <div style="text-align: center;">正在加载K线图...</div>
                    </div>
                    <div id="klineChartContainer" style="width: 100%; height: 100%;"></div>
                </div>

                <!-- 表格区域 -->
                <div class="table-section" id="tableSection">
                    <div class="table-wrapper">
                        <div class="table-content">
                            <table class="dataframe">
                                <thead id="tableHead">
                                    <!-- 表头将动态生成 -->
                                </thead>
                                <tbody id="tableBody">
                                    <!-- 表格内容将动态生成 -->
                                </tbody>
                            </table>
                        </div>
                        <div class="horizontal-scroll-container" id="horizontalScrollContainer">
                            <div class="horizontal-scroll-content" id="horizontalScrollContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选面板 - 长横条设计 -->
    <div class="filter-panel" id="filterPanel">
        <div class="filter-content">
            <div class="filter-title">🔍 筛选</div>

            <div class="filter-input-group">
                <input type="text" id="filterInput" placeholder="例如: 涨幅% > 9.5 AND 量比 > 2 或 振幅 倒序前10 或 成交量 正序前20" onkeypress="handleFilterKeyPress(event)">
            </div>

            <div class="filter-buttons">
                <button onclick="applyFilter()" class="filter-btn primary">应用</button>
                <button onclick="clearFilter()" class="filter-btn">清除</button>
            </div>

            <div class="filter-presets">
                <button onclick="setPresetFilter('量比 > 2')" class="preset-btn">量比>2</button>
                <button onclick="setPresetFilter('振幅 正序前10')" class="preset-btn">振幅前10</button>
                <button onclick="setPresetFilter('倍55 倒序前10')" class="preset-btn">倍55前10</button>
                <button onclick="setPresetFilter('hupd33 倒序前10')" class="preset-btn">hupd33前10</button>
                <button onclick="setPresetFilter('hupd55 倒序前10')" class="preset-btn">hupd55前10</button>
                <button onclick="setPresetFilter('低量 倒序前10')" class="preset-btn">低量前10</button>
            </div>

            <div class="filter-help" id="filterHelp" style="display: none;">
                <div class="help-title">📖 筛选语法说明</div>
                <div class="help-content">
                    <div class="help-section">
                        <strong>比较筛选：</strong> 字段名 运算符 值<br>
                        <span class="help-example">例如：涨幅% > 9.5, 量比 >= 2, 名称 = "平安银行"</span>
                    </div>
                    <div class="help-section">
                        <strong>排序筛选：</strong> 字段名 倒序前N 或 字段名 正序前N<br>
                        <span class="help-example">例如：振幅 倒序前10, 成交量 正序前20</span>
                    </div>
                    <div class="help-section">
                        <strong>组合条件：</strong> 使用 AND、OR 连接多个条件<br>
                        <span class="help-example">例如：涨幅% > 5 AND 振幅 倒序前15</span>
                    </div>
                </div>
            </div>

            <span class="filter-close" onclick="toggleFilterSection()">&times;</span>
            <span class="filter-help-toggle" onclick="toggleFilterHelp()" title="显示/隐藏语法说明">❓</span>
        </div>
    </div>

    <!-- 筛选状态显示 -->
    <div class="filter-status" id="filterStatus"></div>

    <!-- 只加载必要的JS模块 -->
    <script src="table_sorter.js"></script>
    <!-- 不再需要kline_chart_helper.js，使用内置的嵌入式K线图管理器 -->
    
    <script>
        // 增强的应用状态
        const app = {
            currentDate: null,
            currentData: null,
            originalData: null, // 保存原始数据用于搜索
            indexData: null,
            highlightedIndex: -1,
            klineManager: null,
            searchState: {
                isActive: false,
                query: '',
                filteredData: null
            },
            autoRefresh: {
                enabled: true,
                interval: 30000, // 30秒检查一次
                lastCheck: null,
                timer: null
            }
        };

        // 全局变量以兼容现有JS模块
        window.globalHighlightedIndex = -1;
        window.globalKlineManager = null;
        window.globalDatePaginationManager = null;
        window.EMBEDDED_DATA = {};
        
        // 初始化应用
        async function initApp() {
            try {
                updateStatus('正在加载数据索引...');
                
                // 加载数据索引
                const indexResponse = await fetch('data/index.json');
                if (!indexResponse.ok) {
                    throw new Error('数据索引加载失败');
                }
                app.indexData = await indexResponse.json();

                // 预加载所有数据到EMBEDDED_DATA以兼容现有JS模块
                await preloadAllData();

                // 填充日期选择器
                populateDateSelector();

                // 初始化现有JS模块
                initializeModules();

                // 加载默认日期
                if (app.indexData.available_dates.length > 0) {
                    const latestDate = app.indexData.available_dates[app.indexData.available_dates.length - 1];
                    document.getElementById('dateSelector').value = latestDate;
                    await loadDateData(latestDate);
                }

                // 绑定事件
                bindEvents();

                // 启动自动刷新
                startAutoRefresh();

                updateStatus('系统就绪');
                
            } catch (error) {
                console.error('初始化失败:', error);
                updateStatus('初始化失败: ' + error.message);
            }
        }
        
        // 预加载所有数据以兼容现有JS模块
        async function preloadAllData() {
            updateStatus('正在预加载数据...');

            for (const date of app.indexData.available_dates) {
                try {
                    const response = await fetch(`data/date_${date}.json`);
                    if (response.ok) {
                        const data = await response.json();
                        window.EMBEDDED_DATA[date] = data;
                        console.log(`✅ 预加载完成: ${date} (${data.length}条记录)`);
                    }
                } catch (error) {
                    console.warn(`⚠️ 预加载失败: ${date}`, error);
                }
            }

            console.log(`🎉 数据预加载完成，共${Object.keys(window.EMBEDDED_DATA).length}个日期`);
        }

        // 获取默认日期
        function getDefaultDate() {
            if (app.indexData && app.indexData.available_dates.length > 0) {
                return app.indexData.available_dates[app.indexData.available_dates.length - 1];
            }
            return null;
        }

        // 绑定事件
        function bindEvents() {
            // 绑定日期选择器事件
            const dateSelector = document.getElementById('dateSelector');
            if (dateSelector) {
                dateSelector.addEventListener('change', function() {
                    loadDateData(this.value);
                });
            }

            // 绑定横向滚动同步
            setupHorizontalScrollSync();

            // 绑定其他事件
            console.log('✅ 事件绑定完成');
        }

        // 设置横向滚动同步
        function setupHorizontalScrollSync() {
            const tableContent = document.querySelector('.table-content');
            const horizontalScrollContainer = document.getElementById('horizontalScrollContainer');
            const horizontalScrollContent = document.getElementById('horizontalScrollContent');

            if (!tableContent || !horizontalScrollContainer || !horizontalScrollContent) {
                return;
            }

            // 更新横向滚动条宽度
            function updateHorizontalScrollWidth() {
                const table = tableContent.querySelector('table');
                if (table) {
                    const tableWidth = table.scrollWidth;
                    const containerWidth = tableContent.clientWidth;

                    if (tableWidth > containerWidth) {
                        horizontalScrollContent.style.width = tableWidth + 'px';
                        horizontalScrollContainer.style.display = 'block';
                    } else {
                        horizontalScrollContainer.style.display = 'none';
                    }
                }
            }

            // 同步滚动位置
            function syncScroll(source, target) {
                if (source.scrollLeft !== target.scrollLeft) {
                    target.scrollLeft = source.scrollLeft;
                }
            }

            // 绑定滚动事件
            tableContent.addEventListener('scroll', function() {
                syncScroll(tableContent, horizontalScrollContainer);
            });

            horizontalScrollContainer.addEventListener('scroll', function() {
                syncScroll(horizontalScrollContainer, tableContent);
            });

            // 监听表格内容变化
            const observer = new MutationObserver(updateHorizontalScrollWidth);
            observer.observe(tableContent, { childList: true, subtree: true });

            // 监听窗口大小变化
            window.addEventListener('resize', updateHorizontalScrollWidth);

            // 初始化
            setTimeout(updateHorizontalScrollWidth, 100);

            console.log('✅ 横向滚动同步已设置');
        }

        // 全局定时器管理
        let globalRefreshInterval = null;

        // 启动自动刷新
        function startAutoRefresh() {
            // 清理之前的定时器
            if (globalRefreshInterval) {
                clearInterval(globalRefreshInterval);
                console.log('🧹 清理之前的自动刷新定时器');
            }

            globalRefreshInterval = setInterval(async function() {
                try {
                    // 简化自动刷新请求，移除AbortController
                    const response = await fetch('/api/data/refresh', {
                        headers: {
                            'Cache-Control': 'no-cache'
                        }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.success && result.data) {
                            // 检查是否有新数据
                            const newDates = result.data.available_dates;
                            const currentDates = app.indexData.available_dates;

                            if (JSON.stringify(newDates) !== JSON.stringify(currentDates)) {
                                console.log('🔄 检测到数据更新，重新加载...');
                                location.reload();
                            }
                        }
                    }
                } catch (error) {
                    if (error.name === 'AbortError') {
                        console.warn('自动刷新请求超时');
                    } else {
                        console.warn('自动刷新检查失败:', error);
                        // 如果连续失败，增加检查间隔
                        clearInterval(globalRefreshInterval);
                        globalRefreshInterval = null;
                        setTimeout(() => {
                            startAutoRefresh();
                        }, 60000); // 1分钟后重试
                    }
                }
            }, 30000); // 30秒检查一次

            console.log('✅ 自动刷新已启动');
        }

        // 初始化基础模块（不包括K线图）
        function initializeBasicModules() {
            updateStatus('正在初始化基础功能模块...');

            // 初始化日期分页管理器
            if (typeof DatePaginationManager !== 'undefined') {
                // 暂时不使用DatePaginationManager，因为我们有自己的数据加载逻辑
                // 但保留接口兼容性
                window.globalDatePaginationManager = {
                    setSortState: function(column, direction) {
                        console.log(`排序状态更新: 列${column}, 方向${direction}`);
                    },
                    _isApplyingSort: false
                };
                console.log('✅ 日期分页管理器接口初始化完成');
            }

            console.log('✅ 基础功能模块初始化完成');
        }

        // 初始化现有JS模块（包括K线图）
        function initializeModules() {
            updateStatus('正在初始化功能模块...');

            // 先初始化基础模块
            initializeBasicModules();

            // 初始化嵌入式K线图管理器
            initializeEmbeddedKlineChart();

            console.log('🎉 所有功能模块初始化完成');
        }

        // 初始化嵌入式K线图
        function initializeEmbeddedKlineChart() {
            // 检查ECharts是否加载
            if (typeof echarts === 'undefined') {
                console.error('❌ ECharts库未加载，尝试重新加载...');

                // 尝试重新加载ECharts
                const script = document.createElement('script');
                script.src = 'echarts.min.js';
                script.onload = function() {
                    console.log('✅ ECharts库重新加载成功');
                    initializeEmbeddedKlineChart(); // 递归调用
                };
                script.onerror = function() {
                    console.error('❌ ECharts库加载失败，K线图功能将不可用');
                    // 创建一个空的K线图管理器，避免后续错误
                    app.klineManager = {
                        chart: null,
                        show: function() { console.warn('K线图功能不可用'); },
                        hide: function() {},
                        init: function() {}
                    };
                };
                document.head.appendChild(script);
                return;
            }

            console.log('✅ ECharts库已加载，版本:', echarts.version || 'unknown');

            // 创建嵌入式K线图管理器
            app.klineManager = {
                chart: null,
                currentStock: null,
                dataCache: new Map(),

                init: function() {
                    const container = document.getElementById('klineChartContainer');
                    if (container) {
                        this.chart = echarts.init(container, 'dark');
                        console.log('✅ 嵌入式K线图初始化完成');
                    }
                },

                show: function(code, name, date) {
                    this.currentStock = { code, name, date };
                    this.showKlineSection();
                    this.loadKlineData(code, name, date);
                },

                hide: function() {
                    this.hideKlineSection();
                    this.currentStock = null;
                },

                showKlineSection: function() {
                    const klineSection = document.getElementById('klineSection');
                    const contentSection = document.getElementById('contentSection');
                    const mainContainer = document.querySelector('.main-container');

                    if (klineSection && contentSection && mainContainer) {
                        klineSection.classList.add('active');
                        contentSection.classList.add('layout-with-kline');
                        mainContainer.classList.add('layout-with-kline');

                        // 重新调整图表大小
                        setTimeout(() => {
                            if (this.chart) {
                                this.chart.resize();
                            }
                        }, 300);

                        console.log('✅ K线图区域显示，UI元素已隐藏');
                    }
                },

                hideKlineSection: function() {
                    const klineSection = document.getElementById('klineSection');
                    const contentSection = document.getElementById('contentSection');
                    const mainContainer = document.querySelector('.main-container');

                    if (klineSection && contentSection && mainContainer) {
                        klineSection.classList.remove('active');
                        contentSection.classList.remove('layout-with-kline');
                        mainContainer.classList.remove('layout-with-kline');

                        console.log('✅ K线图区域隐藏，UI元素已恢复');
                    }
                },

                loadKlineData: async function(code, name, date) {
                    if (!this.chart) return;

                    // 显示加载指示器
                    const loadingIndicator = document.getElementById('klineLoadingIndicator');
                    if (loadingIndicator) {
                        loadingIndicator.style.display = 'block';
                    }

                    this.chart.showLoading();

                    // 获取真实K线数据
                    const data = await this.fetchKlineData(code, date);

                    this.chart.hideLoading();

                    if (data) {
                        this.renderChart(data, date, code, name);
                        console.log(`✅ K线图数据加载成功: ${name} (${code})`);
                    } else {
                        this.chart.showLoading({ text: '获取K线数据失败', color: '#ff6b6b' });
                        console.error(`❌ K线图数据获取失败: ${code}`);
                    }

                    // 隐藏加载指示器
                    setTimeout(() => {
                        if (loadingIndicator) {
                            loadingIndicator.style.display = 'none';
                        }
                    }, 500);
                },

                // 获取真实K线数据（移植自kline_chart_helper.js）
                fetchKlineData: async function(stockCode, selectedDate) {
                    const cacheKey = `${stockCode}_${selectedDate}`;
                    if (this.dataCache.has(cacheKey)) {
                        return this.dataCache.get(cacheKey);
                    }

                    const market = stockCode.startsWith('6') ? '1' : '0';
                    const secid = `${market}.${stockCode}`;
                    const end = new Date().toISOString().slice(0, 10).replace(/-/g, '');
                    const url = `https://push2his.eastmoney.com/api/qt/stock/kline/get?secid=${secid}&fields1=f1,f2,f3,f4,f5,f6&fields2=f51,f52,f53,f54,f55,f56,f57,f58&klt=101&fqt=1&end=${end}&lmt=240`;

                    try {
                        console.log(`🔍 正在获取K线数据: ${stockCode}`);
                        const response = await fetch(url);
                        const json = await response.json();

                        if (json && json.data && json.data.klines) {
                            const processed = this.processEastMoneyData(json.data);
                            this.dataCache.set(cacheKey, processed);
                            console.log(`✅ K线数据获取成功: ${stockCode}, ${processed.dates.length}条记录`);
                            return processed;
                        }

                        console.warn(`⚠️ K线数据格式异常: ${stockCode}`);
                        return null;
                    } catch (error) {
                        console.error(`❌ K线数据获取失败: ${stockCode}`, error);
                        return null;
                    }
                },

                // 处理东方财富数据格式（移植自kline_chart_helper.js）
                processEastMoneyData: function(data) {
                    const dates = data.klines.map(item => item.split(',')[0]);
                    const klineData = data.klines.map(item => {
                        const parts = item.split(',');
                        return [parseFloat(parts[1]), parseFloat(parts[2]), parseFloat(parts[4]), parseFloat(parts[3])]; // O, C, L, H
                    });
                    const volumes = data.klines.map(item => parseInt(item.split(',')[5]));
                    return { dates, klineData, volumes };
                },

                // 渲染K线图（移植并优化自kline_chart_helper.js）
                renderChart: function(data, selectedDate, stockCode, stockName) {
                    const selectedIndex = data.dates.indexOf(selectedDate);
                    const actualIndex = selectedIndex !== -1 ? selectedIndex : data.dates.length - 1;

                    const start = Math.max(0, actualIndex - 30);
                    const end = Math.min(data.dates.length - 1, actualIndex + 30);

                    const option = {
                        title: {
                            text: `${stockName} (${stockCode}) - ${selectedDate}`,
                            left: 'center',
                            top: '1%',
                            textStyle: {
                                color: '#ffffff',
                                fontSize: 14,
                                fontWeight: 'normal'
                            }
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'cross' }
                        },
                        grid: [
                            {
                                left: '3%',
                                right: '3%',
                                top: '8%',
                                height: '57%'
                            },
                            {
                                left: '3%',
                                right: '3%',
                                top: '75%',
                                height: '15%'
                            }
                        ],
                        xAxis: [
                            { type: 'category', data: data.dates, axisLabel: { show: false } },
                            { type: 'category', gridIndex: 1, data: data.dates }
                        ],
                        yAxis: [
                            {
                                scale: true,
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: 'rgba(128, 128, 128, 0.3)',
                                        type: 'dashed',
                                        width: 1
                                    }
                                }
                            },
                            {
                                scale: true,
                                gridIndex: 1,
                                axisLabel: { show: false },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: 'rgba(128, 128, 128, 0.3)',
                                        type: 'dashed',
                                        width: 1
                                    }
                                }
                            }
                        ],
                        dataZoom: [
                            { type: 'inside', xAxisIndex: [0, 1], startValue: start, endValue: end },
                            {
                                type: 'slider',
                                xAxisIndex: [0, 1],
                                top: '92%',
                                left: '3%',
                                right: '3%',
                                startValue: start,
                                endValue: end
                            }
                        ],
                        series: [
                            {
                                type: 'candlestick',
                                data: data.klineData,
                                markPoint: actualIndex !== -1 ? {
                                    data: [{
                                        name: 'Selected',
                                        coord: [actualIndex, data.klineData[actualIndex][3]],
                                        symbol: 'pin',
                                        symbolSize: 20,
                                        itemStyle: { color: '#FFD700' }
                                    }]
                                } : undefined
                            },
                            {
                                name: 'Volume',
                                type: 'bar',
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                data: data.volumes,
                                itemStyle: {
                                    color: ({ dataIndex }) => data.klineData[dataIndex][1] > data.klineData[dataIndex][0] ? '#ef4444' : '#22c55e'
                                }
                            }
                        ]
                    };

                    this.chart.setOption(option, true);
                    console.log('📊 K线图渲染完成，使用真实数据');
                }
            };

            // 初始化K线图
            app.klineManager.init();
            window.globalKlineManager = app.klineManager;

            console.log('✅ 嵌入式K线图管理器初始化完成');
        }

        // 填充日期选择器
        function populateDateSelector() {
            const selector = document.getElementById('dateSelector');
            selector.innerHTML = '';

            // 保持原始顺序，最新日期在前
            const sortedDates = [...app.indexData.available_dates].reverse();
            sortedDates.forEach(date => {
                const option = document.createElement('option');
                option.value = date;
                option.textContent = date;
                selector.appendChild(option);
            });
        }
        
        // 数据缓存
        const dataCache = new Map();

        // 页面卸载时清理所有定时器
        window.addEventListener('beforeunload', function() {
            if (globalRefreshInterval) {
                clearInterval(globalRefreshInterval);
                console.log('🧹 页面卸载，清理定时器');
            }
        });

        // 数据预加载
        async function startDataPreloading() {
            if (!app.indexData || !app.indexData.available_dates) {
                console.warn('⚠️ [预加载] 无可用日期数据，跳过预加载');
                return;
            }

            const currentDate = app.currentDate;
            const availableDates = app.indexData.available_dates;
            const currentIndex = availableDates.indexOf(currentDate);

            if (currentIndex === -1) {
                console.warn('⚠️ [预加载] 当前日期不在可用日期列表中');
                return;
            }

            // 预加载相邻的日期（前后各2个）
            const preloadDates = [];
            for (let i = -2; i <= 2; i++) {
                const index = currentIndex + i;
                if (index >= 0 && index < availableDates.length) {
                    const date = availableDates[index];
                    if (date !== currentDate && !dataCache.has(date)) {
                        preloadDates.push(date);
                    }
                }
            }

            if (preloadDates.length === 0) {
                console.log('📦 [预加载] 相邻日期数据已缓存，无需预加载');
                return;
            }

            console.log(`🚀 [预加载] 开始预加载 ${preloadDates.length} 个日期:`, preloadDates);

            // 异步预加载，不阻塞主线程
            for (const date of preloadDates) {
                try {
                    // 延迟预加载，避免影响当前操作
                    await new Promise(resolve => setTimeout(resolve, 2000)); // 增加延迟到2秒

                    // 检查是否已被其他操作加载
                    if (dataCache.has(date)) {
                        console.log(`📦 [预加载] 跳过已缓存: ${date}`);
                        continue;
                    }

                    const data = await loadDateDataWithRetry(date);
                    dataCache.set(date, data);
                    console.log(`✅ [预加载] 完成: ${date} (${data.length}条记录)`);
                } catch (error) {
                    // 预加载失败不影响主功能，静默处理
                    console.warn(`⚠️ [预加载] 失败: ${date} -`, error.message);
                }
            }

            console.log(`🎉 [预加载] 全部完成，缓存中共有 ${dataCache.size} 个日期数据`);
        }

        // 简化的数据加载（移除AbortController避免信号问题）
        async function loadDateDataWithRetry(date, retryCount = 0) {
            const maxRetries = 2;

            try {
                console.log(`🔄 [数据加载] 尝试加载 ${date} (第${retryCount + 1}次)`);

                // 简单的fetch请求，不使用AbortController
                const response = await fetch(`data/date_${date}.json`, {
                    headers: {
                        'Cache-Control': 'no-cache'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log(`✅ [数据加载] 成功加载 ${date} (${data.length}条记录)`);
                return data;

            } catch (error) {
                console.warn(`⚠️ [数据加载] 第${retryCount + 1}次尝试失败:`, error.message);

                if (retryCount < maxRetries) {
                    const delay = (retryCount + 1) * 1000; // 简单延迟：1s, 2s
                    console.log(`⏳ [数据加载] ${delay}ms后重试...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return loadDateDataWithRetry(date, retryCount + 1);
                } else {
                    throw new Error(`数据加载失败: ${error.message}`);
                }
            }
        }

        // 加载日期数据
        async function loadDateData(date) {
            try {
                updateStatus(`正在加载 ${date} 数据...`);

                // 检查缓存
                if (dataCache.has(date)) {
                    console.log(`📦 [缓存命中] 使用缓存数据: ${date}`);
                    const cachedData = dataCache.get(date);
                    app.currentData = cachedData;
                    app.originalData = [...cachedData];
                    app.currentDate = date;

                    // 如果有筛选状态，应用筛选
                    if (app.searchState.query) {
                        try {
                            console.log(`🔍 [缓存数据] 应用筛选条件: ${app.searchState.query}`);
                            const parsedConditions = parseFilterCondition(app.searchState.query);
                            const filteredData = applyFilterAndSort(app.originalData, parsedConditions);
                            app.currentData = filteredData;

                            // 渲染筛选后的数据
                            renderTable(app.currentData);

                            // 确保筛选面板状态保持
                            const filterInput = document.getElementById('filterInput');
                            const filterPanel = document.getElementById('filterPanel');

                            if (filterInput && filterInput.value !== app.searchState.query) {
                                filterInput.value = app.searchState.query;
                            }

                            // 确保筛选面板保持可见状态
                            if (filterPanel && !filterPanel.classList.contains('active')) {
                                filterPanel.classList.add('active');
                                app.searchState.isActive = true;
                                console.log('🔍 [缓存数据] 恢复筛选面板可见状态');
                            }

                            // 显示筛选状态
                            const filterStatus = document.getElementById('filterStatus');
                            if (filterStatus) {
                                filterStatus.className = 'filter-status success active';
                                filterStatus.textContent = `✅ 筛选已应用：找到 ${filteredData.length} 条记录`;

                                // 3秒后自动隐藏状态
                                setTimeout(() => {
                                    filterStatus.classList.remove('active');
                                }, 3000);
                            }

                            updateStatus(`已加载 ${date} 数据 (筛选后 ${filteredData.length} 条记录)`);
                            document.getElementById('recordCount').textContent = filteredData.length;
                            console.log(`🔍 [缓存数据] 筛选完成: ${filteredData.length}条结果`);
                        } catch (error) {
                            console.error('缓存数据筛选失败:', error);
                            // 筛选失败时显示原始数据
                            renderTable(app.currentData);
                            updateStatus(`已加载 ${date} 数据 (${cachedData.length} 条记录)`);
                            document.getElementById('recordCount').textContent = cachedData.length;
                        }
                    } else {
                        // 没有筛选条件，直接渲染原始数据
                        renderTable(app.currentData);
                        updateStatus(`已加载 ${date} 数据 (${cachedData.length} 条记录)`);
                        document.getElementById('recordCount').textContent = cachedData.length;
                    }

                    document.getElementById('currentDate').textContent = date;
                    return;
                }

                // 使用重试机制加载数据
                const data = await loadDateDataWithRetry(date);

                // 缓存数据
                dataCache.set(date, data);
                console.log(`💾 [缓存] 已缓存 ${date} 数据`);

                app.currentData = data;
                app.originalData = [...data]; // 保存原始数据
                app.currentDate = date;

                // 如果有筛选状态，应用筛选并保持筛选面板状态
                if (app.searchState.query) {
                    try {
                        const parsedConditions = parseFilterCondition(app.searchState.query);
                        const filteredData = applyFilterAndSort(app.originalData, parsedConditions);
                        app.currentData = filteredData;
                        renderTable(app.currentData);

                        // 确保筛选面板状态保持
                        const filterInput = document.getElementById('filterInput');
                        const filterPanel = document.getElementById('filterPanel');

                        if (filterInput && filterInput.value !== app.searchState.query) {
                            filterInput.value = app.searchState.query;
                        }

                        // 确保筛选面板保持可见状态
                        if (filterPanel && !filterPanel.classList.contains('active')) {
                            filterPanel.classList.add('active');
                            app.searchState.isActive = true;
                            console.log('🔍 [新数据] 恢复筛选面板可见状态');
                        }

                        // 显示筛选状态
                        const filterStatus = document.getElementById('filterStatus');
                        if (filterStatus) {
                            filterStatus.className = 'filter-status success active';
                            filterStatus.textContent = `✅ 筛选已应用：找到 ${filteredData.length} 条记录`;

                            // 3秒后自动隐藏状态
                            setTimeout(() => {
                                filterStatus.classList.remove('active');
                            }, 3000);
                        }

                        console.log(`🔍 日期切换后重新应用筛选: ${filteredData.length}条结果`);
                    } catch (error) {
                        console.error('重新应用筛选失败:', error);
                        renderTable(app.currentData);
                    }
                } else {
                    // 渲染表格
                    renderTable(app.currentData);
                }
                
                // 显示主要内容
                document.getElementById('loadingIndicator').classList.add('hidden');
                document.getElementById('mainContent').classList.remove('hidden');
                
                // 更新状态
                updateStatus('数据加载完成');
                document.getElementById('recordCount').textContent = app.currentData.length;
                document.getElementById('currentDate').textContent = date;
                
            } catch (error) {
                console.error('数据加载失败:', error);

                // 根据错误类型显示不同的提示
                if (error.message.includes('超时')) {
                    updateStatus(`⏰ 数据加载超时: ${date}，请稍后重试`);
                } else if (error.message.includes('AbortError') || error.message.includes('signal is aborted')) {
                    updateStatus(`🔄 请求被中断: ${date}，请重新尝试`);
                } else {
                    updateStatus(`❌ 数据加载失败: ${date} - ${error.message}`);
                }

                // 显示错误状态但不阻塞界面
                document.getElementById('loadingIndicator').classList.add('hidden');
                document.getElementById('mainContent').classList.remove('hidden');

                // 3秒后恢复状态提示
                setTimeout(() => {
                    updateStatus('系统就绪 - 可以尝试重新加载数据');
                }, 3000);
            }
        }
        
        // 表格是否已初始化的标志
        let isTableInitialized = false;

        // 增强的表格渲染（增量更新，避免完全重建）
        function renderTable(data) {
            const startTime = performance.now();

            if (!data || data.length === 0) {
                return;
            }

            // 过滤掉包含"数据来源通达信"的行
            const filteredData = data.filter(row => {
                const rowValues = Object.values(row).join('').toLowerCase();
                const shouldFilter = rowValues.includes('数据来源') || rowValues.includes('通达信');
                return !shouldFilter;
            });

            console.log(`📊 [表格渲染] 原始数据: ${data.length}行, 过滤后: ${filteredData.length}行`);

            const headers = Object.keys(filteredData[0]);
            const thead = document.getElementById('tableHead');
            const tbody = document.getElementById('tableBody');

            // 保存列索引到全局变量，供K线图使用
            window.columnIndexes = {
                code: headers.indexOf('代码'),
                name: headers.indexOf('名称')
            };

            // 只在首次渲染或表头变化时更新表头
            const currentHeaders = Array.from(thead.querySelectorAll('th')).map(th => th.textContent);
            if (!isTableInitialized || JSON.stringify(currentHeaders) !== JSON.stringify(headers)) {
                console.log(`🔄 [表格渲染] 更新表头`);
                thead.innerHTML = '<tr>' + headers.map(header =>
                    `<th>${header}</th>`
                ).join('') + '</tr>';

                // 只在表头更新时重新初始化排序
                if (typeof makeTableSortable === 'function') {
                    makeTableSortable(document.querySelector('table.dataframe'));
                    setupSortingFocusHandler();
                    console.log('✅ 表格排序功能已重新初始化');
                }
            }

            // 使用DocumentFragment进行批量DOM操作，提升性能
            const fragment = document.createDocumentFragment();
            filteredData.forEach((row, index) => {
                const tr = document.createElement('tr');
                tr.setAttribute('data-row-index', index);

                headers.forEach(header => {
                    const td = document.createElement('td');
                    td.textContent = row[header] || '';
                    tr.appendChild(td);
                });

                fragment.appendChild(tr);
            });

            // 一次性更新tbody内容
            tbody.innerHTML = '';
            tbody.appendChild(fragment);

            // 只在首次初始化时绑定事件
            if (!isTableInitialized) {
                console.log(`🔄 [表格渲染] 首次初始化，绑定事件`);

                // 绑定键盘导航（只绑定一次）
                bindKeyboardNavigation();

                // 绑定行点击事件委托（只绑定一次）
                bindRowClickEvents();

                isTableInitialized = true;
            }

            // 智能重置高亮索引（考虑筛选状态）
            const previousIndex = app.highlightedIndex;
            clearHighlight();

            // 如果之前有高亮行且新数据中仍然存在，尝试保持焦点
            if (previousIndex >= 0 && previousIndex < filteredData.length) {
                setTimeout(() => {
                    highlightRow(Math.min(previousIndex, filteredData.length - 1));
                }, 50); // 减少延迟
            }

            const endTime = performance.now();
            console.log(`✅ 表格渲染完成: ${filteredData.length}行 × ${headers.length}列 (${(endTime - startTime).toFixed(2)}ms)`);
        }
        
        // 鼠标交互功能（修复排序后的索引映射问题）
        function handleRowClick(event) {
            // 获取实际点击的DOM行
            const clickedRow = event.target.closest('tr');
            if (!clickedRow) return;

            // 获取该行在当前DOM中的实际索引
            const rows = document.querySelectorAll('table.dataframe tbody tr');
            const actualIndex = Array.from(rows).indexOf(clickedRow);

            console.log(`🖱️ [鼠标点击] DOM行索引: ${actualIndex}`);

            if (actualIndex >= 0) {
                // 使用实际的DOM索引进行焦点管理
                FocusManager.setHighlight(actualIndex);

                // 更新K线图
                updateKlineChart(actualIndex);

                console.log(`✅ [鼠标点击] 焦点设置完成: ${actualIndex}`);
            } else {
                console.error(`❌ [鼠标点击] 无法找到行索引`);
            }
        }

        function handleRowHover(index) {
            // 悬停时不改变焦点，只添加视觉反馈
            const rows = document.querySelectorAll('table.dataframe tbody tr');
            if (rows[index] && !rows[index].classList.contains('highlighted')) {
                // 可以添加悬停效果，但不影响焦点
            }
        }

        // 行点击事件是否已绑定的标志
        let isRowClickEventBound = false;

        // 绑定行点击事件委托（修复排序后的鼠标焦点问题）
        function bindRowClickEvents() {
            if (isRowClickEventBound) {
                console.log(`⏭️ [事件绑定] 行点击事件已绑定，跳过`);
                return;
            }

            const tbody = document.getElementById('tableBody');
            if (!tbody) return;

            // 添加事件监听器
            tbody.addEventListener('click', handleTableBodyClick);
            isRowClickEventBound = true;

            console.log(`✅ [事件绑定] 行点击事件委托已绑定`);
        }

        // 处理表格主体点击事件
        function handleTableBodyClick(event) {
            const clickedRow = event.target.closest('tr');
            if (!clickedRow) return;

            // 调用修复后的行点击处理函数
            handleRowClick(event);
        }

        // 设置排序后的焦点处理 - 统一焦点管理
        function setupSortingFocusHandler() {
            // 记录排序前的高亮数据
            let highlightedDataBeforeSort = null;

            // 监听排序开始事件（表头点击）
            const table = document.querySelector('table.dataframe');
            if (table) {
                const headers = table.querySelectorAll('thead th');
                headers.forEach(header => {
                    header.addEventListener('click', function() {
                        // 排序前记录当前高亮的数据
                        if (app.highlightedIndex >= 0 && app.currentData[app.highlightedIndex]) {
                            highlightedDataBeforeSort = {
                                code: app.currentData[app.highlightedIndex]['代码'],
                                name: app.currentData[app.highlightedIndex]['名称'],
                                originalIndex: app.highlightedIndex
                            };
                            console.log(`📋 [焦点管理] 排序前记录高亮数据:`, highlightedDataBeforeSort);
                        } else {
                            highlightedDataBeforeSort = null;
                            console.log(`📋 [焦点管理] 排序前无高亮行`);
                        }
                    });
                });
            }

            // 监听排序完成事件
            document.addEventListener('tableSortComplete', function(event) {
                console.log(`🔄 [焦点管理] 收到排序完成事件:`, event.detail);

                // 延迟处理，确保DOM完全更新
                setTimeout(() => {
                    handleSortComplete(highlightedDataBeforeSort);
                    highlightedDataBeforeSort = null; // 清除记录
                }, 50);
            });
        }

        // 处理排序完成后的焦点恢复（使用统一焦点管理）
        function handleSortComplete(highlightedDataBeforeSort) {
            console.log(`🔄 [焦点管理] 开始处理排序后焦点恢复`);

            // 使用统一的焦点管理清除状态
            FocusManager.clearAll();

            // 如果有之前的高亮数据，尝试找到排序后的位置
            if (highlightedDataBeforeSort) {
                // 重新获取当前表格数据（排序后的顺序）
                const currentRows = document.querySelectorAll('table.dataframe tbody tr');
                let newIndex = -1;

                // 通过DOM查找匹配的行
                for (let i = 0; i < currentRows.length; i++) {
                    const row = currentRows[i];
                    const cells = row.querySelectorAll('td');
                    if (cells.length > 0) {
                        const code = cells[0].textContent.trim(); // 假设代码在第一列
                        const name = cells[1] ? cells[1].textContent.trim() : ''; // 假设名称在第二列

                        if (code === highlightedDataBeforeSort.code && name === highlightedDataBeforeSort.name) {
                            newIndex = i;
                            break;
                        }
                    }
                }

                if (newIndex >= 0) {
                    console.log(`✅ [焦点管理] 找到排序后位置: ${highlightedDataBeforeSort.originalIndex} -> ${newIndex}`);
                    highlightRow(newIndex);
                } else {
                    console.log(`⚠️ [焦点管理] 未找到匹配行，重置焦点`);
                }
            } else {
                console.log(`📋 [焦点管理] 排序前无高亮行，保持清空状态`);
            }

            // 最终验证状态一致性
            setTimeout(() => {
                FocusManager.validateState();
                console.log(`🔄 [焦点管理] 焦点恢复完成，当前索引: ${app.highlightedIndex}`);
            }, 100);
        }

        // 高亮行功能（使用统一的焦点管理）
        function highlightRow(index) {
            if (FocusManager.setHighlight(index)) {
                // 自动更新K线图（键盘导航联动）
                updateKlineChart(index);

                // 验证状态一致性
                setTimeout(() => {
                    FocusManager.validateState();
                }, 50);
            }
        }

        // 统一的焦点管理系统
        const FocusManager = {
            // 清除所有高亮
            clearAll() {
                const rows = document.querySelectorAll('table.dataframe tbody tr');
                rows.forEach(row => {
                    row.classList.remove('highlighted');
                    // 确保移除所有可能的高亮样式
                    row.style.backgroundColor = '';
                    row.style.borderLeft = '';
                });
                app.highlightedIndex = -1;
                window.globalHighlightedIndex = -1;
                console.log(`🧹 [焦点管理] 清除所有高亮状态`);
            },

            // 设置高亮行
            setHighlight(index) {
                const rows = document.querySelectorAll('table.dataframe tbody tr');

                // 验证索引有效性
                if (index < 0 || index >= rows.length) {
                    console.warn(`⚠️ [焦点管理] 无效索引: ${index}, 总行数: ${rows.length}`);
                    return false;
                }

                // 先清除所有高亮
                this.clearAll();

                // 设置新的高亮
                rows[index].classList.add('highlighted');
                app.highlightedIndex = index;
                window.globalHighlightedIndex = index;

                // 滚动到可见区域
                rows[index].scrollIntoView({ behavior: 'smooth', block: 'nearest' });

                console.log(`✅ [焦点管理] 设置高亮行: ${index}`);
                return true;
            },

            // 获取当前高亮索引
            getCurrentIndex() {
                return app.highlightedIndex;
            },

            // 验证状态一致性
            validateState() {
                const rows = document.querySelectorAll('table.dataframe tbody tr');
                const highlightedRows = Array.from(rows).filter(row => row.classList.contains('highlighted'));

                if (highlightedRows.length > 1) {
                    console.error(`❌ [焦点管理] 发现多个高亮行: ${highlightedRows.length}个`);
                    this.clearAll();
                    return false;
                }

                if (highlightedRows.length === 1) {
                    const actualIndex = Array.from(rows).indexOf(highlightedRows[0]);
                    if (actualIndex !== app.highlightedIndex) {
                        console.warn(`⚠️ [焦点管理] 状态不一致: DOM=${actualIndex}, app=${app.highlightedIndex}`);
                        app.highlightedIndex = actualIndex;
                        window.globalHighlightedIndex = actualIndex;
                    }
                }

                return true;
            }
        };

        // 兼容性函数
        function clearHighlight() {
            FocusManager.clearAll();
        }

        // 更新K线图（修复排序后的数据索引映射）
        function updateKlineChart(domIndex) {
            // 通过DOM索引获取实际的行数据
            const rows = document.querySelectorAll('table.dataframe tbody tr');
            if (domIndex < 0 || domIndex >= rows.length) {
                console.warn(`⚠️ K线图更新失败: 无效DOM索引 ${domIndex}`);
                return;
            }

            const row = rows[domIndex];
            const cells = row.querySelectorAll('td');
            if (cells.length === 0) {
                console.warn(`⚠️ K线图更新失败: 行无数据`);
                return;
            }

            // 从DOM中提取股票数据（使用动态列索引）
            const codeIndex = window.columnIndexes ? window.columnIndexes.code : 0;
            const nameIndex = window.columnIndexes ? window.columnIndexes.name : 1;

            const stockData = {
                code: cells[codeIndex] ? cells[codeIndex].textContent.trim() : '',
                name: cells[nameIndex] ? cells[nameIndex].textContent.trim() : '',
                date: app.currentDate
            };

            console.log(`📊 [K线图更新] 使用列索引 - 代码:${codeIndex}, 名称:${nameIndex}`);

            // 详细的调试信息
            console.log(`📊 [K线图] DOM索引: ${domIndex}, 股票数据:`, {
                code: stockData.code,
                name: stockData.name,
                date: stockData.date
            });

            if (!stockData.code) {
                console.error(`❌ 股票代码为空: 索引${index}`, rowData);
                return;
            }

            if (!stockData.name) {
                console.error(`❌ 股票名称为空: 索引${index}`, rowData);
                return;
            }

            if (app.klineManager) {
                // 如果K线图区域已经显示，则更新数据
                const klineSection = document.getElementById('klineSection');
                if (klineSection && klineSection.classList.contains('active')) {
                    app.klineManager.loadKlineData(stockData.code, stockData.name, stockData.date);
                    console.log(`🔄 更新K线图: ${stockData.name} (${stockData.code}) - ${stockData.date}`);
                }
            } else {
                console.warn(`⚠️ K线图管理器未初始化`);
            }
        }

        // 筛选功能实现（基于现有的筛选功能修复报告）
        function toggleFilterSection() {
            const filterPanel = document.getElementById('filterPanel');

            if (filterPanel) {
                if (filterPanel.classList.contains('active')) {
                    filterPanel.classList.remove('active');
                    app.searchState.isActive = false;
                    console.log('🔍 筛选面板已关闭');
                } else {
                    filterPanel.classList.add('active');
                    app.searchState.isActive = true;
                    const filterInput = document.getElementById('filterInput');
                    if (filterInput) {
                        filterInput.focus();
                    }
                    console.log('🔍 筛选面板已显示');
                }
            }
        }

        function handleFilterKeyPress(event) {
            if (event.key === 'Enter') {
                applyFilter();
            }
        }

        function setPresetFilter(condition) {
            const filterInput = document.getElementById('filterInput');
            if (filterInput) {
                filterInput.value = condition;
                applyFilter();
            }
        }

        function toggleFilterHelp() {
            const helpPanel = document.getElementById('filterHelp');
            if (helpPanel) {
                if (helpPanel.style.display === 'none') {
                    helpPanel.style.display = 'block';
                } else {
                    helpPanel.style.display = 'none';
                }
            }
        }

        function applyFilter() {
            const filterInput = document.getElementById('filterInput');
            const filterStatus = document.getElementById('filterStatus');

            if (!filterInput || !app.originalData) {
                return;
            }

            const condition = filterInput.value.trim();

            if (!condition) {
                clearFilter();
                return;
            }

            try {
                const startTime = performance.now();

                // 解析筛选条件
                const parsedConditions = parseFilterCondition(condition);

                // 应用筛选和排序
                let filteredData = applyFilterAndSort(app.originalData, parsedConditions);

                app.currentData = filteredData;
                app.searchState.filteredData = filteredData;
                app.searchState.query = condition;

                // 确保筛选面板保持可见状态
                const filterPanel = document.getElementById('filterPanel');
                if (filterPanel && !filterPanel.classList.contains('active')) {
                    filterPanel.classList.add('active');
                    app.searchState.isActive = true;
                    console.log('🔍 [筛选应用] 显示筛选面板');
                }

                renderTable(app.currentData);

                const endTime = performance.now();
                const duration = (endTime - startTime).toFixed(2);

                if (filterStatus) {
                    filterStatus.className = 'filter-status success active';
                    filterStatus.textContent = `✅ 筛选完成：找到 ${filteredData.length} 条记录 (${duration}ms)`;

                    // 3秒后自动隐藏状态
                    setTimeout(() => {
                        filterStatus.classList.remove('active');
                    }, 3000);
                }

                console.log(`🔍 筛选完成: "${condition}" -> ${filteredData.length}条结果 (${duration}ms)`);

            } catch (error) {
                if (filterStatus) {
                    filterStatus.className = 'filter-status error active';
                    filterStatus.textContent = `❌ 筛选条件错误：${error.message}`;

                    // 5秒后自动隐藏错误状态
                    setTimeout(() => {
                        filterStatus.classList.remove('active');
                    }, 5000);
                }
                console.error('筛选条件解析失败:', error);
            }
        }

        function clearFilter() {
            const filterInput = document.getElementById('filterInput');
            const filterStatus = document.getElementById('filterStatus');

            if (filterInput) {
                filterInput.value = '';
            }

            if (filterStatus) {
                filterStatus.className = 'filter-status';
                filterStatus.textContent = '';
                filterStatus.classList.remove('active');
            }

            // 恢复原始数据
            if (app.originalData) {
                app.currentData = [...app.originalData];
                app.searchState.query = '';
                app.searchState.filteredData = null;
                renderTable(app.currentData);
                console.log('🔍 筛选已清除，恢复原始数据');
            }
        }

        // K线图显示功能（修复DOM索引数据提取）
        function showKlineChart(domIndex) {
            if (!app.klineManager) {
                console.warn('❌ K线图管理器不可用');
                return;
            }

            // 通过DOM索引获取实际的行数据
            const rows = document.querySelectorAll('table.dataframe tbody tr');
            if (domIndex < 0 || domIndex >= rows.length) {
                console.warn(`❌ K线图显示失败: 无效DOM索引 ${domIndex}`);
                return;
            }

            const row = rows[domIndex];
            const cells = row.querySelectorAll('td');
            if (cells.length === 0) {
                console.warn(`❌ K线图显示失败: 行无数据`);
                return;
            }

            // 从DOM中提取股票数据（使用动态列索引）
            const codeIndex = window.columnIndexes ? window.columnIndexes.code : 0;
            const nameIndex = window.columnIndexes ? window.columnIndexes.name : 1;

            const stockData = {
                code: cells[codeIndex] ? cells[codeIndex].textContent.trim() : '',
                name: cells[nameIndex] ? cells[nameIndex].textContent.trim() : '',
                date: app.currentDate
            };

            console.log(`📊 [K线图数据] 使用列索引 - 代码:${codeIndex}, 名称:${nameIndex}`);

            console.log(`📈 [K线图显示] DOM索引: ${domIndex}, 股票数据:`, stockData);

            if (stockData.code && stockData.name) {
                console.log(`📈 显示K线图: ${stockData.name} (${stockData.code}) - ${stockData.date}`);

                // 检查K线图管理器状态
                console.log(`🔍 [K线图调试] K线图管理器状态:`, {
                    hasManager: !!app.klineManager,
                    hasChart: !!(app.klineManager && app.klineManager.chart),
                    currentStock: app.klineManager ? app.klineManager.currentStock : null
                });

                // 检查DOM元素
                const klineSection = document.getElementById('klineSection');
                const klineContainer = document.getElementById('klineChartContainer');
                console.log(`🔍 [K线图调试] DOM元素状态:`, {
                    hasKlineSection: !!klineSection,
                    hasKlineContainer: !!klineContainer,
                    klineSectionActive: klineSection ? klineSection.classList.contains('active') : false
                });

                // 使用正确的方法名 'show'
                app.klineManager.show(stockData.code, stockData.name, stockData.date);

                // 延迟检查K线图是否显示
                setTimeout(() => {
                    const klineSectionAfter = document.getElementById('klineSection');
                    console.log(`🔍 [K线图调试] 显示后状态:`, {
                        klineSectionActive: klineSectionAfter ? klineSectionAfter.classList.contains('active') : false,
                        klineSectionDisplay: klineSectionAfter ? getComputedStyle(klineSectionAfter).display : 'none'
                    });
                }, 500);

            } else {
                console.warn('❌ 股票数据不完整:', stockData);
            }
        }
        
        // 键盘导航是否已绑定的标志
        let isKeyboardNavigationBound = false;

        // 当前焦点列索引（用于快速导航）
        let currentFocusColumn = 0;

        // 高级表格导航功能
        function handleAdvancedTableNavigation(event) {
            const table = document.querySelector('table.dataframe');
            if (!table) return;

            const headers = table.querySelectorAll('thead th');
            const totalColumns = headers.length;

            if (totalColumns === 0) return;

            switch (event.key) {
                case 'ArrowLeft':
                    // Ctrl+左：快速向左移动8列
                    currentFocusColumn = Math.max(0, currentFocusColumn - 8);
                    highlightColumn(currentFocusColumn);
                    console.log(`⬅️ [快速导航] 向左移动8列，当前列: ${currentFocusColumn} (${headers[currentFocusColumn]?.textContent})`);
                    break;

                case 'ArrowRight':
                    // Ctrl+右：快速向右移动8列
                    currentFocusColumn = Math.min(totalColumns - 1, currentFocusColumn + 8);
                    highlightColumn(currentFocusColumn);
                    console.log(`➡️ [快速导航] 向右移动8列，当前列: ${currentFocusColumn} (${headers[currentFocusColumn]?.textContent})`);
                    break;

                case 'ArrowUp':
                    // Ctrl+上：对当前列进行升序排序
                    if (currentFocusColumn < totalColumns) {
                        sortColumnByIndex(currentFocusColumn, 'asc');
                        console.log(`⬆️ [列排序] 升序排序列: ${currentFocusColumn} (${headers[currentFocusColumn]?.textContent})`);
                    }
                    break;

                case 'ArrowDown':
                    // Ctrl+下：对当前列进行降序排序
                    if (currentFocusColumn < totalColumns) {
                        sortColumnByIndex(currentFocusColumn, 'desc');
                        console.log(`⬇️ [列排序] 降序排序列: ${currentFocusColumn} (${headers[currentFocusColumn]?.textContent})`);
                    }
                    break;
            }
        }

        // 高亮指定列
        function highlightColumn(columnIndex) {
            const table = document.querySelector('table.dataframe');
            if (!table) return;

            // 清除之前的列高亮
            table.querySelectorAll('th, td').forEach(cell => {
                cell.classList.remove('column-focused');
            });

            // 高亮表头
            const headers = table.querySelectorAll('thead th');
            if (headers[columnIndex]) {
                headers[columnIndex].classList.add('column-focused');
            }

            // 高亮该列的所有单元格
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells[columnIndex]) {
                    cells[columnIndex].classList.add('column-focused');
                }
            });

            // 滚动到可见区域
            if (headers[columnIndex]) {
                headers[columnIndex].scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'center'
                });
            }
        }

        // 按列索引排序
        function sortColumnByIndex(columnIndex, direction) {
            const table = document.querySelector('table.dataframe');
            if (!table) return;

            const headers = table.querySelectorAll('thead th');
            if (!headers[columnIndex]) return;

            // 模拟点击表头来触发排序
            const header = headers[columnIndex];

            // 设置排序方向属性
            header.setAttribute('data-sort-direction', direction);

            // 触发点击事件
            header.click();

            // 添加极简视觉反馈
            header.style.backgroundColor = '#f8f9fa';
            setTimeout(() => {
                header.style.backgroundColor = '';
            }, 500);
        }

        // 键盘导航功能
        function bindKeyboardNavigation() {
            if (isKeyboardNavigationBound) {
                console.log('⏭️ 键盘导航已绑定，跳过');
                return;
            }

            // 添加事件监听器
            document.addEventListener('keydown', handleKeyboardNavigation);
            isKeyboardNavigationBound = true;
            console.log('✅ 键盘导航已绑定');
        }

        function handleKeyboardNavigation(event) {
            // 调试：记录所有键盘事件
            const keyList = ['Enter', ' ', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'PageUp', 'PageDown', 'Escape'];
            if (keyList.includes(event.key) || (event.ctrlKey && keyList.includes(event.key))) {
                const modifiers = event.ctrlKey ? 'Ctrl+' : '';
                console.log(`⌨️ [键盘事件] 按键: ${modifiers}${event.key}, 焦点元素: ${document.activeElement.tagName}, ID: ${document.activeElement.id || '无'}`);
            }

            // 检查是否在筛选输入框中
            const filterInput = document.getElementById('filterInput');
            const isInFilterInput = document.activeElement === filterInput;

            // 处理筛选相关快捷键
            if (event.ctrlKey && event.key === 'f') {
                event.preventDefault();
                toggleFilterSection();
                return;
            }

            // 处理表格导航增强快捷键
            if (event.ctrlKey && ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(event.key)) {
                event.preventDefault();
                handleAdvancedTableNavigation(event);
                return;
            }

            if (event.key === 'Escape') {
                event.preventDefault();

                // 优先关闭筛选面板
                if (app.searchState.isActive) {
                    toggleFilterSection();
                    return;
                }

                // 然后关闭K线图
                if (app.klineManager) {
                    app.klineManager.hide();
                }
                return;
            }

            // 如果在筛选输入框中，只处理筛选相关的键盘事件
            if (isInFilterInput) {
                // 允许筛选输入框正常处理所有输入，包括空格
                console.log(`⌨️ [筛选输入] 允许输入: ${event.key}`);
                return; // 让筛选输入框正常处理输入
            }

            const rows = document.querySelectorAll('table.dataframe tbody tr');
            if (rows.length === 0) return;

            switch (event.key) {
                case 'ArrowUp':
                    event.preventDefault();
                    if (app.highlightedIndex > 0) {
                        highlightRow(app.highlightedIndex - 1);
                    }
                    break;

                case 'ArrowDown':
                    event.preventDefault();
                    if (app.highlightedIndex < rows.length - 1) {
                        highlightRow(app.highlightedIndex + 1);
                    } else if (app.highlightedIndex === -1 && rows.length > 0) {
                        highlightRow(0);
                    }
                    break;

                case 'Enter':
                case ' ':
                    event.preventDefault();
                    console.log(`⌨️ [键盘导航] Enter键被按下，当前高亮索引: ${app.highlightedIndex}`);
                    if (app.highlightedIndex >= 0) {
                        console.log(`📈 [键盘导航] 准备显示K线图，索引: ${app.highlightedIndex}`);
                        showKlineChart(app.highlightedIndex);
                    } else {
                        console.warn(`⚠️ [键盘导航] 无有效高亮行，无法显示K线图`);
                    }
                    break;

                case 'PageUp':
                    event.preventDefault();
                    switchToPreviousDate();
                    break;

                case 'PageDown':
                    event.preventDefault();
                    switchToNextDate();
                    break;
            }
        }

        // 日期切换功能（增强筛选状态保持）
        function switchToPreviousDate() {
            const selector = document.getElementById('dateSelector');
            const currentIndex = selector.selectedIndex;
            if (currentIndex > 0) {
                console.log(`📅 [日期切换] 切换到上一日期，当前筛选状态: "${app.searchState.query || '无'}"`);
                console.log(`📅 [日期切换] 筛选面板状态: ${app.searchState.isActive ? '显示' : '隐藏'}`);
                selector.selectedIndex = currentIndex - 1;
                loadDateData(selector.value);
            } else {
                console.log(`📅 [日期切换] 已是最早日期，无法继续向前`);
            }
        }

        function switchToNextDate() {
            const selector = document.getElementById('dateSelector');
            const currentIndex = selector.selectedIndex;
            if (currentIndex < selector.options.length - 1) {
                console.log(`📅 [日期切换] 切换到下一日期，当前筛选状态: "${app.searchState.query || '无'}"`);
                console.log(`📅 [日期切换] 筛选面板状态: ${app.searchState.isActive ? '显示' : '隐藏'}`);
                selector.selectedIndex = currentIndex + 1;
                loadDateData(selector.value);
            } else {
                console.log(`📅 [日期切换] 已是最新日期，无法继续向后`);
            }
        }

        // 自动刷新数据检查
        async function checkForDataUpdates() {
            try {
                // 检查监控API是否可用
                const response = await fetch('/api/data/refresh');
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data) {
                        const newIndexData = result.data;

                        // 比较日期列表是否有变化
                        const oldDates = app.indexData ? app.indexData.available_dates : [];
                        const newDates = newIndexData.available_dates || [];

                        if (JSON.stringify(oldDates) !== JSON.stringify(newDates)) {
                            console.log('🔄 检测到数据更新，重新加载...');

                            // 更新索引数据
                            app.indexData = newIndexData;

                            // 重新填充日期选择器
                            populateDateSelector();

                            // 如果当前日期仍然存在，重新加载数据
                            if (app.currentDate && newDates.includes(app.currentDate)) {
                                await loadDateData(app.currentDate);
                            } else if (newDates.length > 0) {
                                // 加载最新日期
                                const latestDate = newDates[newDates.length - 1];
                                document.getElementById('dateSelector').value = latestDate;
                                await loadDateData(latestDate);
                            }

                            // 显示更新提示
                            updateStatus('数据已自动更新');
                            setTimeout(() => updateStatus('系统就绪'), 3000);
                        }
                    }
                }
            } catch (error) {
                // 静默处理错误，避免干扰用户
                console.debug('自动刷新检查失败:', error);
            }

            app.autoRefresh.lastCheck = Date.now();
        }

        // 启动自动刷新（第二个定义，已删除重复）

        // 停止自动刷新
        function stopAutoRefresh() {
            if (app.autoRefresh.timer) {
                clearInterval(app.autoRefresh.timer);
                app.autoRefresh.timer = null;
                console.log('🛑 自动刷新已停止');
            }
        }

        // 绑定事件
        function bindEvents() {
            document.getElementById('dateSelector').addEventListener('change', function(e) {
                if (e.target.value) {
                    loadDateData(e.target.value);
                }
            });

            document.getElementById('refreshBtn').addEventListener('click', function() {
                location.reload();
            });

            // 绑定筛选面板事件
            const filterInput = document.getElementById('filterInput');
            if (filterInput) {
                filterInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        toggleFilterSection();
                    }
                });
            }

            // 绑定全局键盘导航（只在初始化时绑定一次）
            bindKeyboardNavigation();
        }
        
        // K线图控制（嵌入式显示）
        function closeKlineModal() {
            if (app.klineManager) {
                app.klineManager.hide();
            }
        }

        // 全局函数以兼容现有JS模块
        window.rebindAllFeatures = function() {
            console.log('🔄 重新绑定所有功能');
            // 重置绑定标志，允许重新绑定
            isKeyboardNavigationBound = false;
            isRowClickEventBound = false;
            isTableInitialized = false;
            bindKeyboardNavigation();
        };

        // 筛选条件解析函数（基于筛选功能修复报告的实现）
        function parseFilterCondition(condition) {
            if (!condition || typeof condition !== 'string') {
                return null;
            }

            const trimmed = condition.trim();
            if (!trimmed) {
                return null;
            }

            try {
                return parseExpression(trimmed, ['AND', 'OR']);
            } catch (error) {
                throw new Error(`条件解析失败: ${error.message}`);
            }
        }

        function parseExpression(expression, operators) {
            // 处理括号
            while (expression.includes('(')) {
                const start = expression.lastIndexOf('(');
                const end = expression.indexOf(')', start);

                if (end === -1) {
                    throw new Error('括号不匹配');
                }

                const innerExpression = expression.substring(start + 1, end);
                const innerResult = parseSimpleExpression(innerExpression, operators);

                // 用占位符替换括号内容
                const placeholder = `__PLACEHOLDER_${Math.random().toString(36).substr(2, 9)}__`;
                expression = expression.substring(0, start) + placeholder + expression.substring(end + 1);

                // 保存解析结果
                if (!window._parseCache) window._parseCache = {};
                window._parseCache[placeholder] = innerResult;
            }

            return parseSimpleExpression(expression, operators);
        }

        function parseSimpleExpression(expression, operators) {
            for (const operator of operators) {
                const parts = expression.split(new RegExp(`\\s+${operator}\\s+`, 'i'));
                if (parts.length > 1) {
                    const conditions = parts.map(part => {
                        const trimmed = part.trim();
                        if (trimmed.startsWith('__PLACEHOLDER_')) {
                            return window._parseCache[trimmed];
                        }
                        return parseCondition(trimmed);
                    });

                    return {
                        type: operator.toUpperCase(),
                        conditions: conditions
                    };
                }
            }

            // 单个条件
            return parseCondition(expression.trim());
        }

        function parseCondition(condition) {
            // 检查是否为排序筛选语法：字段名 倒序前N 或 字段名 正序前N
            const sortPattern = /^(.+?)\s+(倒序前|正序前)(\d+)$/;
            const sortMatch = condition.match(sortPattern);

            if (sortMatch) {
                const field = sortMatch[1].trim();
                const sortType = sortMatch[2];
                const count = parseInt(sortMatch[3]);

                return {
                    type: 'sort',
                    field: field,
                    direction: sortType === '倒序前' ? 'desc' : 'asc',
                    count: count
                };
            }

            // 原有的比较运算符处理
            const operators = ['>=', '<=', '!=', '>', '<', '='];

            for (const op of operators) {
                const index = condition.indexOf(op);
                if (index > 0) {
                    const field = condition.substring(0, index).trim();
                    const value = condition.substring(index + op.length).trim();

                    return {
                        field: field,
                        operator: op,
                        value: parseValue(value)
                    };
                }
            }

            throw new Error(`无效的条件格式: ${condition}`);
        }

        function parseValue(value) {
            // 移除引号
            if ((value.startsWith('"') && value.endsWith('"')) ||
                (value.startsWith("'") && value.endsWith("'"))) {
                return value.slice(1, -1);
            }

            // 尝试解析为数字
            const num = parseFloat(value);
            if (!isNaN(num)) {
                return num;
            }

            return value;
        }

        // 应用筛选和排序的综合函数
        function applyFilterAndSort(data, filterConditions) {
            if (!filterConditions) {
                console.log('🔍 [筛选排序] 无筛选条件，返回原始数据');
                return [...data];
            }

            // 检查是否包含排序条件
            const sortCondition = extractSortCondition(filterConditions);

            if (sortCondition) {
                console.log(`🔍 [筛选排序] 发现排序条件: ${sortCondition.field} ${sortCondition.direction === 'desc' ? '倒序' : '正序'}前${sortCondition.count}`);

                // 如果有排序条件，先应用非排序筛选，再排序并取前N条
                const nonSortConditions = removeSortCondition(filterConditions);

                // 应用非排序筛选
                let filteredData = data;
                if (nonSortConditions) {
                    filteredData = data.filter(row => evaluateFilterConditions(row, nonSortConditions));
                    console.log(`🔍 [筛选排序] 非排序筛选后: ${data.length} -> ${filteredData.length} 条记录`);
                } else {
                    console.log(`🔍 [筛选排序] 无非排序筛选条件，使用全部数据: ${data.length} 条记录`);
                }

                // 应用排序
                const sortedData = [...filteredData].sort((a, b) => {
                    const aValue = getFieldValue(a, sortCondition.field);
                    const bValue = getFieldValue(b, sortCondition.field);

                    const aNum = parseFloat(aValue) || 0;
                    const bNum = parseFloat(bValue) || 0;

                    if (sortCondition.direction === 'desc') {
                        return bNum - aNum;
                    } else {
                        return aNum - bNum;
                    }
                });

                // 取前N条
                const result = sortedData.slice(0, sortCondition.count);
                console.log(`🔍 [筛选排序] 排序并取前${sortCondition.count}条: ${filteredData.length} -> ${result.length} 条记录`);
                return result;
            } else {
                // 没有排序条件，只应用普通筛选
                const result = data.filter(row => evaluateFilterConditions(row, filterConditions));
                console.log(`🔍 [筛选排序] 普通筛选: ${data.length} -> ${result.length} 条记录`);
                return result;
            }
        }

        // 提取排序条件
        function extractSortCondition(filterConditions) {
            if (!filterConditions) return null;

            // 单个条件
            if (filterConditions.type === 'sort') {
                return filterConditions;
            }

            // 复合条件中查找排序条件
            if (filterConditions.conditions) {
                for (const condition of filterConditions.conditions) {
                    const sortCondition = extractSortCondition(condition);
                    if (sortCondition) return sortCondition;
                }
            }

            return null;
        }

        // 移除排序条件，保留其他筛选条件
        function removeSortCondition(filterConditions) {
            if (!filterConditions) return null;

            // 单个条件
            if (filterConditions.type === 'sort') {
                return null;
            }

            if (!filterConditions.conditions) {
                return filterConditions;
            }

            // 复合条件中移除排序条件
            const newConditions = [];
            for (const condition of filterConditions.conditions) {
                const filtered = removeSortCondition(condition);
                if (filtered) {
                    newConditions.push(filtered);
                }
            }

            if (newConditions.length === 0) {
                return null;
            } else if (newConditions.length === 1) {
                return newConditions[0];
            } else {
                return {
                    type: filterConditions.type,
                    conditions: newConditions
                };
            }
        }

        // 筛选条件评估函数
        function evaluateFilterConditions(record, filterConditions) {
            if (!filterConditions) {
                return true;
            }

            // 排序条件不参与筛选评估
            if (filterConditions.type === 'sort') {
                return true;
            }

            // 如果是单个条件（没有type属性）
            if (!filterConditions.type) {
                return evaluateCondition(record, filterConditions);
            }

            // 处理AND条件
            if (filterConditions.type === 'AND') {
                for (const condition of filterConditions.conditions) {
                    if (!evaluateFilterConditions(record, condition)) {
                        return false;
                    }
                }
                return true;
            }

            // 处理OR条件
            if (filterConditions.type === 'OR') {
                for (const condition of filterConditions.conditions) {
                    if (evaluateFilterConditions(record, condition)) {
                        return true;
                    }
                }
                return false;
            }

            return true;
        }

        function evaluateCondition(record, condition) {
            const fieldValue = getFieldValue(record, condition.field);
            const conditionValue = condition.value;

            switch (condition.operator) {
                case '>':
                    return parseFloat(fieldValue) > parseFloat(conditionValue);
                case '<':
                    return parseFloat(fieldValue) < parseFloat(conditionValue);
                case '>=':
                    return parseFloat(fieldValue) >= parseFloat(conditionValue);
                case '<=':
                    return parseFloat(fieldValue) <= parseFloat(conditionValue);
                case '=':
                    return fieldValue == conditionValue;
                case '!=':
                    return fieldValue != conditionValue;
                default:
                    return false;
            }
        }

        function getFieldValue(record, fieldName) {
            // 直接字段匹配
            if (record.hasOwnProperty(fieldName)) {
                return record[fieldName];
            }

            // 模糊匹配（忽略大小写和空格）
            const normalizedFieldName = fieldName.toLowerCase().replace(/\s+/g, '');
            for (const key in record) {
                if (key.toLowerCase().replace(/\s+/g, '') === normalizedFieldName) {
                    return record[key];
                }
            }

            return null;
        }

        // 更新状态
        function updateStatus(message) {
            document.getElementById('statusText').textContent = message;
            console.log(message);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const startTime = performance.now();
            console.log('页面加载完成，开始初始化...');

            // 立即初始化基础功能，不等待ECharts
            async function initBasicFeatures() {
                console.log('🚀 开始初始化基础功能...');

                // 先初始化不依赖ECharts的功能
                try {
                    updateStatus('正在加载数据索引...');

                    // 加载数据索引
                    const indexResponse = await fetch('data/index.json');
                    if (!indexResponse.ok) {
                        throw new Error('数据索引加载失败');
                    }
                    app.indexData = await indexResponse.json();

                    // 预加载所有数据
                    await preloadAllData();

                    // 填充日期选择器
                    populateDateSelector();

                    // 初始化基础模块（不包括K线图）
                    initializeBasicModules();

                    // 加载默认日期
                    const defaultDate = getDefaultDate();
                    if (defaultDate) {
                        document.getElementById('dateSelector').value = defaultDate;
                        await loadDateData(defaultDate);
                    }

                    // 绑定事件
                    bindEvents();

                    // 启动自动刷新
                    startAutoRefresh();

                    updateStatus('系统就绪');

                    const basicTime = performance.now();
                    console.log(`✅ 基础功能初始化完成 (${(basicTime - startTime).toFixed(2)}ms)`);

                // 启动数据预加载
                startDataPreloading();
                } catch (error) {
                    console.error('❌ 基础功能初始化失败:', error);
                    updateStatus('基础功能初始化失败: ' + error.message);
                }
            }

            // 异步初始化ECharts相关功能
            function initEChartsFeatures() {
                let retryCount = 0;
                const maxRetries = 5; // 减少重试次数

                function tryInitECharts() {
                    retryCount++;

                    if (typeof echarts === 'undefined') {
                        console.warn(`⏳ ECharts未加载，重试 ${retryCount}/${maxRetries}`);
                        if (retryCount < maxRetries) {
                            setTimeout(tryInitECharts, 300); // 减少重试间隔
                            return;
                        } else {
                            console.error('❌ ECharts加载超时，K线图功能将不可用');
                            // 创建空的K线图管理器
                            app.klineManager = {
                                chart: null,
                                show: function() { console.warn('K线图功能不可用'); },
                                hide: function() {},
                                init: function() {}
                            };
                            return;
                        }
                    }

                    console.log('✅ ECharts已加载，版本:', echarts.version || 'unknown');
                    try {
                        initializeEmbeddedKlineChart();
                        console.log('✅ K线图功能初始化完成');
                    } catch (error) {
                        console.error('❌ K线图初始化失败:', error);
                    }
                }

                tryInitECharts();
            }

            // 立即初始化基础功能
            initBasicFeatures();

            // 异步初始化ECharts功能
            setTimeout(initEChartsFeatures, 50);

            // 测试滚动条显示
            setTimeout(function() {
                const tableContent = document.querySelector('.table-content');
                const horizontalScrollContainer = document.getElementById('horizontalScrollContainer');

                if (tableContent) {
                    console.log('📏 表格容器信息:', {
                        scrollHeight: tableContent.scrollHeight,
                        clientHeight: tableContent.clientHeight,
                        offsetHeight: tableContent.offsetHeight,
                        hasVerticalScrollbar: tableContent.scrollHeight > tableContent.clientHeight
                    });

                    if (tableContent.scrollHeight <= tableContent.clientHeight) {
                        console.warn('⚠️ 表格内容高度不足，可能无法显示垂直滚动条');
                    } else {
                        console.log('✅ 表格内容足够高，垂直滚动条应该可见');
                    }
                }

                if (horizontalScrollContainer) {
                    const isVisible = horizontalScrollContainer.style.display !== 'none';
                    console.log(`📏 横向滚动条状态: ${isVisible ? '可见' : '隐藏'}`);
                }
            }, 1000);
        });
    </script>
</body>
</html>
