# 通达信复盘Excel分析系统 - 筛选功能使用说明

**创建日期：** 2025-7-22  
**版本：** v1.0  
**文件位置：** `excel分析/reports/分页数据分析报告.html`

## 功能概述

实时选股筛选功能允许用户基于107个技术指标对股票数据进行动态筛选，支持复杂的条件组合和逻辑运算。

## 主要特性

### 1. 筛选条件语法
- **基本格式：** `字段名 操作符 数值`
- **支持的操作符：**
  - `>` 大于
  - `<` 小于  
  - `>=` 大于等于
  - `<=` 小于等于
  - `=` 等于
  - `!=` 不等于

### 2. 逻辑组合
- **AND 连接：** 所有条件都必须满足
- **OR 连接：** 任一条件满足即可
- **混合使用：** 支持复杂的逻辑组合

### 3. 字段名称
筛选字段名必须与数据表头完全匹配，包括：
- 基础数据：代码、名称、涨幅%、收盘、总金额等
- 技术指标：量比、次日量比、振幅、HUPD33、HUPD55等
- 共107个字段可用于筛选

## 使用方法

### 1. 打开筛选面板
- 点击页面右上角的 "🔍 筛选" 按钮
- 或使用快捷键 `Ctrl+F`

### 2. 输入筛选条件
在筛选输入框中输入条件，例如：
```
涨幅% > 9.5
量比 > 2
涨幅% >= 9.5 AND 量比 > 1.5
次日量比 > 1.5 OR 振幅 > 10
```

### 3. 执行筛选
- 点击 "筛选" 按钮
- 或在输入框中按 `Enter` 键

### 4. 使用快捷筛选
点击预设的快捷筛选按钮：
- **涨停板：** `涨幅% >= 9.5`
- **放量：** `量比 > 2`
- **次日放量：** `次日量比 > 1.5`
- **强势股：** `涨幅% > 5 AND 量比 > 1.5`
- **活跃股：** `振幅 > 10 AND 涨幅% > 3`
- **创新高：** `HUPD33 > 0`

## 筛选示例

### 基础筛选
```
涨幅% > 9.5          # 筛选涨幅超过9.5%的股票
量比 > 2             # 筛选量比大于2的股票
振幅 < 15            # 筛选振幅小于15的股票
```

### 复合条件筛选
```
涨幅% > 5 AND 量比 > 1.5                    # 强势放量股
次日量比 > 2 AND 振幅 < 15                  # 次日放量但振幅适中
涨幅% >= 9.5 OR (量比 > 3 AND 振幅 > 8)     # 涨停板或大幅放量活跃股
```

### 技术指标筛选
```
HUPD33 > 0                                  # 创33日新高
HUPD55 > 0 AND 量比 > 1.5                   # 创55日新高且放量
强距 > 0 AND 强三线 > 0                     # 技术面强势
```

## 功能特点

### 1. 实时筛选
- 基于当前显示日期的数据进行筛选
- 筛选结果实时更新显示
- 支持在不同日期间切换并保持筛选条件

### 2. 智能提示
- 输入框提供字段名称提示
- 错误条件会显示具体错误信息
- 筛选结果数量实时显示

### 3. 数据保护
- 原始数据自动备份
- 清除筛选后完全恢复原始状态
- 不影响原始数据文件

### 4. 界面集成
- 与现有分页功能无缝集成
- 保持原有排序和显示功能
- 响应式设计，适配不同屏幕

## 快捷键

- `Ctrl+F` - 打开/关闭筛选面板
- `Enter` - 在输入框中执行筛选
- `Escape` - 关闭筛选面板或清除筛选

## 注意事项

### 1. 字段名称匹配
- 字段名必须与表头完全一致，包括特殊字符（如%）
- 区分大小写
- 不支持模糊匹配

### 2. 数值类型
- 系统自动识别数值和文本类型
- 数值比较支持小数
- 文本比较使用字符串匹配

### 3. 性能考虑
- 大数据量时筛选可能需要几秒时间
- 复杂条件会增加处理时间
- 建议使用具体的筛选条件以提高效率

### 4. 兼容性
- 支持现代浏览器（Chrome、Firefox、Edge等）
- 需要启用JavaScript
- 建议使用最新版本浏览器以获得最佳体验

## 故障排除

### 常见问题
1. **筛选无结果**
   - 检查字段名是否正确
   - 确认数值范围是否合理
   - 验证逻辑条件是否正确

2. **语法错误**
   - 确保操作符前后有空格
   - 检查AND/OR大小写
   - 验证括号匹配

3. **功能无响应**
   - 刷新页面重试
   - 检查浏览器控制台错误信息
   - 确认JavaScript已启用

### 调试方法
- 打开浏览器开发者工具（F12）
- 查看控制台日志信息
- 使用测试页面验证筛选逻辑

## 技术实现

### 核心组件
- **条件解析器：** 解析用户输入的筛选条件
- **数据筛选引擎：** 执行筛选逻辑
- **结果渲染器：** 更新页面显示
- **状态管理器：** 管理筛选状态和数据备份

### 数据流程
1. 用户输入筛选条件
2. 解析条件为内部数据结构
3. 遍历当前数据执行筛选
4. 更新页面显示筛选结果
5. 保持原始数据备份以便恢复

## 更新日志

### v1.0 (2025-7-22)
- ✅ 实现基础筛选功能
- ✅ 支持复杂逻辑条件
- ✅ 集成现有分页系统
- ✅ 添加快捷筛选按钮
- ✅ 实现键盘快捷键支持
- ✅ 完成错误处理和用户提示

---

**开发者：** Augment Agent  
**技术支持：** 如有问题请查看浏览器控制台日志或联系开发团队
