/**
 * 内联性能提升脚本
 * 直接在页面中实现虚拟滚动和性能优化
 * 
 * @version 1.0.0
 * @date 2025-07-19
 */

(function() {
    'use strict';
    
    console.log('🚀 内联性能提升脚本启动...');
    
    /**
     * 简化的虚拟滚动实现
     */
    class SimpleVirtualScroll {
        constructor(table, options = {}) {
            this.table = table;
            this.tbody = table.querySelector('tbody');
            this.options = {
                rowHeight: 40,
                visibleRows: 15,
                bufferRows: 5,
                ...options
            };
            
            this.allRows = Array.from(this.tbody.querySelectorAll('tr'));
            this.totalRows = this.allRows.length;
            this.containerHeight = this.options.visibleRows * this.options.rowHeight;
            
            console.log(`📊 虚拟滚动初始化: ${this.totalRows} 行数据`);
            
            this.init();
        }
        
        init() {
            // 创建虚拟滚动容器
            this.createVirtualContainer();
            
            // 初始渲染
            this.renderVisibleRows(0);
            
            // 绑定滚动事件
            this.bindScrollEvents();
            
            console.log('✅ 虚拟滚动初始化完成');
        }
        
        createVirtualContainer() {
            // 设置表格容器样式
            const tableWrapper = this.table.closest('.table-wrapper');
            if (tableWrapper) {
                tableWrapper.style.height = this.containerHeight + 'px';
                tableWrapper.style.overflow = 'auto';
                tableWrapper.style.position = 'relative';
            }
            
            // 创建虚拟高度占位符
            this.spacer = document.createElement('div');
            this.spacer.style.height = (this.totalRows * this.options.rowHeight) + 'px';
            this.spacer.style.position = 'absolute';
            this.spacer.style.top = '0';
            this.spacer.style.left = '0';
            this.spacer.style.width = '1px';
            this.spacer.style.pointerEvents = 'none';
            
            if (tableWrapper) {
                tableWrapper.appendChild(this.spacer);
            }
        }
        
        bindScrollEvents() {
            const tableWrapper = this.table.closest('.table-wrapper');
            if (tableWrapper) {
                let ticking = false;
                
                tableWrapper.addEventListener('scroll', () => {
                    if (!ticking) {
                        requestAnimationFrame(() => {
                            const scrollTop = tableWrapper.scrollTop;
                            const startIndex = Math.floor(scrollTop / this.options.rowHeight);
                            this.renderVisibleRows(startIndex);
                            ticking = false;
                        });
                        ticking = true;
                    }
                });
            }
        }
        
        renderVisibleRows(startIndex) {
            const endIndex = Math.min(
                startIndex + this.options.visibleRows + this.options.bufferRows,
                this.totalRows
            );
            
            // 清空当前显示的行
            this.tbody.innerHTML = '';
            
            // 渲染可见范围内的行
            for (let i = startIndex; i < endIndex; i++) {
                if (this.allRows[i]) {
                    const clonedRow = this.allRows[i].cloneNode(true);
                    this.tbody.appendChild(clonedRow);
                }
            }
            
            // 调整表格位置
            const offsetY = startIndex * this.options.rowHeight;
            this.table.style.transform = `translateY(${offsetY}px)`;
        }
    }
    
    /**
     * 简化的性能监控
     */
    class SimplePerformanceMonitor {
        constructor() {
            this.startTime = performance.now();
            this.metrics = {
                loadTime: 0,
                renderCount: 0,
                memoryUsage: 0
            };
        }
        
        updateMetrics() {
            this.metrics.loadTime = performance.now() - this.startTime;
            this.metrics.renderCount++;
            
            // 估算内存使用
            if (performance.memory) {
                this.metrics.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            }
            
            return this.metrics;
        }
        
        getReport() {
            return {
                totalTime: Math.round(this.metrics.loadTime),
                renderCount: this.metrics.renderCount,
                memoryUsage: {
                    total: this.metrics.memoryUsage + 'MB'
                },
                dataSize: document.querySelectorAll('tbody tr').length
            };
        }
    }
    
    /**
     * 批量渲染优化
     */
    function optimizeTableRendering() {
        console.log('🔧 开始表格渲染优化...');
        
        const table = document.querySelector('table.dataframe') || document.querySelector('table');
        if (!table) {
            console.error('❌ 未找到表格');
            return false;
        }
        
        const tbody = table.querySelector('tbody');
        if (!tbody) {
            console.error('❌ 未找到tbody');
            return false;
        }
        
        const rows = tbody.querySelectorAll('tr');
        console.log(`📊 找到 ${rows.length} 行数据`);
        
        // 如果行数较多，启用虚拟滚动
        if (rows.length > 50) {
            console.log('🔄 启用虚拟滚动...');
            try {
                window.virtualScroll = new SimpleVirtualScroll(table);
                console.log('✅ 虚拟滚动启用成功');
                return true;
            } catch (error) {
                console.error('❌ 虚拟滚动启用失败:', error);
            }
        }
        
        // 如果行数不多，使用批量渲染优化
        console.log('🔄 使用批量渲染优化...');
        optimizeBatchRendering(tbody, rows);
        return true;
    }
    
    /**
     * 批量渲染优化
     */
    function optimizeBatchRendering(tbody, rows) {
        const batchSize = 20;
        const totalBatches = Math.ceil(rows.length / batchSize);
        let currentBatch = 0;
        
        // 显示加载指示器
        showLoadingIndicator();
        
        function renderBatch() {
            if (currentBatch >= totalBatches) {
                hideLoadingIndicator();
                console.log('✅ 批量渲染完成');
                return;
            }
            
            const startIndex = currentBatch * batchSize;
            const endIndex = Math.min(startIndex + batchSize, rows.length);
            
            // 渲染当前批次
            for (let i = startIndex; i < endIndex; i++) {
                if (rows[i]) {
                    rows[i].style.display = '';
                }
            }
            
            currentBatch++;
            
            // 使用requestAnimationFrame确保不阻塞UI
            requestAnimationFrame(renderBatch);
        }
        
        // 先隐藏所有行
        rows.forEach(row => {
            row.style.display = 'none';
        });
        
        // 开始批量渲染
        renderBatch();
    }
    
    /**
     * 显示加载指示器
     */
    function showLoadingIndicator() {
        let indicator = document.getElementById('loadingIndicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'loadingIndicator';
            indicator.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px;
                border-radius: 8px;
                z-index: 10000;
                font-family: Arial, sans-serif;
            `;
            indicator.innerHTML = '⏳ 正在优化表格渲染...';
            document.body.appendChild(indicator);
        }
        indicator.style.display = 'block';
    }
    
    /**
     * 隐藏加载指示器
     */
    function hideLoadingIndicator() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
    
    /**
     * 主要优化函数
     */
    function runInlineOptimization() {
        console.log('🎯 开始内联性能优化...');
        
        // 创建性能监控器
        window.simplePerformanceMonitor = new SimplePerformanceMonitor();
        
        // 优化表格渲染
        const optimized = optimizeTableRendering();
        
        // 更新性能指标
        const metrics = window.simplePerformanceMonitor.updateMetrics();
        console.log('📈 性能指标:', metrics);
        
        // 显示优化结果
        showOptimizationResult(optimized, metrics);
        
        // 暴露性能报告函数
        window.performanceOptimizer = {
            getPerformanceReport: () => window.simplePerformanceMonitor.getReport()
        };
        
        console.log('✅ 内联性能优化完成');
    }
    
    /**
     * 显示优化结果
     */
    function showOptimizationResult(success, metrics) {
        const resultDiv = document.createElement('div');
        resultDiv.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: ${success ? '#4CAF50' : '#f44336'};
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        `;
        
        resultDiv.innerHTML = `
            <h4 style="margin: 0 0 10px 0;">⚡ 性能优化结果</h4>
            <div>状态: ${success ? '✅ 成功' : '❌ 失败'}</div>
            <div>加载时间: ${Math.round(metrics.loadTime)}ms</div>
            <div>内存使用: ${metrics.memoryUsage}MB</div>
            <div>渲染次数: ${metrics.renderCount}</div>
        `;
        
        document.body.appendChild(resultDiv);
        
        // 5秒后自动隐藏
        setTimeout(() => {
            resultDiv.remove();
        }, 5000);
    }
    
    // 页面加载完成后自动运行
    function autoRun() {
        console.log('🔄 自动运行内联优化...');
        
        // 等待表格结构修复完成
        setTimeout(() => {
            runInlineOptimization();
        }, 2000);
    }
    
    // 暴露全局函数
    window.inlinePerformanceBoost = {
        runInlineOptimization,
        optimizeTableRendering,
        SimpleVirtualScroll,
        SimplePerformanceMonitor
    };
    
    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', autoRun);
    } else {
        autoRun();
    }
    
    console.log('✅ 内联性能提升脚本加载完成');
    
})();
