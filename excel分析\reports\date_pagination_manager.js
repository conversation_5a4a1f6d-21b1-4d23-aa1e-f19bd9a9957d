/**
 * date_pagination_manager.js (High-Performance Version)
 * Manages date-based navigation and renders table data on-demand from embedded data.
 */

class DatePaginationManager {
    constructor() {
        this.data = window.EMBEDDED_DATA || {};
        this.availableDates = [];
        this.currentDate = null;
        this.tableBody = null;
        this.tableHeader = null;
        this.dateNavContainer = null;

        // 排序状态管理
        this.sortState = { column: -1, direction: 'asc' };

        this.init();
    }

    init() {
        console.log("🚀 Initializing High-Performance Date Pagination Manager...");

        this.tableBody = document.querySelector('table.dataframe tbody');
        this.tableHeader = document.querySelector('table.dataframe thead');

        if (!this.tableBody) {
            console.error("❌ Critical Error: <table> or <tbody> not found!");
            return;
        }

        this.prepareDates();
        this.createDateNavigation();

        if (this.availableDates.length > 0) {
            // 使用prepareDates中预设的最新日期
            this.switchToDate(this.currentDate);
        } else {
            console.warn("⚠️ No date-grouped data found to display.");
        }

        console.log("✅ Pagination Manager Initialized.");
    }

    prepareDates() {
        this.availableDates = Object.keys(this.data).sort((a, b) => a.localeCompare(b)); // Sort dates ascending (时间顺序)
        console.log(`📅 Found ${this.availableDates.length} available dates.`);
        console.log(`🔍 [日期准备] 日期排序 (升序): ${this.availableDates.join(', ')}`);

        // 默认显示最新日期（数组最后一个）
        this.currentDate = this.availableDates[this.availableDates.length - 1];
        console.log(`🔍 [日期准备] 默认显示最新日期: ${this.currentDate}`);
    }

    createDateNavigation() {
        this.dateNavContainer = document.createElement('div');
        this.dateNavContainer.className = 'date-nav-compact';

        if (this.availableDates.length <= 1) {
            this.dateNavContainer.innerHTML = `
                <div class="date-info-compact">
                    <span>📅</span>
                    <span class="current-date-value">-</span>
                    <span class="current-date-count">(0条)</span>
                </div>
            `;
        } else {
            this.dateNavContainer.innerHTML = `
                <div class="date-info-compact">
                    <span>📅</span>
                    <span class="current-date-value">-</span>
                    <span class="current-date-count">(0条)</span>
                </div>
                <div class="date-controls-compact">
                    <button class="date-btn-compact prev-date" title="上一日期 (PageUp)">◀</button>
                    <select class="date-selector-compact"></select>
                    <button class="date-btn-compact next-date" title="下一日期 (PageDown)">▶</button>
                </div>
            `;
        }

        // Insert navigation into the header navigation area (优先)
        const headerNavigation = document.getElementById('headerNavigation');
        if (headerNavigation) {
            headerNavigation.appendChild(this.dateNavContainer);
            console.log('📅 日期导航已插入到顶部导航区域');
        } else {
            // Fallback: insert into table section if header navigation not found
            const tableWrapper = document.querySelector('.table-wrapper');
            if (tableWrapper) {
                tableWrapper.parentNode.insertBefore(this.dateNavContainer, tableWrapper);
                console.log('📅 日期导航已插入到表格区域（备用位置）');
            }
        }

        if (this.availableDates.length > 1) {
            this.populateDateSelector();
            this.bindDateNavEvents();
        }
    }

    populateDateSelector() {
        const selector = this.dateNavContainer.querySelector('.date-selector-compact');
        if (selector) {
            selector.innerHTML = '';
            this.availableDates.forEach(date => {
                const option = document.createElement('option');
                option.value = date;
                option.textContent = `${date}`;
                selector.appendChild(option);
            });
        }
    }

    bindDateNavEvents() {
        const prevBtn = this.dateNavContainer.querySelector('.prev-date');
        const nextBtn = this.dateNavContainer.querySelector('.next-date');
        const selector = this.dateNavContainer.querySelector('.date-selector-compact');

        prevBtn.addEventListener('click', () => this.navigate('prev'));
        nextBtn.addEventListener('click', () => this.navigate('next'));
        selector.addEventListener('change', (e) => this.switchToDate(e.target.value));

        // Keyboard navigation - 检查是否已经绑定，避免重复绑定
        if (!window.paginationKeyboardEventsBound) {
            document.addEventListener('keydown', (e) => {
                console.log(`🔍 [键盘检查18] date_pagination_manager收到键盘事件: ${e.key}`);

                if (e.key === 'PageUp') {
                    e.preventDefault();
                    console.log(`🔍 [键盘检查19] 分页管理器处理PageUp事件`);
                    this.navigate('prev');
                }
                if (e.key === 'PageDown') {
                    e.preventDefault();
                    console.log(`🔍 [键盘检查20] 分页管理器处理PageDown事件`);
                    this.navigate('next');
                }
            });

            window.paginationKeyboardEventsBound = true;
            console.log('✅ 分页管理器键盘事件已绑定（防重复）');
        } else {
            console.log('⚠️ 分页管理器键盘事件已存在，跳过重复绑定');
        }
    }

    navigate(direction) {
        console.log(`🔍 [日期切换1] 开始导航: ${direction}`);
        console.log(`🔍 [日期切换2] 当前日期: ${this.currentDate}`);
        console.log(`🔍 [日期切换3] 可用日期列表: ${this.availableDates.join(', ')}`);

        const currentIndex = this.availableDates.indexOf(this.currentDate);
        console.log(`🔍 [日期切换4] 当前索引: ${currentIndex}`);

        let newIndex = currentIndex;
        if (direction === 'prev' && currentIndex > 0) {
            newIndex = currentIndex - 1;
            console.log(`🔍 [日期切换5] prev: ${currentIndex} -> ${newIndex}`);
        } else if (direction === 'next' && currentIndex < this.availableDates.length - 1) {
            newIndex = currentIndex + 1;
            console.log(`🔍 [日期切换6] next: ${currentIndex} -> ${newIndex}`);
        } else {
            console.log(`🔍 [日期切换7] 无法切换: 已到边界`);
        }

        if (newIndex !== currentIndex) {
            const newDate = this.availableDates[newIndex];
            console.log(`🔍 [日期切换8] 切换到新日期: ${newDate}`);
            this.switchToDate(newDate);
        } else {
            console.log(`🔍 [日期切换9] 索引未变化，不切换`);
        }
    }

    switchToDate(date) {
        if (!this.data[date]) {
            console.error(`❌ Data for date ${date} not found!`);
            return;
        }

        console.log(`🔄 Switching to date: ${date}`);
        this.currentDate = date;

        this.renderTableData(this.data[date]);
        this.updateNavUI();

        // 先重新绑定所有交互功能
        if (window.rebindAllFeatures) {
            window.rebindAllFeatures();
        }

        // 应用之前保存的排序状态
        setTimeout(() => {
            this.applySortState();
        }, 100); // 增加延迟确保重新绑定完成后再应用排序
    }

    renderTableData(records) {
        console.log(`🔍 [排查12] 开始渲染数据 - 记录数: ${records.length}`);

        // Clear existing table body
        this.tableBody.innerHTML = '';
        console.log(`🔍 [排查13] 已清空表格内容`);

        if (records.length === 0) {
            const row = this.tableBody.insertRow();
            const cell = row.insertCell();
            cell.colSpan = this.tableHeader.rows[0].cells.length;
            cell.textContent = '该日期没有数据。';
            cell.style.textAlign = 'center';
            console.log(`🔍 [排查14] 无数据，显示提示信息`);
            return;
        }

        const fragment = document.createDocumentFragment();
        const columnOrder = Array.from(this.tableHeader.rows[0].cells).map(th => th.textContent.trim());
        console.log(`🔍 [排查15] 列顺序: ${columnOrder.join(', ')}`);

        // 检查第一条记录的数据
        if (records[0]) {
            console.log(`🔍 [排查16] 第一条记录示例:`, records[0]);
        }

        console.log(`🔍 [排查22] 开始渲染循环 - 记录数: ${records.length}, 列数: ${columnOrder.length}`);

        for (let recordIndex = 0; recordIndex < records.length; recordIndex++) {
            const record = records[recordIndex];
            const row = document.createElement('tr');

            if (recordIndex === 0) {
                console.log(`🔍 [排查23] 第一条记录处理开始`);
            }

            for (let colIndex = 0; colIndex < columnOrder.length; colIndex++) {
                const column = columnOrder[colIndex];
                const cell = document.createElement('td');

                // **关键修复**: 清理表头文本，移除排序指示器
                const cleanColumn = column.replace(/\s*[▲▼]\s*$/, '').trim();

                // **FIX:** Correctly access the value from the record object using the cleaned column name as the key.
                let value = record[cleanColumn];

                // 详细检查列12的数据映射（第一条记录）
                if (colIndex === 12 && recordIndex === 0) {
                    console.log(`🔍 [排查20] 列12映射检查:`);
                    console.log(`  - 原始表头文本: "${column}"`);
                    console.log(`  - 清理后表头文本: "${cleanColumn}"`);
                    console.log(`  - 原始数据字段: ${Object.keys(record).join(', ')}`);
                    console.log(`  - 映射结果: "${value}"`);
                    console.log(`  - 原始记录:`, record);
                }

                // 额外检查：如果是第一条记录，显示所有列的映射情况
                if (recordIndex === 0 && colIndex < 15) {
                    console.log(`🔍 [排查21] 列${colIndex}("${column}" -> "${cleanColumn}") -> "${value}"`);
                }

                cell.innerHTML = (value === null || value === undefined) ? '' : value;
                row.appendChild(cell);
            }
            fragment.appendChild(row);
        }

        this.tableBody.appendChild(fragment);
        // 最终检查渲染结果
        const finalRows = this.tableBody.querySelectorAll('tr');
        console.log(`🔍 [排查17] 渲染完成后实际行数: ${finalRows.length}`);

        // 检查第一行的数据完整性
        if (finalRows.length > 0) {
            const firstRow = finalRows[0];
            const cellCount = firstRow.cells.length;
            console.log(`🔍 [排查18] 第一行单元格数: ${cellCount}`);

            // 检查列12的数据（用户排序的列）
            if (firstRow.cells[12]) {
                console.log(`🔍 [排查19] 第一行列12数据: "${firstRow.cells[12].textContent.trim()}"`);
            } else {
                console.log(`🔍 [排查19] 第一行列12不存在`);
            }
        }

        console.log(`✅ Rendered ${records.length} rows for date ${this.currentDate}.`);
    }

    updateNavUI() {
        const dateValue = this.dateNavContainer.querySelector('.current-date-value');
        const dateCount = this.dateNavContainer.querySelector('.current-date-count');

        if (dateValue) {
            dateValue.textContent = this.currentDate;
        }
        if (dateCount) {
            dateCount.textContent = `(${this.data[this.currentDate].length}条)`;
        }

        if (this.availableDates.length > 1) {
            const selector = this.dateNavContainer.querySelector('.date-selector-compact');
            const prevBtn = this.dateNavContainer.querySelector('.prev-date');
            const nextBtn = this.dateNavContainer.querySelector('.next-date');
            const currentIndex = this.availableDates.indexOf(this.currentDate);

            if (selector) {
                selector.value = this.currentDate;
            }
            if (prevBtn) {
                prevBtn.disabled = currentIndex === 0;
            }
            if (nextBtn) {
                nextBtn.disabled = currentIndex === this.availableDates.length - 1;
            }
        }
    }

    // 排序状态管理方法
    setSortState(columnIndex, direction) {
        // 如果正在应用排序状态，避免重复保存
        if (this._isApplyingSort) {
            console.log(`⏸️ 跳过排序状态保存（正在应用中）: 列${columnIndex}, 方向${direction}`);
            return;
        }

        // 检查是否是重复的排序状态
        if (this.sortState.column === columnIndex && this.sortState.direction === direction) {
            console.log(`⏸️ 跳过重复的排序状态保存: 列${columnIndex}, 方向${direction}`);
            return;
        }

        this.sortState = { column: columnIndex, direction: direction };
        console.log(`📊 排序状态已保存: 列${columnIndex}, 方向${direction}`);
    }

    // 应用排序状态到当前表格
    applySortState() {
        if (this.sortState.column >= 0) {
            console.log(`🔄 应用排序状态: 列${this.sortState.column}, 方向${this.sortState.direction}`);

            // 获取表格和表头
            const table = document.querySelector('table.dataframe');
            const headers = table.querySelectorAll('th');

            console.log(`🔍 [排查7] 应用排序前检查:`);
            console.log(`  - 表格存在: ${!!table}`);
            console.log(`  - 表头数量: ${headers.length}`);
            console.log(`  - 目标列存在: ${!!headers[this.sortState.column]}`);
            console.log(`  - sortTable函数存在: ${typeof sortTable === 'function'}`);

            // 检查应用排序前的数据
            const tbody = table.querySelector('tbody');
            const beforeRows = tbody.querySelectorAll('tr');
            console.log(`🔍 [排查8] 应用排序前行数: ${beforeRows.length}`);
            if (beforeRows.length > 0 && beforeRows[0].cells[this.sortState.column]) {
                console.log(`🔍 [排查9] 应用排序前第一行列${this.sortState.column}数据: "${beforeRows[0].cells[this.sortState.column].textContent.trim()}"`);
            }

            if (table && headers[this.sortState.column] && typeof sortTable === 'function') {
                // 临时禁用排序状态保存，避免循环触发
                this._isApplyingSort = true;

                // 直接调用table_sorter.js中的原始排序函数
                sortTable(table, this.sortState.column, this.sortState.direction);

                // 检查应用排序后的数据
                const afterRows = tbody.querySelectorAll('tr');
                console.log(`🔍 [排查10] 应用排序后行数: ${afterRows.length}`);
                if (afterRows.length > 0 && afterRows[0].cells[this.sortState.column]) {
                    console.log(`🔍 [排查11] 应用排序后第一行列${this.sortState.column}数据: "${afterRows[0].cells[this.sortState.column].textContent.trim()}"`);
                }

                // 手动更新表头样式
                this.updateHeaderStyles(headers, this.sortState.column, this.sortState.direction);

                // 排序完成后重新绑定所有功能
                if (window.rebindAllFeatures) {
                    window.rebindAllFeatures();
                    console.log(`🔄 排序状态应用后重新绑定所有功能`);
                }

                // 恢复排序状态保存
                setTimeout(() => {
                    this._isApplyingSort = false;
                }, 100);

                console.log(`✅ 排序状态应用完成: 列${this.sortState.column}, 方向${this.sortState.direction}`);
            } else {
                console.warn('⚠️ 无法应用排序状态：表格或排序函数未找到');
            }
        }
    }

    // 根据保存的状态排序表格
    sortTableByState(table, columnIndex, direction) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        const collator = new Intl.Collator(['zh-CN', 'en-US'], { numeric: true });

        rows.sort((a, b) => {
            const aText = a.cells[columnIndex] ? a.cells[columnIndex].textContent.trim() : '';
            const bText = b.cells[columnIndex] ? b.cells[columnIndex].textContent.trim() : '';

            // 预处理，移除单位如'亿', '万', '%'
            const cleanA = aText.replace(/亿|万|%/g, '');
            const cleanB = bText.replace(/亿|万|%/g, '');

            const valA = isNaN(parseFloat(cleanA)) ? aText : parseFloat(cleanA);
            const valB = isNaN(parseFloat(cleanB)) ? bText : parseFloat(cleanB);

            let comparison = 0;
            if (typeof valA === 'number' && typeof valB === 'number') {
                comparison = valA - valB;
            } else {
                comparison = collator.compare(aText, bText);
            }

            return direction === 'asc' ? comparison : -comparison;
        });

        // 重新将排序后的行插入tbody
        rows.forEach(row => tbody.appendChild(row));
    }

    // 更新表头样式
    updateHeaderStyles(headers, activeIndex, direction) {
        headers.forEach((header, index) => {
            // 移除旧的排序指示
            const indicator = header.querySelector('.sort-indicator');
            if (indicator) {
                indicator.remove();
            }
            // 为活动列添加新的排序指示
            if (index === activeIndex) {
                const newIndicator = document.createElement('span');
                newIndicator.className = 'sort-indicator';
                newIndicator.innerHTML = direction === 'asc' ? ' &#9650;' : ' &#9660;'; // ▲ or ▼
                header.appendChild(newIndicator);
            }
        });
    }
}

// Initialize when the DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (typeof EMBEDDED_DATA !== 'undefined') {
        const manager = new DatePaginationManager();
        // 设置全局引用，供table_sorter.js使用
        window.globalDatePaginationManager = manager;
    } else {
        console.warn("⚠️ EMBEDDED_DATA not found. Pagination will not be initialized.");
    }
});