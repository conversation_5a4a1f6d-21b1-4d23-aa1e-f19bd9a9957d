#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复盘数据索引自动更新脚本
功能：自动扫描data目录中的日期文件，更新index.json
作者：AI Assistant
日期：2025-07-23
"""

import json
import os
import re
from datetime import datetime
from pathlib import Path

def update_data_index(data_dir="data"):
    """
    自动更新数据索引文件
    
    Args:
        data_dir (str): 数据目录路径，默认为"data"
    
    Returns:
        list: 更新后的日期列表
    """
    
    # 确保数据目录存在
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return []
    
    index_file = os.path.join(data_dir, "index.json")
    
    print(f"🔍 正在扫描数据目录: {data_dir}")
    
    # 扫描所有日期文件
    date_files = []
    total_files = 0
    
    for file in os.listdir(data_dir):
        if file.startswith("date_") and file.endswith(".json"):
            total_files += 1
            # 提取日期，支持格式：date_YYYY-MM-DD.json
            match = re.search(r'date_(\d{4}-\d{2}-\d{2})\.json', file)
            if match:
                date_str = match.group(1)
                date_files.append(date_str)
                print(f"  ✅ 发现日期文件: {file} -> {date_str}")
            else:
                print(f"  ⚠️ 文件名格式不正确: {file}")
    
    if not date_files:
        print(f"❌ 未找到有效的日期文件（格式：date_YYYY-MM-DD.json）")
        return []
    
    # 排序日期（从早到晚）
    date_files.sort()
    
    print(f"📊 找到 {len(date_files)} 个有效日期文件")
    
    # 读取现有的index.json（如果存在）
    existing_data = {}
    if os.path.exists(index_file):
        try:
            with open(index_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
            print(f"📖 读取现有索引文件: {len(existing_data.get('available_dates', []))} 个日期")
        except Exception as e:
            print(f"⚠️ 读取现有索引文件失败: {e}")
    
    # 创建新的索引数据
    index_data = {
        "last_updated": datetime.now().isoformat(),
        "available_dates": date_files,
        "total_dates": len(date_files),
        "data_directory": data_dir,
        "file_pattern": "date_YYYY-MM-DD.json"
    }
    
    # 如果有额外的元数据，保留它们
    if "metadata" in existing_data:
        index_data["metadata"] = existing_data["metadata"]
    
    # 写入更新后的index.json
    try:
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 成功更新 {index_file}")
        print(f"📅 包含日期: {date_files[0]} 到 {date_files[-1]}")
        print(f"📊 总计: {len(date_files)} 个日期")
        
        # 显示变化
        old_dates = set(existing_data.get('available_dates', []))
        new_dates = set(date_files)
        
        added_dates = new_dates - old_dates
        removed_dates = old_dates - new_dates
        
        if added_dates:
            print(f"➕ 新增日期: {sorted(added_dates)}")
        if removed_dates:
            print(f"➖ 移除日期: {sorted(removed_dates)}")
        if not added_dates and not removed_dates and old_dates:
            print(f"🔄 日期列表无变化")
            
    except Exception as e:
        print(f"❌ 写入索引文件失败: {e}")
        return []
    
    return date_files

def validate_date_files(data_dir="data"):
    """
    验证日期文件的格式和内容
    
    Args:
        data_dir (str): 数据目录路径
    """
    print(f"\n🔍 验证数据文件...")
    
    for file in os.listdir(data_dir):
        if file.startswith("date_") and file.endswith(".json"):
            file_path = os.path.join(data_dir, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list) and len(data) > 0:
                    # 检查第一条记录的字段
                    first_record = data[0]
                    required_fields = ['代码', '名称']
                    missing_fields = [field for field in required_fields if field not in first_record]
                    
                    if missing_fields:
                        print(f"  ⚠️ {file}: 缺少必需字段 {missing_fields}")
                    else:
                        print(f"  ✅ {file}: {len(data)} 条记录，格式正确")
                        
                        # 检查是否有数据来源行（应该被过滤）
                        source_rows = [row for row in data if any('数据来源' in str(v) or '通达信' in str(v) for v in row.values())]
                        if source_rows:
                            print(f"    ℹ️ 包含 {len(source_rows)} 行数据来源信息（将被自动过滤）")
                else:
                    print(f"  ❌ {file}: 数据格式错误或为空")
                    
            except json.JSONDecodeError as e:
                print(f"  ❌ {file}: JSON格式错误 - {e}")
            except Exception as e:
                print(f"  ❌ {file}: 读取失败 - {e}")

def main():
    """主函数"""
    print("🚀 复盘数据索引自动更新工具")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 查找data目录
    data_dir = "data"
    if not os.path.exists(data_dir):
        # 尝试其他可能的路径
        possible_paths = [
            "reports/data",
            "../data",
            "excel分析/reports/data"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                data_dir = path
                break
        else:
            print(f"❌ 找不到数据目录，请确保在正确的目录下运行此脚本")
            print(f"   预期路径: {possible_paths}")
            return
    
    print(f"📂 使用数据目录: {data_dir}")
    
    # 更新索引
    dates = update_data_index(data_dir)
    
    if dates:
        # 验证文件
        validate_date_files(data_dir)
        
        print("\n" + "=" * 50)
        print("✅ 数据索引更新完成！")
        print(f"📊 可用日期范围: {dates[0]} 到 {dates[-1]}")
        print(f"📈 总计: {len(dates)} 个交易日")
        print("\n💡 提示: 刷新网页（F5）即可看到更新后的数据")
    else:
        print("\n❌ 数据索引更新失败")

if __name__ == "__main__":
    main()
