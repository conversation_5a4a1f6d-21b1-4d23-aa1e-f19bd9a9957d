#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的端到端数据监控系统
功能：监控"复盘数据"目录，自动转换.xls文件为JSON，更新网页索引
作者：AI Assistant
日期：2025-07-23
"""

import os
import sys
import time
import threading
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import logging

# 确保当前目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入现有的处理器
try:
    from excel_processor import TongDaXinProcessor
except ImportError as e:
    print(f"❌ 无法导入excel_processor模块: {e}")
    print(f"📂 当前工作目录: {os.getcwd()}")
    print(f"📂 脚本所在目录: {current_dir}")
    print("💡 请确保excel_processor.py文件存在于脚本同一目录下")
    sys.exit(1)

# 导入数据监控模块
try:
    from data_monitor import DataIndexManager
except ImportError:
    print("❌ 无法导入data_monitor模块，请先运行: python install_dependencies.py")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompleteDataHandler(FileSystemEventHandler):
    """完整数据处理器"""
    
    def __init__(self, source_dir="../复盘数据", output_dir="reports/data"):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.processor = TongDaXinProcessor(str(self.source_dir), str(self.output_dir))
        self.index_manager = DataIndexManager(str(self.output_dir))
        self.last_process_time = {}
        self.process_delay = 3  # 延迟3秒处理，避免文件正在写入
        
        logger.info(f"📂 监控源目录: {self.source_dir.absolute()}")
        logger.info(f"📂 输出目录: {self.output_dir.absolute()}")
    
    def on_any_event(self, event):
        """处理文件系统事件"""
        if event.is_directory:
            return
        
        # 只处理支持的文件格式
        file_path = Path(event.src_path)
        if file_path.suffix.lower() not in ['.xls', '.xlsx', '.csv']:
            return
        
        filename = file_path.name
        current_time = time.time()
        
        # 防抖处理
        if filename in self.last_process_time:
            if current_time - self.last_process_time[filename] < self.process_delay:
                return
        
        self.last_process_time[filename] = current_time
        
        if event.event_type == 'created':
            logger.info(f"📁 检测到新文件: {filename}")
            # 延迟处理，确保文件写入完成
            threading.Timer(self.process_delay, self._process_file, [file_path]).start()
            
        elif event.event_type == 'modified':
            logger.info(f"📝 检测到文件修改: {filename}")
            # 延迟处理
            threading.Timer(self.process_delay, self._process_file, [file_path]).start()
            
        elif event.event_type == 'deleted':
            logger.info(f"🗑️ 检测到文件删除: {filename}")
            # 文件删除时，检查是否需要清理对应的JSON文件
            self._handle_file_deletion(filename)
    
    def _process_file(self, file_path):
        """处理单个文件"""
        try:
            filename = file_path.name
            logger.info(f"🔄 开始处理文件: {filename}")
            
            # 检查文件是否仍然存在（可能在延迟期间被删除）
            if not file_path.exists():
                logger.warning(f"⚠️ 文件已不存在: {filename}")
                return
            
            # 检查文件是否可以读取（可能还在写入中）
            try:
                with open(file_path, 'rb') as f:
                    f.read(1024)  # 尝试读取一小部分
            except (PermissionError, IOError) as e:
                logger.warning(f"⚠️ 文件可能正在写入中，稍后重试: {filename}")
                # 再次延迟处理
                threading.Timer(self.process_delay, self._process_file, [file_path]).start()
                return
            
            # 使用现有的处理器处理文件
            success = self.processor.process_single_file(filename)
            
            if success:
                logger.info(f"✅ 文件处理成功: {filename}")
                
                # 更新索引文件
                if self.index_manager.update_index():
                    logger.info("📊 索引文件已更新")
                else:
                    logger.warning("⚠️ 索引文件更新失败")
                    
            else:
                logger.error(f"❌ 文件处理失败: {filename}")
                
        except Exception as e:
            logger.error(f"❌ 处理文件时发生错误 {file_path.name}: {e}")
    
    def _handle_file_deletion(self, filename):
        """处理文件删除"""
        try:
            # 从文件名提取日期
            date_str = self.processor.extract_date_from_filename(filename)
            if date_str:
                # 检查对应的JSON文件是否存在
                json_file = self.output_dir / f"date_{date_str}.json"
                if json_file.exists():
                    logger.info(f"🗑️ 删除对应的JSON文件: {json_file.name}")
                    json_file.unlink()
                    
                    # 更新索引
                    if self.index_manager.update_index():
                        logger.info("📊 索引文件已更新（删除后）")
                        
        except Exception as e:
            logger.error(f"❌ 处理文件删除时发生错误: {e}")

class CompleteMonitor:
    """完整监控系统"""
    
    def __init__(self, source_dir="../复盘数据", output_dir="reports/data"):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.observer = None
        self.running = False
        
        # 确保目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def start(self):
        """启动监控"""
        if self.running:
            logger.warning("监控已在运行中")
            return True
            
        if not self.source_dir.exists():
            logger.error(f"源数据目录不存在: {self.source_dir}")
            return False
        
        logger.info("🚀 启动完整数据监控系统...")
        logger.info(f"📂 监控目录: {self.source_dir.absolute()}")
        logger.info(f"📂 输出目录: {self.output_dir.absolute()}")
        
        # 初始处理现有文件
        self._initial_process()
        
        # 启动文件监控
        event_handler = CompleteDataHandler(str(self.source_dir), str(self.output_dir))
        self.observer = Observer()
        self.observer.schedule(event_handler, str(self.source_dir), recursive=False)
        
        try:
            self.observer.start()
            self.running = True
            logger.info("👀 文件监控已启动，等待文件变化...")
            return True
        except Exception as e:
            logger.error(f"启动监控失败: {e}")
            return False
    
    def _initial_process(self):
        """初始处理现有文件"""
        logger.info("🔄 初始处理现有文件...")
        try:
            processor = TongDaXinProcessor(str(self.source_dir), str(self.output_dir))
            success = processor.process_all_files()
            if success:
                logger.info("✅ 初始处理完成")
            else:
                logger.warning("⚠️ 初始处理部分失败")
        except Exception as e:
            logger.error(f"❌ 初始处理失败: {e}")
    
    def stop(self):
        """停止监控"""
        if not self.running:
            return
            
        if self.observer:
            self.observer.stop()
            self.observer.join()
            
        self.running = False
        logger.info("🛑 文件监控已停止")
    
    def is_running(self):
        """检查是否在运行"""
        return self.running
    
    def manual_process(self):
        """手动处理所有文件"""
        logger.info("🔄 手动处理所有文件...")
        try:
            processor = TongDaXinProcessor(str(self.source_dir), str(self.output_dir))
            success = processor.process_all_files()
            if success:
                logger.info("✅ 手动处理完成")
                return True
            else:
                logger.error("❌ 手动处理失败")
                return False
        except Exception as e:
            logger.error(f"❌ 手动处理时发生错误: {e}")
            return False

def main():
    """主函数"""
    import signal
    
    # 检查依赖
    try:
        import watchdog
        import pandas as pd
    except ImportError as e:
        logger.error(f"❌ 缺少依赖库: {e}")
        logger.info("请运行: python install_dependencies.py")
        return
    
    # 查找正确的工作目录
    current_dir = Path.cwd()
    if current_dir.name == "excel分析":
        # 已经在正确目录
        source_dir = "../复盘数据"
        output_dir = "reports/data"
    elif (current_dir / "excel分析").exists():
        # 需要进入excel分析目录
        os.chdir("excel分析")
        source_dir = "../复盘数据"
        output_dir = "reports/data"
    else:
        logger.error("❌ 请在excel分析目录或其父目录下运行此脚本")
        return
    
    # 创建监控器
    monitor = CompleteMonitor(source_dir, output_dir)
    
    # 信号处理
    def signal_handler(sig, frame):
        logger.info("收到停止信号...")
        monitor.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动监控
    if monitor.start():
        try:
            logger.info("✅ 监控系统运行中，按 Ctrl+C 停止")
            logger.info("💡 现在可以将.xls文件放入复盘数据目录，系统会自动处理")
            while monitor.is_running():
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            monitor.stop()
    else:
        logger.error("❌ 监控启动失败")

if __name__ == "__main__":
    main()
