<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页数据分析报告</title>

    <!-- 现有样式文件 -->
    <link rel="stylesheet" href="date_pagination_styles.css">

    <!-- ECharts 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

    <style>
        /* 基础样式 - 与现有标准保持一致 */
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            overflow: hidden; /* 防止页面滚动 */
        }

        /* 主容器 - 支持动态布局 */
        .main-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
            transition: all 0.3s ease;
        }

        /* 顶部信息区域 - 精简化，支持动态隐藏 */
        .header-section {
            flex-shrink: 0;
            background: #fff;
            padding: 10px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 60px;
            transition: transform 0.3s ease, opacity 0.3s ease;
            transform: translateY(0);
            opacity: 1;
        }

        .header-title {
            margin: 0;
            color: #2c3e50;
            font-size: 1.5em;
            font-weight: bold;
        }

        .header-subtitle {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }

        /* 顶部导航区域 - 极简风格 */
        .header-navigation {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-shrink: 0;
        }

        /* 极简日期导航样式 */
        .date-nav-compact {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(74, 144, 226, 0.1);
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid rgba(74, 144, 226, 0.2);
        }

        .date-info-compact {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.85em;
            color: #4A90E2;
            font-weight: 500;
        }

        .date-controls-compact {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .date-btn-compact {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.75em;
            transition: background-color 0.2s;
        }

        .date-btn-compact:hover {
            background: #357ABD;
        }

        /* 内容区域 */
        .content-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background: #fff;
            margin: 0 10px 10px 10px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 状态信息 */
        .status-bar {
            padding: 8px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.85em;
            color: #666;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-left {
            display: flex;
            gap: 15px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }

        /* 加载和错误状态 */
        .loading, .error {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 40px;
        }

        .loading {
            color: #666;
        }

        .error {
            color: #dc3545;
        }

        .loading::before {
            content: "⏳";
            font-size: 48px;
            margin-bottom: 15px;
        }

        .error::before {
            content: "❌";
            font-size: 48px;
            margin-bottom: 15px;
        }

        /* 隐藏状态 */
        .hidden {
            display: none !important;
        }

        /* 表格区域样式 - 与现有JS模块完全兼容 */
        .table-section {
            flex: 1;
            background: #fff;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            transition: all 0.3s ease;
            margin: 0;
            padding: 0;
        }

        .table-wrapper {
            flex: 1;
            overflow: auto;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin: 5px 10px;
            position: relative;
            min-height: 350px;
        }

        /* 表格样式 */
        table.dataframe {
            border-collapse: collapse;
            width: 100%;
            min-width: 100%;
            font-size: 14px;
            background: white;
        }

        /* 固定表头 */
        table.dataframe th {
            white-space: nowrap;
            border: 1px solid #dee2e6;
            padding: 12px 15px;
            text-align: center;
            background: #f8f9fa;
            color: #333;
            font-weight: 600;
            font-size: 13px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        table.dataframe td {
            white-space: nowrap;
            border: 1px solid #dee2e6;
            padding: 10px 15px;
            text-align: center;
            font-size: 13px;
        }

        table.dataframe tr:nth-child(even) {
            background: #f8f9fa;
        }

        /* K线按钮样式 */
        .kline-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .kline-btn:hover {
            background-color: #0056b3;
        }

        /* 排序指示器 */
        .sort-indicator {
            font-size: 12px;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <!-- 主容器 - 与现有标准保持一致 -->
    <div class="main-container">
        <!-- 顶部信息区域 -->
        <div class="header-section">
            <div class="header-info">
                <h1 class="header-title">📊 通达信复盘分析系统</h1>
                <p class="header-subtitle">智能数据分析 · 实时K线联动 · 高级筛选功能</p>
            </div>

            <!-- 顶部导航区域 -->
            <div class="header-navigation">
                <!-- 极简日期导航 -->
                <div class="date-nav-compact">
                    <div class="date-info-compact">
                        <span>📅</span>
                        <select id="dateSelector" style="border: none; background: transparent; color: #4A90E2; font-weight: 500;">
                            <option value="">加载中...</option>
                        </select>
                    </div>
                    <div class="date-controls-compact">
                        <button id="prevDateBtn" class="date-btn-compact" title="上一日期">◀</button>
                        <button id="nextDateBtn" class="date-btn-compact" title="下一日期">▶</button>
                        <button id="refreshBtn" class="date-btn-compact" title="刷新数据">🔄</button>
                        <button id="filterBtn" class="date-btn-compact" title="高级筛选">🔍</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span id="statusText">系统就绪</span>
                </div>
                <div class="status-item">
                    <span>📋 数据量: <span id="recordCount">-</span></span>
                </div>
                <div class="status-item">
                    <span>⏱️ 更新时间: <span id="updateTime">-</span></span>
                </div>
            </div>
            <div class="status-right">
                <span id="currentDateInfo">-</span>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-section">
            <div id="loadingIndicator" class="loading">
                <div>正在初始化数据加载器...</div>
            </div>

            <div id="errorIndicator" class="error hidden">
                <div>数据加载失败，请检查数据文件是否存在</div>
            </div>

            <!-- 这里将动态插入表格和图表内容 -->
            <div id="mainContent" class="hidden">
                <!-- 表格区域 - 完全匹配现有JS模块期望的DOM结构 -->
                <div class="table-section" id="tableSection">
                    <div class="table-wrapper">
                        <table class="dataframe">
                            <thead>
                                <!-- 表头将由现有的JS模块动态生成 -->
                            </thead>
                            <tbody>
                                <!-- 表格内容将由现有的JS模块动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- K线图容器 -->
                <div id="klineChartContainer" class="hidden" style="width: 100%; height: 400px;"></div>

                <!-- 筛选面板容器 -->
                <div id="filterContainer"></div>
            </div>
        </div>
    </div>

    <!-- 筛选面板（隐藏） -->
    <div id="filterPanel" class="hidden">
        <!-- 筛选功能将在这里动态生成 -->
    </div>
    
    <!-- 核心JavaScript文件 -->
    <!-- 注意：保持现有JS文件的加载顺序，确保兼容性 -->
    <script src="data_loader.js"></script>
    <script src="data_manager.js"></script>
    <script src="date_pagination_manager.js"></script>
    <script src="table_sorter.js"></script>
    <script src="kline_chart_helper.js"></script>

    <!-- 现有的核心模块 - 只加载必要的 -->
    <script src="smart_table_renderer.js"></script>
    
    <!-- 主应用脚本 -->
    <script>
        // 全局应用状态
        const app = {
            currentDate: null,
            currentData: null,
            isInitialized: false,
            datePaginationManager: null,
            dataManager: null
        };

        // 页面初始化
        async function initializePage() {
            try {
                console.log('🚀 开始初始化通达信复盘分析系统...');

                // 等待数据加载器初始化
                const success = await window.initializeDataLoader();
                if (!success) {
                    // 尝试降级到现有数据
                    console.log('🔄 尝试使用现有数据结构...');
                    await initializeWithExistingData();
                }

                // 初始化现有的JS模块
                await initializeExistingModules();

                // 填充日期选择器
                await populateDateSelector();

                // 绑定事件
                bindEvents();

                // 显示主要内容
                showMainContent();

                // 加载默认日期数据
                await loadDefaultDate();

                app.isInitialized = true;
                updateStatus('系统就绪', 'success');

                console.log('✅ 页面初始化完成');

            } catch (error) {
                console.error('❌ 页面初始化失败:', error);
                showError('初始化失败: ' + error.message);
            }
        }

        // 初始化现有的JS模块
        async function initializeExistingModules() {
            try {
                console.log('🔧 初始化现有JS模块...');

                // 等待现有模块加载完成
                await waitForModules();

                // 初始化数据管理器
                if (window.DataManager) {
                    app.dataManager = new DataManager();
                    console.log('✅ DataManager 初始化完成');
                }

                // 初始化日期分页管理器
                if (window.DatePaginationManager) {
                    app.datePaginationManager = new DatePaginationManager();
                    console.log('✅ DatePaginationManager 初始化完成');
                }

                // 初始化表格排序器
                if (window.TableSorter) {
                    console.log('✅ TableSorter 可用');
                }

                // 初始化K线图助手
                if (window.KlineChartHelper) {
                    console.log('✅ KlineChartHelper 可用');
                }

            } catch (error) {
                console.error('❌ 现有模块初始化失败:', error);
                throw error;
            }
        }

        // 等待模块加载
        function waitForModules() {
            return new Promise((resolve) => {
                const checkModules = () => {
                    if (window.DataManager && window.DatePaginationManager) {
                        resolve();
                    } else {
                        setTimeout(checkModules, 100);
                    }
                };
                checkModules();
            });
        }

        // 使用现有数据结构初始化
        async function initializeWithExistingData() {
            console.log('🔄 使用现有数据结构初始化...');

            // 检查是否有嵌入数据
            if (window.EMBEDDED_DATA && Object.keys(window.EMBEDDED_DATA).length > 0) {
                console.log('✅ 发现嵌入数据，使用兼容模式');
                return true;
            }

            // 检查新的data目录数据
            try {
                const response = await fetch('data/index.json');
                if (response.ok) {
                    const indexData = await response.json();
                    console.log('✅ 发现新data数据，使用新架构');

                    // 模拟EMBEDDED_DATA结构以兼容现有JS模块
                    window.EMBEDDED_DATA = {};
                    for (const date of indexData.available_dates || []) {
                        try {
                            const dataResponse = await fetch(`data/date_${date}.json`);
                            if (dataResponse.ok) {
                                const data = await dataResponse.json();
                                window.EMBEDDED_DATA[date] = data;
                                console.log(`✅ 加载日期数据: ${date} (${data.length}条记录)`);
                            }
                        } catch (e) {
                            console.warn(`⚠️ 无法加载日期数据: ${date}`);
                        }
                    }

                    if (Object.keys(window.EMBEDDED_DATA).length > 0) {
                        console.log(`✅ 成功加载 ${Object.keys(window.EMBEDDED_DATA).length} 个日期的数据`);
                        return true;
                    }
                }
            } catch (e) {
                console.log('ℹ️ 未发现新data数据');
            }

            // 检查是否有date_data目录的数据（向后兼容）
            try {
                const response = await fetch('date_data/date_index.json');
                if (response.ok) {
                    const indexData = await response.json();
                    console.log('✅ 发现date_data数据，使用现有架构');

                    // 模拟EMBEDDED_DATA结构
                    window.EMBEDDED_DATA = {};
                    for (const date of indexData.available_dates || []) {
                        try {
                            const dataResponse = await fetch(`date_data/data_${date}.json`);
                            if (dataResponse.ok) {
                                window.EMBEDDED_DATA[date] = await dataResponse.json();
                            }
                        } catch (e) {
                            console.warn(`⚠️ 无法加载日期数据: ${date}`);
                        }
                    }
                    return true;
                }
            } catch (e) {
                console.log('ℹ️ 未发现date_data数据');
            }

            throw new Error('未找到可用的数据源');
        }
        
        // 填充日期选择器
        async function populateDateSelector() {
            const selector = document.getElementById('dateSelector');
            let dates = [];

            // 尝试从不同数据源获取日期
            if (window.dataLoader && window.dataLoader.indexData) {
                dates = window.dataLoader.getAvailableDates();
            } else if (window.EMBEDDED_DATA) {
                dates = Object.keys(window.EMBEDDED_DATA).sort();
            } else if (app.datePaginationManager) {
                dates = app.datePaginationManager.availableDates || [];
            }

            selector.innerHTML = '';

            if (dates.length === 0) {
                selector.innerHTML = '<option value="">暂无可用数据</option>';
                return;
            }

            // 添加日期选项（最新的在前面）
            const sortedDates = [...dates].reverse();
            sortedDates.forEach(date => {
                const option = document.createElement('option');
                option.value = date;
                option.textContent = `${date} (${formatDateChinese(date)})`;
                selector.appendChild(option);
            });

            // 设置默认选中最新日期
            if (sortedDates.length > 0) {
                selector.value = sortedDates[0];
                app.currentDate = sortedDates[0];
            }

            console.log(`📅 已加载 ${dates.length} 个可用日期`);
        }
        
        // 格式化日期为中文
        function formatDateChinese(dateStr) {
            const date = new Date(dateStr);
            const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            return weekdays[date.getDay()];
        }
        
        // 绑定事件
        function bindEvents() {
            // 日期选择
            document.getElementById('dateSelector').addEventListener('change', handleDateChange);

            // 日期导航按钮
            document.getElementById('prevDateBtn').addEventListener('click', () => switchDate(-1));
            document.getElementById('nextDateBtn').addEventListener('click', () => switchDate(1));

            // 刷新按钮
            document.getElementById('refreshBtn').addEventListener('click', handleRefresh);

            // 筛选按钮
            document.getElementById('filterBtn').addEventListener('click', handleFilter);

            // 键盘快捷键
            document.addEventListener('keydown', handleKeyboard);
        }
        
        // 处理日期变更
        async function handleDateChange(event) {
            const selectedDate = event.target.value;
            if (!selectedDate) return;

            console.log(`📅 用户选择日期: ${selectedDate}`);

            // 如果有现有的日期分页管理器，使用它
            if (app.datePaginationManager && app.datePaginationManager.switchToDate) {
                app.datePaginationManager.switchToDate(selectedDate);
                app.currentDate = selectedDate;
                updateDateInfo(selectedDate);
            } else {
                // 否则使用新的数据加载方式
                await loadDateData(selectedDate);
            }
        }

        // 加载指定日期数据（新架构）
        async function loadDateData(date) {
            try {
                updateStatus('正在加载数据...', 'loading');

                console.log(`📊 加载日期数据: ${date}`);

                let data = null;

                // 尝试从新的数据加载器获取数据
                if (window.dataLoader) {
                    try {
                        data = await window.dataLoader.loadDateData(date);
                    } catch (e) {
                        console.warn('新数据加载器失败，尝试其他方式:', e);
                    }
                }

                // 降级到嵌入数据
                if (!data && window.EMBEDDED_DATA && window.EMBEDDED_DATA[date]) {
                    data = window.EMBEDDED_DATA[date];
                    console.log('✅ 从嵌入数据加载');
                }

                if (!data) {
                    throw new Error(`未找到日期 ${date} 的数据`);
                }

                // 更新应用状态
                app.currentDate = date;
                app.currentData = data;

                // 更新状态信息
                updateStatus('数据加载完成', 'success');
                updateRecordCount(data.length);
                updateTime();
                updateDateInfo(date);

                console.log(`✅ 数据加载完成: ${date} (${data.length} 条记录)`);

            } catch (error) {
                console.error('❌ 数据加载失败:', error);
                showError('数据加载失败: ' + error.message);
                updateStatus('数据加载失败', 'error');
            }
        }

        // 更新日期信息
        function updateDateInfo(date) {
            const recordCount = app.currentData ? app.currentData.length : 0;
            updateRecordCount(recordCount);
            updateTime();

            // 更新当前日期显示
            const currentDateEl = document.getElementById('currentDateInfo');
            if (currentDateEl) {
                currentDateEl.textContent = `当前: ${date} (${formatDateChinese(date)})`;
            }

            // 更新页面标题
            document.title = `分页数据分析报告 - ${date}`;
        }
        
        // 其他辅助函数
        function showLoading() {
            document.getElementById('loadingIndicator').classList.remove('hidden');
            document.getElementById('errorIndicator').classList.add('hidden');
            document.getElementById('mainContent').classList.add('hidden');
        }

        function showError(message) {
            const errorEl = document.getElementById('errorIndicator');
            errorEl.querySelector('div').textContent = message;
            errorEl.classList.remove('hidden');
            document.getElementById('loadingIndicator').classList.add('hidden');
            document.getElementById('mainContent').classList.add('hidden');
        }

        function showMainContent() {
            document.getElementById('loadingIndicator').classList.add('hidden');
            document.getElementById('errorIndicator').classList.add('hidden');
            document.getElementById('mainContent').classList.remove('hidden');
        }
        
        function updateStatus(text, type) {
            document.getElementById('statusText').textContent = text;
        }
        
        function updateRecordCount(count) {
            document.getElementById('recordCount').textContent = count.toLocaleString();
        }
        
        function updateTime() {
            const now = new Date();
            document.getElementById('updateTime').textContent = now.toLocaleTimeString();
        }
        
        // 处理刷新
        async function handleRefresh() {
            try {
                updateStatus('正在刷新...', 'loading');

                // 刷新数据索引
                if (window.dataLoader && window.dataLoader.refreshIndex) {
                    await window.dataLoader.refreshIndex();
                }

                // 重新填充日期选择器
                await populateDateSelector();

                // 如果有当前日期，重新加载
                if (app.currentDate) {
                    const selector = document.getElementById('dateSelector');
                    selector.value = app.currentDate;
                    await handleDateChange({ target: selector });
                }

                updateStatus('刷新完成', 'success');
                console.log('✅ 数据刷新完成');

            } catch (error) {
                console.error('❌ 刷新失败:', error);
                updateStatus('刷新失败', 'error');
            }
        }

        // 处理筛选
        function handleFilter() {
            // 检查是否有现有的筛选功能
            if (window.showFilterPanel) {
                window.showFilterPanel();
            } else if (window.toggleFilter) {
                window.toggleFilter();
            } else {
                // 显示简单的筛选提示
                const filterContainer = document.getElementById('filterContainer');
                if (filterContainer) {
                    filterContainer.innerHTML = `
                        <div style="padding: 20px; background: #f8f9fa; border-radius: 8px; margin-top: 20px;">
                            <h4>🔍 高级筛选功能</h4>
                            <p>筛选功能将在集成现有JS模块后可用</p>
                            <p>支持的筛选条件：涨跌幅、成交量、市值等</p>
                            <button onclick="this.parentElement.style.display='none'" style="margin-top: 10px; padding: 5px 10px;">关闭</button>
                        </div>
                    `;
                }
            }
        }

        // 处理键盘快捷键
        function handleKeyboard(event) {
            // Ctrl+F: 筛选
            if (event.ctrlKey && event.key === 'f') {
                event.preventDefault();
                handleFilter();
            }

            // PageUp/PageDown: 切换日期
            if (event.key === 'PageUp' || event.key === 'PageDown') {
                event.preventDefault();
                switchDate(event.key === 'PageUp' ? -1 : 1);
            }

            // Escape: 关闭筛选面板
            if (event.key === 'Escape') {
                const filterContainer = document.getElementById('filterContainer');
                if (filterContainer) {
                    filterContainer.innerHTML = '';
                }
            }
        }

        // 切换日期（键盘导航）
        function switchDate(direction) {
            const selector = document.getElementById('dateSelector');
            const options = Array.from(selector.options);
            const currentIndex = options.findIndex(opt => opt.value === app.currentDate);

            if (currentIndex >= 0) {
                const newIndex = currentIndex + direction;
                if (newIndex >= 0 && newIndex < options.length) {
                    selector.selectedIndex = newIndex;
                    handleDateChange({ target: selector });
                }
            }
        }

        // 加载默认日期
        async function loadDefaultDate() {
            const selector = document.getElementById('dateSelector');
            if (selector.options.length > 0 && selector.value) {
                console.log(`📅 加载默认日期: ${selector.value}`);
                await handleDateChange({ target: selector });
            } else {
                console.log('⚠️ 没有可用的日期数据');
            }
        }
        
        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePage);
        } else {
            initializePage();
        }
    </script>
</body>
</html>
