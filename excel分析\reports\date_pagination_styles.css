/**
 * date_pagination_styles.css
 * 通达信复盘系统日期分组和分页功能的样式文件
 */

/* 日期导航样式 */
.date-navigation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.date-nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.date-nav-header h3 {
    margin: 0;
    font-size: 1.4em;
    font-weight: 600;
    color: white;
    border: none;
}

.current-date-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1em;
}

.current-date-label {
    opacity: 0.9;
}

.current-date-value {
    font-weight: bold;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-family: 'Courier New', monospace;
}

.current-date-count {
    opacity: 0.8;
    font-size: 0.9em;
}

.date-nav-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.date-nav-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.date-nav-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.date-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.date-selector {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #333;
    padding: 10px 15px;
    border-radius: 25px;
    font-size: 14px;
    min-width: 200px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.date-selector:focus {
    outline: none;
    background: white;
    border-color: #4A90E2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

/* 分页容器样式 */
.pagination-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e8ed;
}

.pagination-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.page-size-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-size-control label {
    font-weight: 500;
    color: #555;
}

.page-size-selector {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.page-size-selector:focus {
    outline: none;
    border-color: #4A90E2;
}

.pagination-stats {
    display: flex;
    gap: 20px;
    color: #666;
    font-size: 14px;
}

.current-page-info {
    font-weight: 600;
    color: #4A90E2;
}

.total-records-info {
    color: #888;
}

/* 分页控件样式 */
.pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.page-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    min-width: 44px;
    height: 36px;
}

.page-btn:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
}

.page-btn:disabled {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.page-numbers {
    display: flex;
    gap: 5px;
    align-items: center;
}

.page-number {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    min-width: 36px;
    height: 36px;
}

.page-number:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.page-number.active {
    background: #4A90E2;
    border-color: #4A90E2;
    color: white;
    font-weight: 600;
}

.page-ellipsis {
    padding: 8px 4px;
    color: #6c757d;
    font-size: 14px;
}

.page-jump {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 20px;
    color: #666;
    font-size: 14px;
}

.page-jump-input {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
}

.page-jump-input:focus {
    outline: none;
    border-color: #4A90E2;
}

.page-jump-btn {
    background: #4A90E2;
    border: none;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.page-jump-btn:hover {
    background: #357abd;
}

/* 加载遮罩样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 6px;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4A90E2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .date-nav-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .date-nav-controls {
        flex-direction: column;
        width: 100%;
    }
    
    .date-selector {
        width: 100%;
    }
    
    .pagination-info {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .pagination-controls {
        justify-content: flex-start;
    }
    
    .page-jump {
        margin-left: 0;
        margin-top: 10px;
    }
}

@media (max-width: 480px) {
    .date-navigation,
    .pagination-container {
        padding: 15px;
        margin: 15px 0;
    }
    
    .page-numbers {
        flex-wrap: wrap;
    }
    
    .pagination-controls {
        flex-direction: column;
        gap: 15px;
    }
}
