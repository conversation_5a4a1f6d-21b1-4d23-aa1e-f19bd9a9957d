<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据加载测试</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .loading { text-align: center; padding: 20px; }
    </style>
</head>
<body>
    <h1>🧪 通达信数据加载测试</h1>
    
    <div id="status" class="status info">正在初始化...</div>
    
    <div id="indexInfo"></div>
    <div id="dataInfo"></div>
    <div id="tableContainer"></div>
    
    <script>
        async function testDataLoading() {
            const statusEl = document.getElementById('status');
            const indexInfoEl = document.getElementById('indexInfo');
            const dataInfoEl = document.getElementById('dataInfo');
            const tableEl = document.getElementById('tableContainer');
            
            try {
                // 1. 测试索引文件加载
                statusEl.textContent = '正在加载索引文件...';
                statusEl.className = 'status info';

                console.log('尝试加载索引文件: data/index.json');
                const indexResponse = await fetch('data/index.json');
                console.log('索引文件响应状态:', indexResponse.status);

                if (!indexResponse.ok) {
                    throw new Error(`索引文件加载失败: ${indexResponse.status} - ${indexResponse.statusText}`);
                }
                
                const indexData = await indexResponse.json();
                console.log('索引数据:', indexData);
                
                indexInfoEl.innerHTML = `
                    <div class="status success">
                        <h3>✅ 索引文件加载成功</h3>
                        <p><strong>可用日期:</strong> ${indexData.available_dates.join(', ')}</p>
                        <p><strong>总日期数:</strong> ${indexData.total_dates}</p>
                        <p><strong>最后更新:</strong> ${indexData.last_updated}</p>
                    </div>
                `;
                
                // 2. 测试数据文件加载
                if (indexData.available_dates.length > 0) {
                    const testDate = indexData.available_dates[0];
                    statusEl.textContent = `正在加载日期数据: ${testDate}...`;
                    
                    const dataResponse = await fetch(`data/date_${testDate}.json`);
                    if (!dataResponse.ok) {
                        throw new Error(`数据文件加载失败: ${dataResponse.status}`);
                    }
                    
                    const data = await dataResponse.json();
                    console.log('数据样本:', data[0]);
                    
                    dataInfoEl.innerHTML = `
                        <div class="status success">
                            <h3>✅ 数据文件加载成功</h3>
                            <p><strong>测试日期:</strong> ${testDate}</p>
                            <p><strong>数据条数:</strong> ${data.length}</p>
                            <p><strong>字段数量:</strong> ${Object.keys(data[0] || {}).length}</p>
                            <p><strong>主要字段:</strong> ${Object.keys(data[0] || {}).slice(0, 10).join(', ')}</p>
                        </div>
                    `;
                    
                    // 3. 显示数据表格
                    if (data.length > 0) {
                        const headers = Object.keys(data[0]);
                        const displayHeaders = headers.slice(0, 8); // 只显示前8列
                        
                        let tableHTML = `
                            <h3>📊 数据预览 (前5行，前8列)</h3>
                            <table>
                                <thead>
                                    <tr>${displayHeaders.map(h => `<th>${h}</th>`).join('')}</tr>
                                </thead>
                                <tbody>
                        `;
                        
                        for (let i = 0; i < Math.min(5, data.length); i++) {
                            const row = data[i];
                            tableHTML += '<tr>';
                            for (const header of displayHeaders) {
                                const value = row[header] || '';
                                tableHTML += `<td>${value}</td>`;
                            }
                            tableHTML += '</tr>';
                        }
                        
                        tableHTML += '</tbody></table>';
                        tableEl.innerHTML = tableHTML;
                    }
                }
                
                statusEl.innerHTML = '<strong>🎉 所有测试通过！数据加载系统工作正常</strong>';
                statusEl.className = 'status success';
                
            } catch (error) {
                console.error('测试失败:', error);
                statusEl.innerHTML = `<strong>❌ 测试失败:</strong> ${error.message}`;
                statusEl.className = 'status error';
            }
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', testDataLoading);
    </script>
</body>
</html>
