/**
 * kline_chart_helper.js (High-Performance Version)
 * Manages the K-line chart modal and its interactions.
 * Works in tandem with table_sorter.js and date_pagination_manager.js.
 */

// --- Global State and Helpers ---

let globalKlineManager = null;
let globalHighlightedIndex = -1;

function extractStockDataFromRow(row) {
    if (!row) return null;
    try {
        const headerCells = Array.from(document.querySelectorAll('table.dataframe thead th'));
        const cells = row.cells;
        
        const getCellIndex = (name) => headerCells.findIndex(th => th.textContent.trim() === name);

        const codeIndex = getCellIndex('代码');
        const nameIndex = getCellIndex('名称');
        const dateIndex = getCellIndex('文件日期');

        const code = cells[codeIndex]?.textContent.trim();
        const name = cells[nameIndex]?.textContent.trim();
        const date = cells[dateIndex]?.textContent.trim();

        if (code && name && date) {
            return { code, name, date };
        }
        return null;
    } catch (error) {
        console.warn('Could not extract stock data from row', error);
        return null;
    }
}

// --- KlineChartManager Class ---

class KlineChartManager {
    constructor() {
        this.modal = null;
        this.chart = null;
        this.dataCache = new Map();
        this.isPositioned = false;
        this.createModal();
    }

    createModal() {
        const modalHTML = `
            <div id="kline-modal" style="display: none;">
                <div id="kline-modal-header">
                    <span id="kline-modal-title"></span>
                    <span id="kline-modal-close">&times;</span>
                </div>
                <div id="kline-chart-container"></div>
            </div>
        `;
        const modalCSS = `
            #kline-modal { position: fixed; z-index: 1001; background-color: #2c2c34; border: 1px solid #4a4a52; border-radius: 8px; box-shadow: 0 5px 20px rgba(0,0,0,0.4); width: 960px; min-height: 640px; display: none; flex-direction: column; color: #e0e0e0; }
            #kline-modal-header { padding: 10px 16px; cursor: move; background-color: #2c2c34; border-bottom: 1px solid #4a4a52; display: flex; justify-content: space-between; align-items: center; user-select: none; }
            #kline-modal-title { font-size: 1.1em; font-weight: bold; }
            #kline-modal-close { font-size: 24px; cursor: pointer; opacity: 0.7; transition: opacity 0.2s; }
            #kline-modal-close:hover { opacity: 1; }
            #kline-chart-container { width: 100%; flex: 1; }
        `;
        document.head.insertAdjacentHTML('beforeend', `<style>${modalCSS}</style>`);
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        this.modal = document.getElementById('kline-modal');
        this.chart = echarts.init(document.getElementById('kline-chart-container'), 'dark');

        this.makeDraggable();
        document.getElementById('kline-modal-close').addEventListener('click', () => this.hide());
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') this.hide();
        });
    }

    makeDraggable() {
        const header = this.modal.querySelector('#kline-modal-header');
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        const dragMouseDown = (e) => {
            e.preventDefault();
            pos3 = e.clientX; pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;
        };
        const elementDrag = (e) => {
            e.preventDefault();
            pos1 = pos3 - e.clientX; pos2 = pos4 - e.clientY;
            pos3 = e.clientX; pos4 = e.clientY;
            this.modal.style.top = (this.modal.offsetTop - pos2) + "px";
            this.modal.style.left = (this.modal.offsetLeft - pos1) + "px";
        };
        const closeDragElement = () => {
            document.onmouseup = null; document.onmousemove = null;
            localStorage.setItem('klineModalPosition', JSON.stringify({ top: this.modal.style.top, left: this.modal.style.left }));
        };
        header.onmousedown = dragMouseDown;
    }

    async show(stockCode, stockName, selectedDate) {
        document.getElementById('kline-modal-title').textContent = `${stockName} (${stockCode})`;
        this.modal.style.display = 'flex';
        if (this.chart) this.chart.resize();

        if (!this.isPositioned) {
            const lastPos = localStorage.getItem('klineModalPosition');
            if (lastPos) {
                const pos = JSON.parse(lastPos);
                this.modal.style.top = pos.top;
                this.modal.style.left = pos.left;
            } else {
                this.modal.style.left = `${(window.innerWidth - this.modal.offsetWidth) / 2}px`;
                this.modal.style.top = `${(window.innerHeight - this.modal.offsetHeight) / 2}px`;
            }
            this.isPositioned = true;
        }

        this.chart.showLoading();
        const data = await this.fetchKlineData(stockCode, selectedDate);
        this.chart.hideLoading();

        if (data) {
            this.renderChart(data, selectedDate);
        } else {
            this.chart.showLoading({ text: '获取K线数据失败', color: '#ff6b6b' });
        }
    }

    async showEmbedded(stockCode, stockName, selectedDate, containerId) {
        console.log(`🔍 [K线图] 嵌入式显示: ${stockName} (${stockCode}) 到容器 ${containerId}`);

        // 保存当前股票信息用于显示
        this.currentStockInfo = {
            code: stockCode,
            name: stockName,
            date: selectedDate
        };

        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`❌ 容器 ${containerId} 未找到`);
            return;
        }

        // 确保模态窗口隐藏
        if (this.modal) {
            this.modal.style.display = 'none';
        }

        // 初始化或重新初始化图表到指定容器
        if (this.chart) {
            this.chart.dispose();
        }
        this.chart = echarts.init(container);

        console.log(`🔍 [K线图] 图表已初始化到容器 ${containerId}`);

        this.chart.showLoading();
        const data = await this.fetchKlineData(stockCode, selectedDate);
        this.chart.hideLoading();

        if (data) {
            this.renderChart(data, selectedDate);
            console.log(`✅ [K线图] 嵌入式显示成功: ${stockName}`);
        } else {
            this.chart.showLoading({ text: '获取K线数据失败', color: '#ff6b6b' });
            console.error(`❌ [K线图] 数据获取失败: ${stockCode}`);
        }
    }

    hide() {
        if (this.modal) this.modal.style.display = 'none';
        if (this.chart) this.chart.clear();
    }

    async fetchKlineData(stockCode, selectedDate) {
        const cacheKey = `${stockCode}_${selectedDate}`;
        if (this.dataCache.has(cacheKey)) return this.dataCache.get(cacheKey);

        const market = stockCode.startsWith('6') ? '1' : '0';
        const secid = `${market}.${stockCode}`;
        const end = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        const url = `https://push2his.eastmoney.com/api/qt/stock/kline/get?secid=${secid}&fields1=f1,f2,f3,f4,f5,f6&fields2=f51,f52,f53,f54,f55,f56,f57,f58&klt=101&fqt=1&end=${end}&lmt=240`;

        try {
            const response = await fetch(url);
            const json = await response.json();
            if (json && json.data && json.data.klines) {
                const processed = this.processEastMoneyData(json.data);
                this.dataCache.set(cacheKey, processed);
                return processed;
            }
            return null;
        } catch (error) {
            console.error("Fetch K-line data failed:", error);
            return null;
        }
    }

    processEastMoneyData(data) {
        const dates = data.klines.map(item => item.split(',')[0]);
        const klineData = data.klines.map(item => {
            const parts = item.split(',');
            return [parseFloat(parts[1]), parseFloat(parts[2]), parseFloat(parts[4]), parseFloat(parts[3])]; // O, C, L, H
        });
        const volumes = data.klines.map(item => parseInt(item.split(',')[5]));
        return { dates, klineData, volumes };
    }

    renderChart(data, selectedDate) {
        const selectedIndex = data.dates.indexOf(selectedDate);
        if (selectedIndex === -1) return;

        const start = Math.max(0, selectedIndex - 30);
        const end = Math.min(data.dates.length - 1, selectedIndex + 30);

        // 从extractStockDataFromRow获取股票信息
        const stockInfo = this.currentStockInfo || { code: 'N/A', name: 'N/A', date: selectedDate };

        const option = {
            title: {
                text: `${stockInfo.name} (${stockInfo.code}) - ${selectedDate}`,
                left: 'center',
                top: '1%',
                textStyle: {
                    color: '#ffffff',
                    fontSize: 14,
                    fontWeight: 'normal'
                }
            },
            tooltip: { trigger: 'axis', axisPointer: { type: 'cross' } },
            grid: [
                {
                    left: '3%',    // 减少左边距，原默认约10%
                    right: '3%',   // 减少右边距，原默认约10%
                    top: '8%',     // 为标题留出空间
                    height: '57%'  // K线图区域高度，为标题调整
                },
                {
                    left: '3%',    // 成交量区域左边距与K线图保持一致
                    right: '3%',   // 成交量区域右边距与K线图保持一致
                    top: '75%',    // 成交量区域顶部位置
                    height: '15%'  // 成交量区域高度
                }
            ],
            xAxis: [
                { type: 'category', data: data.dates, axisLabel: { show: false } },
                { type: 'category', gridIndex: 1, data: data.dates }
            ],
            yAxis: [
                {
                    scale: true,
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(128, 128, 128, 0.3)',
                            type: 'dashed', // 虚线样式
                            width: 1
                        }
                    }
                },
                {
                    scale: true,
                    gridIndex: 1,
                    axisLabel: { show: false },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(128, 128, 128, 0.3)',
                            type: 'dashed', // 虚线样式
                            width: 1
                        }
                    }
                }
            ],
            dataZoom: [
                { type: 'inside', xAxisIndex: [0, 1], startValue: start, endValue: end },
                {
                    type: 'slider',
                    xAxisIndex: [0, 1],
                    top: '92%',
                    left: '3%',    // 与grid保持一致的左边距
                    right: '3%',   // 与grid保持一致的右边距
                    startValue: start,
                    endValue: end
                }
            ],
            series: [
                {
                    type: 'candlestick', data: data.klineData,
                    markPoint: {
                        data: [{
                            name: 'Selected', coord: [selectedIndex, data.klineData[selectedIndex][3]],
                            symbol: 'pin', symbolSize: 20, itemStyle: { color: '#FFD700' }
                        }]
                    }
                },
                {
                    name: 'Volume', type: 'bar', xAxisIndex: 1, yAxisIndex: 1, data: data.volumes,
                    itemStyle: {
                        color: ({ dataIndex }) => data.klineData[dataIndex][1] > data.klineData[dataIndex][0] ? '#ef4444' : '#22c55e'
                    }
                }
            ]
        };
        this.chart.setOption(option, true);
        console.log('📊 K线图布局优化已应用: left/right边距减少至3%，图表内容宽度提升至94%');
    }
}

// --- Event Binding Logic ---

function bindKlineChartEvents() {
    const tableBody = document.querySelector('table.dataframe tbody');
    if (!tableBody) return;

    const rows = tableBody.querySelectorAll('tr');
    console.log(`🔍 [事件绑定1] 开始重新绑定K线图事件`);

    console.log(`🔍 [事件绑定2] 当前globalHighlightedIndex: ${globalHighlightedIndex}`);

    // 使用更温和的方式清除事件：标记已绑定的行
    rows.forEach((row, index) => {
        // 移除旧的点击事件标记
        row.removeAttribute('data-click-bound');
    });

    console.log(`🔍 [事件绑定3] 找到${rows.length}行，准备绑定事件`);

    rows.forEach((row, index) => {
        // 只绑定未绑定过的行，避免重复绑定
        if (!row.getAttribute('data-click-bound')) {
            row.style.cursor = 'pointer';
            row.setAttribute('data-click-bound', 'true');
            console.log(`🔍 [事件绑定5] 绑定第${index}行事件，行内容: ${row.cells[1]?.textContent || 'N/A'}`);

            row.addEventListener('click', () => {
                // 实时计算点击行的实际索引，避免排序后索引错乱
                const currentRows = document.querySelectorAll('table.dataframe tbody tr');
                const actualIndex = Array.from(currentRows).indexOf(row);
                console.log(`🔍 [鼠标点击] 点击行，实际索引${actualIndex}`);

                setActiveRow(actualIndex);
                const stockData = extractStockDataFromRow(row);
                if (stockData) {
                    // 使用布局管理器显示嵌入式K线图
                    if (window.layoutManager && typeof window.layoutManager.showKlineChart === 'function') {
                        window.layoutManager.showKlineChart(stockData.code, stockData.name, stockData.date);
                    } else {
                        console.warn('⚠️ 布局管理器未找到，使用模态显示');
                        globalKlineManager.show(stockData.code, stockData.name, stockData.date);
                    }
                }
            });
        } else {
            console.log(`🔍 [事件绑定] 跳过已绑定的行${index}`);
        }
    });

    console.log(`🔍 [事件绑定6] 事件绑定完成，当前globalHighlightedIndex: ${globalHighlightedIndex}`);

    // 防止重复绑定键盘事件
    if (!window.klineKeyboardEventsBound) {
        document.addEventListener('keydown', (e) => {
        const currentRows = document.querySelectorAll('table.dataframe tbody tr');
        if (currentRows.length === 0) return;

        let newIndex = globalHighlightedIndex;
        if (e.key === 'ArrowDown') {
            newIndex = globalHighlightedIndex < currentRows.length - 1 ? globalHighlightedIndex + 1 : 0;
        } else if (e.key === 'ArrowUp') {
            newIndex = globalHighlightedIndex > 0 ? globalHighlightedIndex - 1 : currentRows.length - 1;
        }

        if (newIndex !== globalHighlightedIndex) {
            e.preventDefault();
            setActiveRow(newIndex);
            const stockData = extractStockDataFromRow(currentRows[newIndex]);
            if (stockData) {
                // 使用布局管理器显示嵌入式K线图
                if (window.layoutManager && typeof window.layoutManager.showKlineChart === 'function') {
                    window.layoutManager.showKlineChart(stockData.code, stockData.name, stockData.date);
                } else {
                    console.warn('⚠️ 布局管理器未找到，使用模态显示');
                    globalKlineManager.show(stockData.code, stockData.name, stockData.date);
                }
            }
        }
        });
        window.klineKeyboardEventsBound = true;
        console.log(`🔍 [事件绑定6] 键盘事件已绑定`);
    } else {
        console.log(`⚠️ K线图键盘事件已存在，跳过重复绑定`);
    }
}

function setActiveRow(index) {
    const rows = document.querySelectorAll('table.dataframe tbody tr');
    rows.forEach((row, i) => {
        row.style.backgroundColor = i === index ? '#f0f8ff' : '';
    });
    globalHighlightedIndex = index;
    if (rows[index]) {
        // 检查行是否在可视区域内，只有在需要时才滚动
        const rect = rows[index].getBoundingClientRect();
        const tableContainer = document.querySelector('.table-wrapper');
        const containerRect = tableContainer ? tableContainer.getBoundingClientRect() : null;

        if (containerRect) {
            const isVisible = rect.top >= containerRect.top && rect.bottom <= containerRect.bottom;

            if (!isVisible) {
                // 只有当行不在可视区域时才滚动，使用最小滚动
                rows[index].scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',  // 使用nearest而不是center，减少滚动幅度
                    inline: 'nearest'
                });
                console.log(`🔍 [滚动] 行${index}不在可视区域，执行最小滚动`);
            } else {
                console.log(`🔍 [滚动] 行${index}已在可视区域，跳过滚动`);
            }
        } else {
            // 备用方案：使用最小滚动
            rows[index].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }
}

// --- Initialization ---

function initializeKlineChart() {
    if (typeof echarts === 'undefined') {
        console.error("ECharts library not loaded.");
        return;
    }
    if (!globalKlineManager) {
        globalKlineManager = new KlineChartManager();
    }
    bindKlineChartEvents();
}

/**
 * @description This is the single entry point to re-initialize all interactive features after the DOM is updated.
 * It should be called by date_pagination_manager.js after rendering new table data.
 */
window.rebindAllFeatures = function() {
    console.log("🔄 Rebiding all interactive features...");
    console.log(`🔍 [重绑定1] 重绑定前globalHighlightedIndex: ${globalHighlightedIndex}`);

    // 1. Rebind K-line chart events
    if (globalKlineManager) {
        // 保存当前焦点状态，避免重绑定时丢失
        const savedIndex = globalHighlightedIndex;
        console.log(`🔍 [重绑定2] 保存当前焦点索引: ${savedIndex}`);

        bindKlineChartEvents();

        // 恢复焦点状态
        if (savedIndex >= 0) {
            globalHighlightedIndex = savedIndex;
            console.log(`🔍 [重绑定3] 恢复焦点索引: ${savedIndex}`);

            // 重新应用高亮样式
            const rows = document.querySelectorAll('table.dataframe tbody tr');
            if (rows[savedIndex]) {
                rows.forEach((row, i) => {
                    row.style.backgroundColor = i === savedIndex ? '#f0f8ff' : '';
                });
                console.log(`🔍 [重绑定4] 重新应用高亮样式到行${savedIndex}`);
            }
        }
        console.log("    ✅ K-line chart events rebound.");
    } else {
        initializeKlineChart();
        console.log("    ✅ K-line chart initialized and events bound.");
    }

    // 2. Rebind table sorter
    const table = document.querySelector('table.dataframe');
    if (table && typeof makeTableSortable === 'function') {
        makeTableSortable(table);
        console.log("    ✅ Table sorter rebound.");
    }
};

document.addEventListener('DOMContentLoaded', () => {
    // Initial binding is now handled by rebindAllFeatures, 
    // which is called by the date manager after the first render.
});
