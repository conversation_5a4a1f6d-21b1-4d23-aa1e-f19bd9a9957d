/**
 * 数据加载器 - 按需加载分离的数据文件
 * 保持与现有JS模块的完全兼容性
 * 
 * 功能：
 * 1. 按需加载日期数据
 * 2. 缓存机制
 * 3. 兼容现有的 window.EMBEDDED_DATA 格式
 * 4. 支持增量更新
 * 
 * @version 1.0.0
 * @date 2025-01-23
 */

class DataLoader {
    constructor() {
        this.cache = new Map();
        this.indexData = null;
        this.isLoading = false;
        this.loadingPromises = new Map();
        
        // 兼容性：模拟原有的 EMBEDDED_DATA 结构
        window.EMBEDDED_DATA = {};
        
        console.log('📊 数据加载器初始化完成');
    }
    
    /**
     * 初始化 - 加载数据索引
     */
    async initialize() {
        try {
            console.log('🔄 正在加载数据索引...');
            const response = await fetch('data/index.json');
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            this.indexData = await response.json();
            console.log(`✅ 数据索引加载完成，共 ${this.indexData.total_dates} 个日期`);
            console.log(`📅 可用日期: ${this.indexData.available_dates.join(', ')}`);
            
            return this.indexData;
        } catch (error) {
            console.error('❌ 数据索引加载失败:', error);
            
            // 降级方案：尝试从现有的 EMBEDDED_DATA 获取
            if (window.EMBEDDED_DATA && Object.keys(window.EMBEDDED_DATA).length > 0) {
                console.log('🔄 使用嵌入数据作为降级方案');
                this.indexData = {
                    available_dates: Object.keys(window.EMBEDDED_DATA).sort(),
                    total_dates: Object.keys(window.EMBEDDED_DATA).length,
                    last_updated: new Date().toISOString()
                };
                return this.indexData;
            }
            
            throw error;
        }
    }
    
    /**
     * 获取可用日期列表
     */
    getAvailableDates() {
        if (!this.indexData) {
            console.warn('⚠️ 数据索引未加载，请先调用 initialize()');
            return [];
        }
        return this.indexData.available_dates || [];
    }
    
    /**
     * 加载指定日期的数据
     * @param {string} date - 日期字符串 (YYYY-MM-DD)
     * @returns {Promise<Array>} 数据数组
     */
    async loadDateData(date) {
        // 检查缓存
        if (this.cache.has(date)) {
            console.log(`📋 从缓存加载日期数据: ${date}`);
            return this.cache.get(date);
        }
        
        // 检查是否正在加载
        if (this.loadingPromises.has(date)) {
            console.log(`⏳ 等待日期数据加载完成: ${date}`);
            return this.loadingPromises.get(date);
        }
        
        // 开始加载
        const loadPromise = this._loadDateDataInternal(date);
        this.loadingPromises.set(date, loadPromise);
        
        try {
            const data = await loadPromise;
            this.loadingPromises.delete(date);
            return data;
        } catch (error) {
            this.loadingPromises.delete(date);
            throw error;
        }
    }
    
    /**
     * 内部加载方法
     * @private
     */
    async _loadDateDataInternal(date) {
        try {
            console.log(`🔄 正在加载日期数据: ${date}`);
            
            // 首先检查是否在嵌入数据中（降级兼容）
            if (window.EMBEDDED_DATA && window.EMBEDDED_DATA[date]) {
                console.log(`📋 从嵌入数据加载: ${date}`);
                const data = window.EMBEDDED_DATA[date];
                this.cache.set(date, data);
                return data;
            }
            
            // 从独立文件加载
            const response = await fetch(`data/date_${date}.json`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // 缓存数据
            this.cache.set(date, data);
            
            // 同时更新 EMBEDDED_DATA 以保持兼容性
            window.EMBEDDED_DATA[date] = data;
            
            console.log(`✅ 日期数据加载完成: ${date} (${data.length} 条记录)`);
            return data;
            
        } catch (error) {
            console.error(`❌ 加载日期数据失败: ${date}`, error);
            throw error;
        }
    }
    
    /**
     * 预加载多个日期的数据
     * @param {Array<string>} dates - 日期数组
     */
    async preloadDates(dates) {
        console.log(`🚀 开始预加载 ${dates.length} 个日期的数据...`);
        
        const promises = dates.map(date => this.loadDateData(date).catch(error => {
            console.warn(`⚠️ 预加载失败: ${date}`, error);
            return null;
        }));
        
        const results = await Promise.all(promises);
        const successCount = results.filter(result => result !== null).length;
        
        console.log(`✅ 预加载完成: ${successCount}/${dates.length} 个日期`);
        return results;
    }
    
    /**
     * 清除缓存
     * @param {string} date - 可选，指定日期。不提供则清除所有缓存
     */
    clearCache(date = null) {
        if (date) {
            this.cache.delete(date);
            console.log(`🗑️ 已清除日期缓存: ${date}`);
        } else {
            this.cache.clear();
            console.log('🗑️ 已清除所有缓存');
        }
    }
    
    /**
     * 获取缓存状态
     */
    getCacheStatus() {
        return {
            cachedDates: Array.from(this.cache.keys()),
            cacheSize: this.cache.size,
            totalAvailable: this.indexData ? this.indexData.total_dates : 0
        };
    }
    
    /**
     * 刷新数据索引（用于检测新增数据）
     */
    async refreshIndex() {
        console.log('🔄 刷新数据索引...');
        this.indexData = null;
        return this.initialize();
    }
}

// 创建全局数据加载器实例
window.dataLoader = new DataLoader();

/**
 * 兼容性函数：确保现有代码正常工作
 */
window.ensureDataLoaded = async function(date) {
    if (!window.dataLoader.indexData) {
        await window.dataLoader.initialize();
    }
    
    if (date) {
        return window.dataLoader.loadDateData(date);
    }
    
    return true;
};

/**
 * 初始化数据加载器
 * 这个函数会在页面加载时自动调用
 */
window.initializeDataLoader = async function() {
    try {
        await window.dataLoader.initialize();
        
        // 预加载最新的几个日期
        const availableDates = window.dataLoader.getAvailableDates();
        if (availableDates.length > 0) {
            // 预加载最新的3个日期
            const recentDates = availableDates.slice(-3);
            await window.dataLoader.preloadDates(recentDates);
        }
        
        console.log('🎉 数据加载器初始化完成');
        return true;
    } catch (error) {
        console.error('❌ 数据加载器初始化失败:', error);
        return false;
    }
};

// 页面加载完成后自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', window.initializeDataLoader);
} else {
    window.initializeDataLoader();
}

console.log('📦 数据加载器模块已加载');
