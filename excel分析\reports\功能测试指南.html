<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试指南</title>
    <style>
        body { font-family: 'Microsoft YaHei', <PERSON>l, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .feature-section { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #3498db; }
        .test-item { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border: 1px solid #e9ecef; }
        .test-step { margin: 5px 0; padding: 8px; background: #e3f2fd; border-radius: 4px; }
        .expected { color: #28a745; font-weight: bold; }
        .shortcut { background: #333; color: white; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
        .btn { padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #2980b9; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 通达信复盘系统功能测试指南</h1>
        
        <div class="status info">
            <strong>📋 测试目标：</strong>验证增强版精简页面的所有交互功能是否正常工作
        </div>
        
        <div class="status warning">
            <strong>⚠️ 测试前准备：</strong>确保HTTP服务器正在运行，并且已经打开了增强版精简页面
        </div>
        
        <a href="复盘分析_精简版.html" class="btn" target="_blank">🚀 打开增强版精简页面</a>
        <a href="debug_simple.html" class="btn" target="_blank">🔧 打开调试页面</a>
        
        <h2>🎯 核心功能测试清单</h2>
        
        <div class="feature-section">
            <h3>1. 📊 表格排序功能</h3>
            <div class="test-item">
                <strong>测试步骤：</strong>
                <div class="test-step">1. 点击任意表头（如"涨幅%"、"收盘"等）</div>
                <div class="test-step">2. 观察表格数据是否按该列重新排序</div>
                <div class="test-step">3. 再次点击同一表头，观察是否切换排序方向</div>
                <div class="test-step">4. 检查表头是否显示排序指示器（▲ 或 ▼）</div>
                <div class="expected">✅ 预期结果：表格数据正确排序，表头显示排序指示器</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>2. ⌨️ 键盘导航功能</h3>
            <div class="test-item">
                <strong>方向键导航：</strong>
                <div class="test-step">1. 按 <span class="shortcut">↓</span> 键选择第一行</div>
                <div class="test-step">2. 继续按 <span class="shortcut">↓</span> 键向下移动</div>
                <div class="test-step">3. 按 <span class="shortcut">↑</span> 键向上移动</div>
                <div class="expected">✅ 预期结果：高亮行正确移动，自动滚动到可见区域</div>
            </div>
            
            <div class="test-item">
                <strong>日期切换：</strong>
                <div class="test-step">1. 按 <span class="shortcut">PageUp</span> 切换到上一个日期</div>
                <div class="test-step">2. 按 <span class="shortcut">PageDown</span> 切换到下一个日期</div>
                <div class="expected">✅ 预期结果：日期选择器更新，表格数据重新加载</div>
            </div>
            
            <div class="test-item">
                <strong>K线图快捷键：</strong>
                <div class="test-step">1. 选择任意一行（用方向键）</div>
                <div class="test-step">2. 按 <span class="shortcut">Enter</span> 或 <span class="shortcut">Space</span> 键</div>
                <div class="test-step">3. 按 <span class="shortcut">Escape</span> 键关闭K线图</div>
                <div class="expected">✅ 预期结果：K线图模态框正确打开和关闭</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>3. 🖱️ 鼠标交互功能</h3>
            <div class="test-item">
                <strong>行点击和悬停：</strong>
                <div class="test-step">1. 鼠标悬停在表格行上，观察悬停效果</div>
                <div class="test-step">2. 点击任意表格行</div>
                <div class="test-step">3. 观察行是否高亮显示</div>
                <div class="test-step">4. 观察是否自动显示K线图</div>
                <div class="expected">✅ 预期结果：悬停效果正常，点击后行高亮，K线图显示</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>4. 📈 K线图功能</h3>
            <div class="test-item">
                <strong>K线图显示：</strong>
                <div class="test-step">1. 点击任意股票行或使用键盘选择后按Enter</div>
                <div class="test-step">2. 观察K线图模态框是否正确打开</div>
                <div class="test-step">3. 检查股票代码和名称是否正确显示</div>
                <div class="test-step">4. 点击关闭按钮或按Escape键关闭</div>
                <div class="expected">✅ 预期结果：K线图正确显示股票信息，可以正常关闭</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>5. 📅 日期切换功能</h3>
            <div class="test-item">
                <strong>日期选择器：</strong>
                <div class="test-step">1. 点击页面顶部的日期选择器</div>
                <div class="test-step">2. 选择不同的日期（2025-07-01, 07-02, 07-03）</div>
                <div class="test-step">3. 观察表格数据是否正确更新</div>
                <div class="test-step">4. 检查状态栏中的数据量和当前日期是否更新</div>
                <div class="expected">✅ 预期结果：数据正确切换，状态信息正确更新</div>
            </div>
        </div>
        
        <h2>🔍 性能测试</h2>
        
        <div class="feature-section">
            <h3>6. ⚡ 加载性能</h3>
            <div class="test-item">
                <strong>页面加载速度：</strong>
                <div class="test-step">1. 刷新页面，观察加载时间</div>
                <div class="test-step">2. 切换日期，观察响应速度</div>
                <div class="test-step">3. 排序表格，观察处理速度</div>
                <div class="expected">✅ 预期结果：页面加载 < 5秒，交互响应 < 1秒</div>
            </div>
        </div>
        
        <h2>📝 测试结果记录</h2>
        
        <div class="status success">
            <strong>✅ 已通过的功能：</strong>
            <ul id="passedTests">
                <li>基础数据加载和显示</li>
                <li>日期选择器功能</li>
                <!-- 测试时添加更多项目 -->
            </ul>
        </div>
        
        <div class="status warning">
            <strong>⚠️ 需要修复的问题：</strong>
            <ul id="failedTests">
                <!-- 测试时发现的问题会添加到这里 -->
            </ul>
        </div>
        
        <h2>🛠️ 故障排除</h2>
        
        <div class="feature-section">
            <h3>常见问题解决方案</h3>
            <div class="test-item">
                <strong>如果表格排序不工作：</strong>
                <div class="test-step">1. 检查浏览器控制台是否有JavaScript错误</div>
                <div class="test-step">2. 确认table_sorter.js已正确加载</div>
                <div class="test-step">3. 检查表格DOM结构是否正确</div>
            </div>
            
            <div class="test-item">
                <strong>如果键盘导航不工作：</strong>
                <div class="test-step">1. 确保页面获得了焦点（点击页面任意位置）</div>
                <div class="test-step">2. 检查键盘事件监听器是否正确绑定</div>
                <div class="test-step">3. 查看控制台日志确认键盘事件是否被捕获</div>
            </div>
            
            <div class="test-item">
                <strong>如果K线图不显示：</strong>
                <div class="test-step">1. 检查kline_chart_helper.js是否正确加载</div>
                <div class="test-step">2. 确认ECharts库是否可用</div>
                <div class="test-step">3. 检查股票数据格式是否正确</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="复盘分析_精简版.html" class="btn">🔄 重新开始测试</a>
            <a href="system_status.html" class="btn">📊 查看系统状态</a>
        </div>
    </div>
    
    <script>
        // 简单的测试结果记录功能
        function addTestResult(testName, passed) {
            const listId = passed ? 'passedTests' : 'failedTests';
            const list = document.getElementById(listId);
            const item = document.createElement('li');
            item.textContent = testName;
            list.appendChild(item);
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 功能测试指南已加载，请按照指南进行测试');
        });
    </script>
</body>
</html>
