@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    通达信复盘系统验证工具
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 系统环境检查...
echo.

REM 检查Python环境
echo [1/6] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python环境未找到
    echo    请安装Python 3.7+并添加到PATH环境变量
    goto :error
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo ✅ Python版本: %%i
)

REM 检查依赖包
echo.
echo [2/6] 检查Python依赖包...
python -c "import pandas, openpyxl, xlrd, chardet" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少依赖包，尝试自动安装...
    pip install -r requirements.txt >nul 2>&1
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        goto :error
    ) else (
        echo ✅ 依赖包安装成功
    )
) else (
    echo ✅ 依赖包检查通过
)

REM 检查数据目录
echo.
echo [3/6] 检查数据目录...
if exist "..\复盘数据" (
    echo ✅ 数据目录存在: ..\复盘数据
    dir "..\复盘数据\*.xls*" >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  数据目录为空，请添加通达信导出的Excel文件
    ) else (
        for /f %%i in ('dir "..\复盘数据\*.xls*" /b 2^>nul ^| find /c /v ""') do echo ✅ 发现 %%i 个数据文件
    )
) else (
    echo ⚠️  数据目录不存在，将创建: ..\复盘数据
    mkdir "..\复盘数据" >nul 2>&1
)

REM 检查输出目录
echo.
echo [4/6] 检查输出目录...
if exist "reports" (
    echo ✅ 输出目录存在: reports
) else (
    echo ⚠️  输出目录不存在，将创建: reports
    mkdir "reports" >nul 2>&1
)

if exist "reports\data" (
    echo ✅ 数据输出目录存在: reports\data
    if exist "reports\data\index.json" (
        echo ✅ 发现数据索引文件
    ) else (
        echo ⚠️  未发现数据索引文件，需要运行数据处理器
    )
) else (
    echo ⚠️  数据输出目录不存在，需要运行数据处理器
)

REM 检查前端文件
echo.
echo [5/6] 检查前端文件...
set "missing_files="

if not exist "reports\复盘分析.html" (
    set "missing_files=%missing_files% 复盘分析.html"
)
if not exist "reports\data_loader.js" (
    set "missing_files=%missing_files% data_loader.js"
)
if not exist "reports\data_manager.js" (
    set "missing_files=%missing_files% data_manager.js"
)

if "%missing_files%"=="" (
    echo ✅ 前端文件检查通过
) else (
    echo ❌ 缺少前端文件:%missing_files%
    goto :error
)

REM 运行系统测试
echo.
echo [6/6] 运行系统测试...
if exist "test_system.py" (
    echo 🧪 运行Python系统测试...
    python test_system.py
    if errorlevel 1 (
        echo ❌ 系统测试失败
        goto :error
    ) else (
        echo ✅ 系统测试通过
    )
) else (
    echo ⚠️  测试脚本不存在，跳过系统测试
)

echo.
echo ========================================
echo ✅ 系统验证完成！
echo ========================================
echo.
echo 📋 验证结果摘要:
echo    ✅ Python环境正常
echo    ✅ 依赖包完整
echo    ✅ 目录结构正确
echo    ✅ 前端文件完整
echo    ✅ 系统功能正常
echo.
echo 🚀 下一步操作:
echo    1. 将通达信数据文件放入: ..\复盘数据\
echo    2. 运行: 一键处理数据.bat
echo    3. 打开: reports\复盘分析.html
echo.
echo 📖 详细说明请查看: docs\部署指南_2025-01-23.md
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ 系统验证失败！
echo ========================================
echo.
echo 🔧 故障排除建议:
echo    1. 检查Python是否正确安装
echo    2. 运行: pip install -r requirements.txt
echo    3. 确保所有文件完整
echo    4. 查看详细错误信息
echo.
echo 📞 如需帮助，请查看: docs\部署指南_2025-01-23.md
echo.

:end
echo 按任意键退出...
pause >nul
