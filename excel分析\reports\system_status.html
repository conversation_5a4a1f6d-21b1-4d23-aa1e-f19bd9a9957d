<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态检查</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 5px solid; }
        .success { background: #d4edda; color: #155724; border-color: #28a745; }
        .error { background: #f8d7da; color: #721c24; border-color: #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #17a2b8; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffc107; }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .check-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px; }
        .check-result { font-weight: bold; }
        .btn { padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #2980b9; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 通达信复盘系统状态检查</h1>
        
        <div id="overallStatus" class="status info">
            <strong>正在检查系统状态...</strong>
        </div>
        
        <h2>📋 系统检查项目</h2>
        <div id="checkResults"></div>
        
        <h2>📊 数据概览</h2>
        <div id="dataOverview"></div>
        
        <h2>🔧 操作面板</h2>
        <button class="btn" onclick="window.open('复盘分析.html', '_blank')">🚀 打开主页面</button>
        <button class="btn" onclick="window.open('test_data_loading.html', '_blank')">🧪 数据加载测试</button>
        <button class="btn" onclick="location.reload()">🔄 重新检查</button>
        
        <div id="detailInfo" style="margin-top: 20px;"></div>
    </div>

    <script>
        async function checkSystemStatus() {
            const checks = [
                { name: '服务器连接', test: checkServerConnection },
                { name: '数据索引文件', test: checkIndexFile },
                { name: '数据文件完整性', test: checkDataFiles },
                { name: 'JavaScript模块', test: checkJSModules },
                { name: '样式文件', test: checkStyleFiles }
            ];
            
            const resultsEl = document.getElementById('checkResults');
            const overallEl = document.getElementById('overallStatus');
            const dataEl = document.getElementById('dataOverview');
            
            let allPassed = true;
            let results = [];
            
            for (const check of checks) {
                try {
                    const result = await check.test();
                    results.push({ name: check.name, status: 'success', message: result });
                    
                    resultsEl.innerHTML += `
                        <div class="check-item">
                            <span>${check.name}</span>
                            <span class="check-result" style="color: #28a745;">✅ 通过</span>
                        </div>
                    `;
                } catch (error) {
                    allPassed = false;
                    results.push({ name: check.name, status: 'error', message: error.message });
                    
                    resultsEl.innerHTML += `
                        <div class="check-item">
                            <span>${check.name}</span>
                            <span class="check-result" style="color: #dc3545;">❌ 失败</span>
                        </div>
                    `;
                }
            }
            
            // 更新总体状态
            if (allPassed) {
                overallEl.className = 'status success';
                overallEl.innerHTML = '<strong>🎉 系统状态正常！所有检查项目都通过了。</strong>';
            } else {
                overallEl.className = 'status error';
                overallEl.innerHTML = '<strong>⚠️ 系统存在问题，请查看详细检查结果。</strong>';
            }
            
            // 显示数据概览
            try {
                const indexResponse = await fetch('data/index.json');
                const indexData = await indexResponse.json();
                
                dataEl.innerHTML = `
                    <div class="status success">
                        <h4>📈 数据统计</h4>
                        <p><strong>可用日期:</strong> ${indexData.available_dates.join(', ')}</p>
                        <p><strong>总日期数:</strong> ${indexData.total_dates}</p>
                        <p><strong>最后更新:</strong> ${new Date(indexData.last_updated).toLocaleString()}</p>
                    </div>
                `;
            } catch (error) {
                dataEl.innerHTML = `<div class="status error">数据概览加载失败: ${error.message}</div>`;
            }
        }
        
        async function checkServerConnection() {
            const response = await fetch(window.location.href);
            if (response.ok) {
                return `HTTP服务器正常运行 (${response.status})`;
            }
            throw new Error(`服务器响应异常: ${response.status}`);
        }
        
        async function checkIndexFile() {
            const response = await fetch('data/index.json');
            if (!response.ok) {
                throw new Error(`索引文件不存在或无法访问 (${response.status})`);
            }
            const data = await response.json();
            return `索引文件正常，包含 ${data.total_dates} 个日期`;
        }
        
        async function checkDataFiles() {
            const indexResponse = await fetch('data/index.json');
            const indexData = await indexResponse.json();
            
            let totalRecords = 0;
            for (const date of indexData.available_dates) {
                const dataResponse = await fetch(`data/date_${date}.json`);
                if (!dataResponse.ok) {
                    throw new Error(`日期文件 ${date} 无法访问`);
                }
                const data = await dataResponse.json();
                totalRecords += data.length;
            }
            
            return `所有数据文件正常，共 ${totalRecords} 条记录`;
        }
        
        async function checkJSModules() {
            const modules = [
                'data_loader.js',
                'data_manager.js', 
                'date_pagination_manager.js',
                'table_sorter.js',
                'kline_chart_helper.js'
            ];
            
            for (const module of modules) {
                const response = await fetch(module);
                if (!response.ok) {
                    throw new Error(`模块 ${module} 无法访问`);
                }
            }
            
            return `所有 ${modules.length} 个JS模块正常`;
        }
        
        async function checkStyleFiles() {
            const response = await fetch('date_pagination_styles.css');
            if (!response.ok) {
                throw new Error('样式文件无法访问');
            }
            return '样式文件正常';
        }
        
        // 页面加载完成后开始检查
        document.addEventListener('DOMContentLoaded', checkSystemStatus);
    </script>
</body>
</html>
