/**
 * table_sorter.js
 * 为HTML表格提供可点击的表头排序功能。
 * - 支持数字、字符串和百分比排序。
 * - **核心：排序后主动调用 window.rebindKlineChartEvents() 来确保K线图完全兼容。**
 */
function makeTableSortable(table) {
    const headers = table.querySelectorAll('th');
    let currentSort = { column: -1, direction: 'asc' };

    headers.forEach((header, index) => {
        // 为已有内容的表头添加排序能力
        if (header.textContent.trim().length > 0) {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                const direction = (index === currentSort.column && currentSort.direction === 'asc') ? 'desc' : 'asc';
                sortTable(table, index, direction);
                updateHeaderStyles(headers, index, direction);
                currentSort = { column: index, direction: direction };
            });
        }
    });
}

function sortTable(table, columnIndex, direction) {
    console.log(`🔍 [排查1] 开始排序 - 列${columnIndex}, 方向${direction}`);

    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    console.log(`🔍 [排查2] 排序前行数: ${rows.length}`);

    // 检查排序前第一行的数据
    if (rows.length > 0 && rows[0].cells[columnIndex]) {
        console.log(`🔍 [排查3] 排序前第一行列${columnIndex}数据: "${rows[0].cells[columnIndex].textContent.trim()}"`);
    }

    const collator = new Intl.Collator(['zh-CN', 'en-US'], { numeric: true });

    rows.sort((a, b) => {
        const aText = a.cells[columnIndex] ? a.cells[columnIndex].textContent.trim() : '';
        const bText = b.cells[columnIndex] ? b.cells[columnIndex].textContent.trim() : '';

        // 预处理，移除单位如'亿', '万', '%'
        const cleanA = aText.replace(/亿|万|%/g, '');
        const cleanB = bText.replace(/亿|万|%/g, '');
        
        const valA = isNaN(parseFloat(cleanA)) ? aText : parseFloat(cleanA);
        const valB = isNaN(parseFloat(cleanB)) ? bText : parseFloat(cleanB);

        let comparison = 0;
        if (typeof valA === 'number' && typeof valB === 'number') {
            comparison = valA - valB;
        } else {
            comparison = collator.compare(aText, bText);
        }

        return direction === 'asc' ? comparison : -comparison;
    });

    // 重新将排序后的行插入tbody
    console.log(`🔍 [排查4] 排序后准备插入行数: ${rows.length}`);

    rows.forEach((row, index) => {
        tbody.appendChild(row);
        // 检查前3行的数据
        if (index < 3 && row.cells[columnIndex]) {
            console.log(`🔍 [排查5] 排序后第${index+1}行列${columnIndex}数据: "${row.cells[columnIndex].textContent.trim()}"`);
        }
    });

    // 检查插入后的实际行数
    const finalRows = tbody.querySelectorAll('tr');
    console.log(`🔍 [排查6] 排序完成后实际行数: ${finalRows.length}`);

    // 排序完成，通知主页面进行焦点管理
    console.log(`🔍 [排序完成] 通知主页面处理焦点恢复`);

    // 触发自定义事件，让主页面处理焦点恢复
    const sortCompleteEvent = new CustomEvent('tableSortComplete', {
        detail: { columnIndex, direction, rowCount: finalRows.length }
    });
    document.dispatchEvent(sortCompleteEvent);

    // **关键集成点**: 通知分页管理器和K线图模块
    if (window.globalDatePaginationManager) {
        // 检查是否正在应用排序状态，避免循环调用
        if (!window.globalDatePaginationManager._isApplyingSort) {
            // 通知分页管理器排序状态变化
            window.globalDatePaginationManager.setSortState(columnIndex, direction);
            console.log('Table sorted. Pagination manager updated.');
        } else {
            console.log('Table sorted. Skipping pagination manager update (applying sort state).');
        }
    } else if (window.rebindAllFeatures) {
        // 如果没有分页管理器，直接重绑定所有功能
        console.log('Table sorted. Rebinding all features...');
        window.rebindAllFeatures();
    }
}

function updateHeaderStyles(headers, activeIndex, direction) {
    headers.forEach((header, index) => {
        // 移除旧的排序指示
        const indicator = header.querySelector('.sort-indicator');
        if (indicator) {
            indicator.remove();
        }
        // 为活动列添加新的排序指示
        if (index === activeIndex) {
            const newIndicator = document.createElement('span');
            newIndicator.className = 'sort-indicator';
            newIndicator.innerHTML = direction === 'asc' ? ' ↑' : ' ↓'; // 极简上下箭头
            header.appendChild(newIndicator);
        }
    });
}

// 全局初始化
document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('table.dataframe').forEach(makeTableSortable);
});
