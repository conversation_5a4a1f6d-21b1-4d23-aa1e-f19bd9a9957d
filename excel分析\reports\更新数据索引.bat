@echo off
chcp 65001 >nul
title 复盘数据索引更新工具

echo.
echo ========================================
echo 🚀 复盘数据索引自动更新工具
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python
    echo 💡 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 显示当前目录
echo 📁 当前目录：%CD%
echo.

:: 检查是否在正确的目录
if not exist "data" (
    if not exist "reports\data" (
        echo ❌ 错误：找不到data目录
        echo 💡 请确保在正确的目录下运行此脚本
        echo    预期目录结构：
        echo    ├── data\
        echo    ├── 复盘分析_精简版.html
        echo    └── 更新数据索引.bat
        echo.
        pause
        exit /b 1
    )
)

:: 运行Python脚本
echo 🔄 正在更新数据索引...
echo.
python update_data_index.py

:: 检查执行结果
if errorlevel 1 (
    echo.
    echo ❌ 数据索引更新失败
    echo 💡 请检查错误信息并重试
) else (
    echo.
    echo ✅ 数据索引更新成功！
    echo 💡 现在可以刷新网页查看最新数据
)

echo.
echo ========================================
echo 按任意键退出...
pause >nul
