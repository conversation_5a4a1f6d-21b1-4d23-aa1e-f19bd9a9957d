#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于历史成功经验的特殊文件修复工具 - 单文件修复工具
应用2025年7月17日文件修复的成功方法

这是单文件修复工具，用于处理特定的特殊编码文件：
- 基于历史成功经验开发
- 专门处理单个文件的编码问题
- 包含详细的日志和验证机制

注意：对于批量处理，建议使用 enhanced_auto_fixer.py

使用方法：
1. 修改文件路径（第18-19行）
2. 运行：python fix_special_file_historical.py

状态：辅助工具，用于单文件处理
主要工具：enhanced_auto_fixer.py
"""

import pandas as pd
import json
from pathlib import Path
import chardet
import logging

def setup_logger():
    """设置日志"""
    logger = logging.getLogger('HistoricalFixer')
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def fix_special_file_historical():
    """基于历史成功经验修复特殊文件"""
    
    logger = setup_logger()
    
    file_path = Path("../复盘数据/临时条件股_20250715_1.xls")
    output_path = Path("reports/data/date_2025-07-15.json")
    
    logger.info(f"正在使用历史成功方法修复文件: {file_path}")
    
    # 方法1: 复用2025年7月17日的成功方法
    logger.info("=== 方法1: 复用7月17日成功方法 ===")
    
    # 历史成功的编码顺序（基于文档记录）
    historical_encodings = ['gb2312', 'gbk', 'gb18030', 'utf-8']
    
    for encoding in historical_encodings:
        try:
            logger.info(f"尝试编码: {encoding}")
            
            # 使用历史成功的容错模式
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                lines = f.readlines()
            
            logger.info(f"文件总行数: {len(lines)}")
            
            # 历史成功的智能表头查找方法
            header_line_idx = -1
            for i, line in enumerate(lines):
                if '代码' in line and '名称' in line:
                    header_line_idx = i
                    logger.info(f"找到表头行: {i+1}")
                    logger.info(f"表头内容: {line.strip()[:100]}")
                    break
            
            if header_line_idx == -1:
                logger.warning("未找到表头行，尝试下一个编码")
                continue
            
            # 历史成功的手动TSV解析方法
            header_line = lines[header_line_idx].strip()
            headers = [col.strip() for col in header_line.split('\t')]
            logger.info(f"列名数量: {len(headers)}")
            logger.info(f"前5个列名: {headers[:5]}")
            
            # 验证列名是否正确（历史验证方法）
            has_chinese = any('\u4e00' <= char <= '\u9fff' for col in headers for char in str(col))
            if not has_chinese:
                logger.warning(f"列名可能有乱码，尝试下一个编码")
                continue
            
            # 历史成功的数据解析方法
            data_rows = []
            for i in range(header_line_idx + 1, len(lines)):
                line = lines[i].strip()
                if line:  # 跳过空行
                    values = line.split('\t')
                    if len(values) >= len(headers):
                        # 创建数据行字典（历史方法）
                        row_dict = {}
                        for j, header in enumerate(headers):
                            if j < len(values):
                                value = values[j].strip()
                                # 历史成功的特殊值处理
                                if value == 'nan' or value == '':
                                    row_dict[header] = None
                                else:
                                    # 历史成功的类型转换
                                    try:
                                        if '.' in value:
                                            row_dict[header] = float(value)
                                        else:
                                            row_dict[header] = int(value)
                                    except:
                                        row_dict[header] = value
                            else:
                                row_dict[header] = None
                        data_rows.append(row_dict)
            
            logger.info(f"成功解析数据行: {len(data_rows)}")
            
            if len(data_rows) > 0:
                # 验证第一行数据的中文显示（历史验证方法）
                first_row = data_rows[0]
                first_row_values = [str(val) for val in first_row.values() if val is not None]
                first_row_chinese = any('\u4e00' <= char <= '\u9fff' for val in first_row_values for char in val)
                
                logger.info(f"第一行数据示例: {dict(list(first_row.items())[:3])}")
                logger.info(f"第一行包含中文: {first_row_chinese}")
                
                if first_row_chinese or '代码' in first_row:
                    # 历史成功的保存方法
                    output_path.parent.mkdir(exist_ok=True)
                    with open(output_path, 'w', encoding='utf-8') as f:
                        json.dump(data_rows, f, ensure_ascii=False, indent=2)
                    
                    logger.info(f"✅ 文件修复成功！")
                    logger.info(f"输出文件: {output_path}")
                    logger.info(f"数据行数: {len(data_rows)}")
                    logger.info(f"列数: {len(headers)}")
                    logger.info(f"使用编码: {encoding}")
                    
                    # 显示第一行数据示例
                    if data_rows:
                        logger.info("第一行数据示例:")
                        first_row = data_rows[0]
                        for i, (key, value) in enumerate(first_row.items()):
                            if i < 5:
                                logger.info(f"  {key}: {value}")
                    
                    return True
                else:
                    logger.warning("数据中文显示异常，尝试下一个编码")
                    continue
            else:
                logger.warning("未找到有效数据行")
                continue
                
        except Exception as e:
            logger.warning(f"编码 {encoding} 失败: {str(e)[:100]}")
            continue
    
    # 方法2: 使用历史文档中的备用方法
    logger.info("=== 方法2: 历史备用方法 ===")
    
    try:
        # 基于历史文档的编码检测方法
        with open(file_path, 'rb') as f:
            raw_data = f.read(10000)
            result = chardet.detect(raw_data)
            detected_encoding = result.get('encoding', 'gbk')
            confidence = result.get('confidence', 0)
            
        logger.info(f"chardet检测: {detected_encoding} (置信度: {confidence:.2f})")
        
        # 历史文档中的编码优先级处理
        if detected_encoding and detected_encoding.lower() in ['gb2312', 'gbk', 'gb18030']:
            preferred_encoding = detected_encoding.lower()
        else:
            preferred_encoding = 'gbk'
        
        logger.info(f"使用优先编码: {preferred_encoding}")
        
        # 尝试pandas方法（历史备用方案）
        for skip_rows in [1, 2, 3]:
            try:
                logger.info(f"尝试跳过行数: {skip_rows}")
                
                df = pd.read_csv(
                    file_path, 
                    encoding=preferred_encoding, 
                    sep='\t',
                    skiprows=skip_rows, 
                    low_memory=False, 
                    dtype=str,
                    on_bad_lines='skip',
                    engine='python'  # 使用python引擎增强容错性
                )
                
                if not df.empty and len(df.columns) > 5:
                    # 检查列名
                    column_names = list(df.columns)
                    has_chinese = any('\u4e00' <= char <= '\u9fff' for col in column_names for char in str(col))
                    
                    if has_chinese:
                        logger.info(f"✅ pandas方法成功，跳过行数: {skip_rows}")
                        
                        # 清理数据（历史方法）
                        df = df.dropna(axis=1, how='all')
                        df.columns = [col.strip() for col in df.columns]
                        
                        # 转换为字典列表
                        data_rows = df.to_dict('records')
                        
                        # 保存
                        output_path.parent.mkdir(exist_ok=True)
                        with open(output_path, 'w', encoding='utf-8') as f:
                            json.dump(data_rows, f, ensure_ascii=False, indent=2)
                        
                        logger.info(f"✅ pandas方法修复成功！")
                        logger.info(f"数据行数: {len(data_rows)}")
                        return True
                        
            except Exception as e:
                logger.warning(f"pandas方法失败，跳过行数 {skip_rows}: {str(e)[:50]}")
                continue
                
    except Exception as e:
        logger.error(f"备用方法失败: {e}")
    
    logger.error("❌ 所有历史方法都失败了")
    return False

if __name__ == "__main__":
    success = fix_special_file_historical()
    if success:
        print("\n🎉 基于历史经验的修复成功！")
    else:
        print("\n❌ 修复失败，建议重新获取文件")
