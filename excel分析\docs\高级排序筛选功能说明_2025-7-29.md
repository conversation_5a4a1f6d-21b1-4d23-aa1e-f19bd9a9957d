# 高级排序筛选功能说明

## 功能概述

复盘分析系统新增了高级排序筛选功能，支持"字段名 倒序前N"或"字段名 正序前N"的筛选语句，可以快速获取按指定字段排序的前N条记录。

## 新增语法

### 排序筛选语法
```
字段名 倒序前N    # 按字段降序排列，取前N条
字段名 正序前N    # 按字段升序排列，取前N条
```

### 语法示例
```
振幅 倒序前10        # 按振幅降序，取前10条记录
成交量 正序前20      # 按成交量升序，取前20条记录
涨幅% 倒序前15       # 按涨幅%降序，取前15条记录
量比 倒序前5         # 按量比降序，取前5条记录
```

## 组合使用

### 与现有筛选条件组合
```
涨幅% > 5 AND 振幅 倒序前10     # 先筛选涨幅>5%，再按振幅排序取前10
量比 > 1.5 AND 成交量 倒序前20  # 先筛选量比>1.5，再按成交量排序取前20
```

### 复杂组合示例
```
(涨幅% > 3 OR 量比 > 2) AND 振幅 倒序前15
名称 CONTAINS "银行" AND 成交量 倒序前10
```

## 使用方法

### 1. 通过输入框
在筛选输入框中直接输入排序语句：
- 点击页面右上角的"🔍 筛选"按钮
- 在输入框中输入：`振幅 倒序前10`
- 点击"应用"按钮或按回车键

### 2. 通过预设按钮
系统提供了常用的排序预设按钮：
- **振幅前10**：`振幅 倒序前10`
- **成交量前15**：`成交量 倒序前15`
- **涨幅前20**：`涨幅% 倒序前20`

### 3. 查看语法帮助
- 点击筛选面板右上角的"❓"按钮
- 查看完整的语法说明和示例

## 支持的字段

排序筛选支持数据表中的所有数值字段，包括但不限于：

### 基础数据字段
- 涨幅%、收盘、开盘、最高、最低
- 成交量、成交额、总金额
- 振幅、量比、换手率

### 技术指标字段
- 次日量比、次日涨幅%
- HUPD33、HUPD55、HUPD89
- 各种自定义技术指标

## 实现原理

### 处理流程
1. **语法解析**：识别排序语句格式
2. **条件分离**：将排序条件与普通筛选条件分离
3. **先筛选后排序**：先应用普通筛选条件，再对结果进行排序
4. **取前N条**：按指定数量返回排序结果

### 排序逻辑
- **倒序前N**：按数值从大到小排序，取前N条
- **正序前N**：按数值从小到大排序，取前N条
- **数值处理**：自动将字段值转换为数值进行比较

## 使用场景

### 1. 寻找极值股票
```
振幅 倒序前10      # 找出振幅最大的10只股票
成交量 倒序前20    # 找出成交量最大的20只股票
涨幅% 倒序前15     # 找出涨幅最大的15只股票
```

### 2. 组合筛选分析
```
涨幅% > 5 AND 振幅 倒序前10        # 涨幅>5%中振幅最大的10只
量比 > 2 AND 成交量 倒序前15       # 量比>2中成交量最大的15只
```

### 3. 特定条件排序
```
名称 CONTAINS "科技" AND 涨幅% 倒序前10    # 科技股中涨幅最大的10只
总金额 > 1000000 AND 振幅 倒序前20        # 大盘股中振幅最大的20只
```

## 注意事项

### 1. 字段名称匹配
- 字段名必须与数据表头完全匹配
- 支持中文字段名（如"涨幅%"、"成交量"）
- 区分大小写和特殊字符

### 2. 数值处理
- 非数值字段将被转换为0进行排序
- 空值或无效值按0处理
- 百分比字段（如"涨幅%"）按数值排序

### 3. 性能考虑
- 排序操作在筛选后的数据上进行，性能较好
- 建议先用筛选条件缩小数据范围，再进行排序
- 大数据集建议限制N的值

## 错误处理

### 常见错误及解决方法

1. **字段名不存在**
   - 错误：`未知字段 倒序前10`
   - 解决：检查字段名是否与表头完全匹配

2. **语法格式错误**
   - 错误：`振幅倒序前10`（缺少空格）
   - 解决：确保格式为"字段名 倒序前N"

3. **数量参数错误**
   - 错误：`振幅 倒序前abc`
   - 解决：确保N为正整数

## 更新日志

### v1.0 (2025-7-29)
- ✅ 实现基础排序筛选语法
- ✅ 支持倒序前N和正序前N
- ✅ 与现有筛选条件完美兼容
- ✅ 添加预设排序按钮
- ✅ 集成语法帮助面板
- ✅ 完善错误处理和用户提示

---

**开发者：** Augment Agent  
**更新日期：** 2025-07-29  
**版本：** v1.0
