#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
功能：自动安装所需的Python依赖包
作者：AI Assistant
日期：2025-07-23
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"📦 正在安装 {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print(f"✅ {package} 安装成功")
            return True
        else:
            print(f"❌ {package} 安装失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 安装 {package} 时出错: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("🚀 复盘系统依赖检查和安装")
    print("=" * 50)
    
    # 需要的包列表
    required_packages = [
        ("watchdog", "文件监控功能"),
        ("pandas", "数据处理功能"),
        ("openpyxl", "Excel文件读取"),
        ("xlrd", "旧版Excel文件支持"),
        ("chardet", "编码检测"),
    ]
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        return False
    
    # 检查pip
    try:
        import pip
        print("✅ pip 可用")
    except ImportError:
        print("❌ pip 不可用，请先安装pip")
        return False
    
    # 检查和安装依赖
    all_success = True
    
    for package, description in required_packages:
        print(f"\n📋 检查 {package} ({description})...")
        
        if check_package(package):
            print(f"✅ {package} 已安装")
        else:
            print(f"⚠️ {package} 未安装，开始安装...")
            if not install_package(package):
                all_success = False
    
    print("\n" + "=" * 50)
    
    if all_success:
        print("✅ 所有依赖安装完成！")
        print("\n🎉 现在可以运行以下命令启动监控:")
        print("   python web_monitor.py")
        print("\n或者只启动数据监控:")
        print("   python data_monitor.py")
        return True
    else:
        print("❌ 部分依赖安装失败")
        print("\n💡 请尝试手动安装:")
        for package, _ in required_packages:
            print(f"   pip install {package}")
        return False

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
