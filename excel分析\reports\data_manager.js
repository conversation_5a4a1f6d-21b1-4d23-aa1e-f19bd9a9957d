/**
 * 数据管理器
 * 负责数据的加载、缓存和提供统一的数据访问接口
 * 保持与现有JS模块的完全兼容性
 * 
 * @version 1.0.0
 * @date 2025-07-19
 */

class DataManager {
    constructor() {
        this.data = null;
        this.isLoaded = false;
        this.loadingPromise = null;
        this.cache = new Map();
        
        // 兼容性接口，确保现有代码正常工作
        this.compatibilityMode = true;
        
        console.log('📊 数据管理器初始化');
    }
    
    /**
     * 加载数据（支持多种数据源）
     */
    async loadData(source = 'embedded') {
        if (this.isLoaded) {
            return this.data;
        }
        
        if (this.loadingPromise) {
            return this.loadingPromise;
        }
        
        this.loadingPromise = this._loadDataInternal(source);
        return this.loadingPromise;
    }
    
    async _loadDataInternal(source) {
        console.log(`🔄 开始加载数据，来源: ${source}`);
        
        try {
            let data;
            
            switch (source) {
                case 'embedded':
                    // 从嵌入的数据加载
                    data = await this._loadEmbeddedData();
                    break;
                case 'json':
                    // 从JSON文件加载
                    data = await this._loadJsonData();
                    break;
                case 'dom':
                    // 从现有DOM提取（兼容模式）
                    data = await this._extractFromDOM();
                    break;
                default:
                    throw new Error(`不支持的数据源: ${source}`);
            }
            
            this.data = data;
            this.isLoaded = true;
            
            console.log(`✅ 数据加载完成: ${data.rows.length}行 × ${data.headers.length}列`);
            return data;
            
        } catch (error) {
            console.error('❌ 数据加载失败:', error);
            // 降级到DOM提取模式
            if (source !== 'dom') {
                console.log('🔄 降级到DOM提取模式...');
                return this._loadDataInternal('dom');
            }
            throw error;
        }
    }
    
    /**
     * 从嵌入数据加载
     */
    async _loadEmbeddedData() {
        if (window.EMBEDDED_TABLE_DATA) {
            return window.EMBEDDED_TABLE_DATA;
        }
        throw new Error('嵌入数据不存在');
    }
    
    /**
     * 从JSON文件加载
     */
    async _loadJsonData() {
        const response = await fetch('table_data.json');
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.json();
    }
    
    /**
     * 从现有DOM提取数据（兼容模式）
     */
    async _extractFromDOM() {
        console.log('🔄 从DOM提取数据（兼容模式）...');
        
        const table = document.querySelector('table.dataframe');
        if (!table) {
            throw new Error('未找到数据表格');
        }
        
        // 提取表头
        const headers = [];
        const thead = table.querySelector('thead');
        if (thead) {
            const headerRow = thead.querySelector('tr');
            if (headerRow) {
                headerRow.querySelectorAll('th').forEach(th => {
                    headers.push(th.textContent.trim());
                });
            }
        }
        
        // 提取数据行
        const rows = [];
        const tbody = table.querySelector('tbody');
        if (tbody) {
            tbody.querySelectorAll('tr').forEach(tr => {
                const cells = [];
                tr.querySelectorAll('td').forEach(td => {
                    cells.push(td.textContent.trim());
                });
                if (cells.length === headers.length) {
                    rows.push(cells);
                }
            });
        }
        
        return {
            headers,
            rows,
            metadata: {
                total_rows: rows.length,
                total_columns: headers.length,
                source: 'dom_extraction',
                extracted_at: new Date().toISOString()
            }
        };
    }
    
    /**
     * 获取数据（同步接口，用于兼容现有代码）
     */
    getData() {
        if (!this.isLoaded) {
            console.warn('⚠️ 数据尚未加载完成');
            return null;
        }
        return this.data;
    }
    
    /**
     * 获取表头
     */
    getHeaders() {
        const data = this.getData();
        return data ? data.headers : [];
    }
    
    /**
     * 获取所有行数据
     */
    getRows() {
        const data = this.getData();
        return data ? data.rows : [];
    }
    
    /**
     * 获取分页数据
     */
    getPageData(page, pageSize) {
        const rows = this.getRows();
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        
        return {
            data: rows.slice(start, end),
            totalRows: rows.length,
            totalPages: Math.ceil(rows.length / pageSize),
            currentPage: page,
            pageSize: pageSize
        };
    }
    
    /**
     * 搜索数据
     */
    searchData(searchTerm) {
        const rows = this.getRows();
        if (!searchTerm) {
            return rows;
        }
        
        const term = searchTerm.toLowerCase();
        return rows.filter(row => 
            row.some(cell => cell.toLowerCase().includes(term))
        );
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        const data = this.getData();
        if (!data) {
            return null;
        }
        
        return {
            totalRows: data.rows.length,
            totalColumns: data.headers.length,
            dataSize: JSON.stringify(data).length,
            isLoaded: this.isLoaded,
            source: data.metadata?.source || 'unknown'
        };
    }
    
    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
        console.log('🗑️ 数据缓存已清除');
    }
    
    /**
     * 重新加载数据
     */
    async reload(source) {
        this.isLoaded = false;
        this.loadingPromise = null;
        this.data = null;
        this.clearCache();
        
        return this.loadData(source);
    }
}

/**
 * 异步表格渲染器
 * 负责高性能的表格渲染，保持与现有功能的兼容性
 */
class AsyncTableRenderer {
    constructor(dataManager, tableElement) {
        this.dataManager = dataManager;
        this.table = tableElement;
        this.tbody = tableElement.querySelector('tbody');
        this.thead = tableElement.querySelector('thead');
        
        this.batchSize = 50;
        this.isRendering = false;
        this.renderQueue = [];
        
        // 保存原始表格内容（用于兼容性）
        this.originalContent = null;
        
        console.log('🎨 异步表格渲染器初始化');
    }
    
    /**
     * 渲染表格（保持原有DOM结构）
     */
    async renderTable(data = null) {
        if (this.isRendering) {
            console.log('⚠️ 渲染正在进行中，跳过重复请求');
            return;
        }
        
        this.isRendering = true;
        
        try {
            // 获取数据
            const tableData = data || await this.dataManager.loadData();
            
            // 备份原始内容（首次渲染时）
            if (!this.originalContent) {
                this.originalContent = {
                    thead: this.thead ? this.thead.innerHTML : '',
                    tbody: this.tbody ? this.tbody.innerHTML : ''
                };
            }
            
            // 渲染表头（如果需要）
            if (this.thead && this.thead.children.length === 0) {
                this.renderHeaders(tableData.headers);
            }
            
            // 异步渲染表体
            await this.renderBodyAsync(tableData.rows);
            
            // 触发兼容性事件，通知其他模块表格已更新
            this.triggerCompatibilityEvents();
            
            console.log('✅ 表格渲染完成');
            
        } catch (error) {
            console.error('❌ 表格渲染失败:', error);
            // 恢复原始内容
            this.restoreOriginalContent();
        } finally {
            this.isRendering = false;
        }
    }
    
    /**
     * 渲染表头
     */
    renderHeaders(headers) {
        if (!this.thead) return;
        
        const headerRow = document.createElement('tr');
        headers.forEach(header => {
            const th = document.createElement('th');
            th.textContent = header;
            th.className = 'sortable'; // 保持排序功能兼容性
            headerRow.appendChild(th);
        });
        
        this.thead.innerHTML = '';
        this.thead.appendChild(headerRow);
    }
    
    /**
     * 异步渲染表体
     */
    async renderBodyAsync(rows) {
        if (!this.tbody) return;
        
        // 清空现有内容
        this.tbody.innerHTML = '';
        
        // 分批渲染
        const totalBatches = Math.ceil(rows.length / this.batchSize);
        let currentBatch = 0;
        
        return new Promise((resolve) => {
            const renderBatch = () => {
                const start = currentBatch * this.batchSize;
                const end = Math.min(start + this.batchSize, rows.length);
                
                // 创建文档片段以提高性能
                const fragment = document.createDocumentFragment();
                
                for (let i = start; i < end; i++) {
                    const row = document.createElement('tr');
                    row.className = 'data-row'; // 保持CSS兼容性
                    
                    rows[i].forEach(cell => {
                        const td = document.createElement('td');
                        td.textContent = cell;
                        row.appendChild(td);
                    });
                    
                    fragment.appendChild(row);
                }
                
                this.tbody.appendChild(fragment);
                
                // 更新进度
                const progress = Math.round((currentBatch + 1) / totalBatches * 100);
                this.updateRenderProgress(progress, end, rows.length);
                
                currentBatch++;
                
                if (currentBatch < totalBatches) {
                    // 使用requestAnimationFrame确保不阻塞UI
                    requestAnimationFrame(renderBatch);
                } else {
                    resolve();
                }
            };
            
            renderBatch();
        });
    }
    
    /**
     * 更新渲染进度
     */
    updateRenderProgress(progress, current, total) {
        // 发送自定义事件，其他模块可以监听
        const event = new CustomEvent('tableRenderProgress', {
            detail: { progress, current, total }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * 触发兼容性事件
     */
    triggerCompatibilityEvents() {
        // 触发表格更新事件，确保其他模块知道表格已更新
        const events = [
            'tableUpdated',
            'dataLoaded',
            'renderComplete'
        ];
        
        events.forEach(eventName => {
            const event = new CustomEvent(eventName, {
                detail: { 
                    table: this.table,
                    dataManager: this.dataManager
                }
            });
            document.dispatchEvent(event);
        });
        
        // 确保排序功能正常工作
        if (typeof makeTableSortable === 'function') {
            makeTableSortable(this.table);
        }
    }
    
    /**
     * 恢复原始内容
     */
    restoreOriginalContent() {
        if (this.originalContent) {
            if (this.thead) {
                this.thead.innerHTML = this.originalContent.thead;
            }
            if (this.tbody) {
                this.tbody.innerHTML = this.originalContent.tbody;
            }
            console.log('🔄 已恢复原始表格内容');
        }
    }
    
    /**
     * 获取当前渲染状态
     */
    getRenderStatus() {
        return {
            isRendering: this.isRendering,
            batchSize: this.batchSize,
            hasOriginalContent: !!this.originalContent
        };
    }
}

// 全局实例，确保兼容性
window.dataManager = null;
window.asyncTableRenderer = null;

/**
 * 初始化数据管理系统
 */
async function initializeDataManager() {
    console.log('🚀 初始化数据管理系统...');
    
    try {
        // 创建数据管理器
        window.dataManager = new DataManager();
        
        // 查找表格元素
        const table = document.querySelector('table.dataframe');
        if (table) {
            // 创建异步渲染器
            window.asyncTableRenderer = new AsyncTableRenderer(window.dataManager, table);
            
            // 开始加载和渲染数据
            await window.asyncTableRenderer.renderTable();
            
            console.log('✅ 数据管理系统初始化完成');
            return true;
        } else {
            console.warn('⚠️ 未找到数据表格，跳过渲染器初始化');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 数据管理系统初始化失败:', error);
        return false;
    }
}

// 导出给其他模块使用
window.initializeDataManager = initializeDataManager;
