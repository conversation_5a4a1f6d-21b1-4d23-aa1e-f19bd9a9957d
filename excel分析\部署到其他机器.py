#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署到其他机器的辅助脚本
功能：检查部署环境，创建必要目录，验证系统完整性
作者：AI Assistant
日期：2025-07-29
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("💡 需要Python 3.7或更高版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_required_files():
    """检查必要文件是否存在"""
    required_files = [
        "excel_processor.py",
        "web_monitor.py", 
        "complete_monitor.py",
        "data_monitor.py",
        "启动监控服务器.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 正在安装依赖...")
    try:
        # 检查requirements.txt是否存在
        if not Path("requirements.txt").exists():
            print("⚠️ requirements.txt不存在，创建基本依赖文件")
            with open("requirements.txt", "w", encoding="utf-8") as f:
                f.write("pandas>=1.3.0\n")
                f.write("watchdog>=2.1.0\n")
                f.write("openpyxl>=3.0.0\n")
        
        # 安装依赖
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True, text=True)
        
        print("✅ 依赖安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_directories():
    """创建必要目录"""
    directories = [
        "../复盘数据",
        "reports",
        "reports/data"
    ]

    for dir_path in directories:
        path = Path(dir_path)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {path.absolute()}")
        else:
            print(f"📂 目录已存在: {path.absolute()}")

    return True

def test_import():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    # 确保当前目录在Python路径中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    try:
        from excel_processor import TongDaXinProcessor
        print("✅ excel_processor模块导入成功")
        
        from data_monitor import DataIndexManager
        print("✅ data_monitor模块导入成功")
        
        from complete_monitor import CompleteMonitor
        print("✅ complete_monitor模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 通达信复盘系统部署检查器")
    print("=" * 50)
    
    # 获取当前目录信息
    current_dir = Path.cwd()
    script_dir = Path(__file__).parent.absolute()
    
    print(f"📂 当前工作目录: {current_dir}")
    print(f"📂 脚本所在目录: {script_dir}")
    
    # 切换到脚本目录
    os.chdir(script_dir)
    print(f"📂 切换到脚本目录: {Path.cwd()}")
    
    # 执行检查步骤
    checks = [
        ("Python版本检查", check_python_version),
        ("必要文件检查", check_required_files),
        ("安装依赖", install_dependencies),
        ("创建目录", create_directories),
        ("模块导入测试", test_import)
    ]
    
    failed_checks = []
    
    for check_name, check_func in checks:
        print(f"\n🔍 {check_name}...")
        try:
            if not check_func():
                failed_checks.append(check_name)
        except Exception as e:
            print(f"❌ {check_name}失败: {e}")
            failed_checks.append(check_name)
    
    print("\n" + "=" * 50)
    if failed_checks:
        print(f"❌ 部署检查失败，以下项目需要修复:")
        for check in failed_checks:
            print(f"   - {check}")
        print("\n💡 请修复上述问题后重新运行此脚本")
    else:
        print("✅ 所有检查通过！系统已准备就绪")
        print("\n🚀 现在可以运行: python 启动监控服务器.py")
        
        # 询问是否立即启动
        start_now = input("\n是否立即启动监控服务器？(y/n): ").lower().strip()
        if start_now == 'y':
            try:
                subprocess.run([sys.executable, "启动监控服务器.py"], check=True)
            except subprocess.CalledProcessError as e:
                print(f"❌ 启动失败: {e}")
            except KeyboardInterrupt:
                print("\n🛑 用户中断启动")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
